(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-vendors"],{"1fb5":function(t,e,n){"use strict";e.byteLength=l,e.toByteArray=f,e.fromByteArray=y;for(var r=[],i=[],s="undefined"!==typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/",a=0,c=o.length;a<c;++a)r[a]=o[a],i[o.charCodeAt(a)]=a;function u(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");-1===n&&(n=e);var r=n===e?0:4-n%4;return[n,r]}function l(t){var e=u(t),n=e[0],r=e[1];return 3*(n+r)/4-r}function h(t,e,n){return 3*(e+n)/4-n}function f(t){var e,n,r=u(t),o=r[0],a=r[1],c=new s(h(t,o,a)),l=0,f=a>0?o-4:o;for(n=0;n<f;n+=4)e=i[t.charCodeAt(n)]<<18|i[t.charCodeAt(n+1)]<<12|i[t.charCodeAt(n+2)]<<6|i[t.charCodeAt(n+3)],c[l++]=e>>16&255,c[l++]=e>>8&255,c[l++]=255&e;return 2===a&&(e=i[t.charCodeAt(n)]<<2|i[t.charCodeAt(n+1)]>>4,c[l++]=255&e),1===a&&(e=i[t.charCodeAt(n)]<<10|i[t.charCodeAt(n+1)]<<4|i[t.charCodeAt(n+2)]>>2,c[l++]=e>>8&255,c[l++]=255&e),c}function d(t){return r[t>>18&63]+r[t>>12&63]+r[t>>6&63]+r[63&t]}function p(t,e,n){for(var r,i=[],s=e;s<n;s+=3)r=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),i.push(d(r));return i.join("")}function y(t){for(var e,n=t.length,i=n%3,s=[],o=16383,a=0,c=n-i;a<c;a+=o)s.push(p(t,a,a+o>c?c:a+o));return 1===i?(e=t[n-1],s.push(r[e>>2]+r[e<<4&63]+"==")):2===i&&(e=(t[n-2]<<8)+t[n-1],s.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"=")),s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},"320c":function(t,e,n){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var r=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable;function o(t){if(null===t||void 0===t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}function a(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},n=0;n<10;n++)e["_"+String.fromCharCode(n)]=n;var r=Object.getOwnPropertyNames(e).map((function(t){return e[t]}));if("**********"!==r.join(""))return!1;var i={};return"abcdefghijklmnopqrst".split("").forEach((function(t){i[t]=t})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},i)).join("")}catch(s){return!1}}t.exports=a()?Object.assign:function(t,e){for(var n,a,c=o(t),u=1;u<arguments.length;u++){for(var l in n=Object(arguments[u]),n)i.call(n,l)&&(c[l]=n[l]);if(r){a=r(n);for(var h=0;h<a.length;h++)s.call(n,a[h])&&(c[a[h]]=n[a[h]])}}return c}},"3eea":function(t,e,n){"use strict";n.r(e),n.d(e,"memcpy",(function(){return u})),n.d(e,"joinUint8Arrays",(function(){return l})),n.d(e,"toArrayBufferView",(function(){return h})),n.d(e,"toInt8Array",(function(){return f})),n.d(e,"toInt16Array",(function(){return d})),n.d(e,"toInt32Array",(function(){return p})),n.d(e,"toBigInt64Array",(function(){return y})),n.d(e,"toUint8Array",(function(){return b})),n.d(e,"toUint16Array",(function(){return g})),n.d(e,"toUint32Array",(function(){return m})),n.d(e,"toBigUint64Array",(function(){return _})),n.d(e,"toFloat32Array",(function(){return v})),n.d(e,"toFloat64Array",(function(){return w})),n.d(e,"toUint8ClampedArray",(function(){return O})),n.d(e,"toArrayBufferViewIterator",(function(){return S})),n.d(e,"toInt8ArrayIterator",(function(){return A})),n.d(e,"toInt16ArrayIterator",(function(){return T})),n.d(e,"toInt32ArrayIterator",(function(){return B})),n.d(e,"toUint8ArrayIterator",(function(){return x})),n.d(e,"toUint16ArrayIterator",(function(){return j})),n.d(e,"toUint32ArrayIterator",(function(){return E})),n.d(e,"toFloat32ArrayIterator",(function(){return D})),n.d(e,"toFloat64ArrayIterator",(function(){return L})),n.d(e,"toUint8ClampedArrayIterator",(function(){return F})),n.d(e,"toArrayBufferViewAsyncIterator",(function(){return U})),n.d(e,"toInt8ArrayAsyncIterator",(function(){return M})),n.d(e,"toInt16ArrayAsyncIterator",(function(){return C})),n.d(e,"toInt32ArrayAsyncIterator",(function(){return N})),n.d(e,"toUint8ArrayAsyncIterator",(function(){return k})),n.d(e,"toUint16ArrayAsyncIterator",(function(){return R})),n.d(e,"toUint32ArrayAsyncIterator",(function(){return V})),n.d(e,"toFloat32ArrayAsyncIterator",(function(){return P})),n.d(e,"toFloat64ArrayAsyncIterator",(function(){return z})),n.d(e,"toUint8ClampedArrayAsyncIterator",(function(){return $})),n.d(e,"rebaseValueOffsets",(function(){return Y})),n.d(e,"compareArrayLike",(function(){return W}));var r=n("a6b2"),i=n("5217"),s=n("841f"),o=r["a"].ByteBuffer;const a="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;function c(t){let e,n,r,i,s=t[0]?[t[0]]:[];for(let o,a,c=0,u=0,l=t.length;++c<l;)o=s[u],a=t[c],!o||!a||o.buffer!==a.buffer||a.byteOffset<o.byteOffset?a&&(s[++u]=a):(({byteOffset:e,byteLength:r}=o),({byteOffset:n,byteLength:i}=a),e+r<n||n+i<e?a&&(s[++u]=a):s[u]=new Uint8Array(o.buffer,e,n-e+i));return s}function u(t,e,n=0,r=e.byteLength){const i=t.byteLength,s=new Uint8Array(t.buffer,t.byteOffset,i),o=new Uint8Array(e.buffer,e.byteOffset,Math.min(r,i));return s.set(o,n),t}function l(t,e){let n,r,i,s=c(t),o=s.reduce((t,e)=>t+e.byteLength,0),a=0,l=-1,h=Math.min(e||1/0,o);for(let c=s.length;++l<c;){if(n=s[l],r=n.subarray(0,Math.min(n.length,h-a)),h<=a+r.length){r.length<n.length?s[l]=n.subarray(r.length):r.length===n.length&&l++,i?u(i,r,a):i=r;break}u(i||(i=new Uint8Array(h)),r,a),a+=r.length}return[i||new Uint8Array(0),s.slice(l),o-(i?i.byteLength:0)]}function h(t,e){let n=Object(s["j"])(e)?e.value:e;return n instanceof t?t===Uint8Array?new t(n.buffer,n.byteOffset,n.byteLength):n:n?("string"===typeof n&&(n=Object(i["b"])(n)),n instanceof ArrayBuffer||n instanceof a?new t(n):n instanceof o?h(t,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new t(0):new t(n.buffer,n.byteOffset,n.byteLength/t.BYTES_PER_ELEMENT):t.from(n)):new t(0)}const f=t=>h(Int8Array,t),d=t=>h(Int16Array,t),p=t=>h(Int32Array,t),y=t=>h(s["b"],t),b=t=>h(Uint8Array,t),g=t=>h(Uint16Array,t),m=t=>h(Uint32Array,t),_=t=>h(s["d"],t),v=t=>h(Float32Array,t),w=t=>h(Float64Array,t),O=t=>h(Uint8ClampedArray,t),I=t=>(t.next(),t);function*S(t,e){const n=function*(t){yield t},r="string"===typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof a?n(e):Object(s["i"])(e)?e:n(e);yield*I(function*(e){let n=null;do{n=e.next(yield h(t,n))}while(!n.done)}(r[Symbol.iterator]()))}const A=t=>S(Int8Array,t),T=t=>S(Int16Array,t),B=t=>S(Int32Array,t),x=t=>S(Uint8Array,t),j=t=>S(Uint16Array,t),E=t=>S(Uint32Array,t),D=t=>S(Float32Array,t),L=t=>S(Float64Array,t),F=t=>S(Uint8ClampedArray,t);async function*U(t,e){if(Object(s["l"])(e))return yield*U(t,await e);const n=async function*(t){yield await t},r=async function*(t){yield*I(function*(t){let e=null;do{e=t.next(yield e&&e.value)}while(!e.done)}(t[Symbol.iterator]()))},i="string"===typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof a?n(e):Object(s["i"])(e)?r(e):Object(s["f"])(e)?e:n(e);yield*I(async function*(e){let n=null;do{n=await e.next(yield h(t,n))}while(!n.done)}(i[Symbol.asyncIterator]()))}const M=t=>U(Int8Array,t),C=t=>U(Int16Array,t),N=t=>U(Int32Array,t),k=t=>U(Uint8Array,t),R=t=>U(Uint16Array,t),V=t=>U(Uint32Array,t),P=t=>U(Float32Array,t),z=t=>U(Float64Array,t),$=t=>U(Uint8ClampedArray,t);function Y(t,e,n){if(0!==t){n=n.slice(0,e+1);for(let r=-1;++r<=e;)n[r]+=t}return n}function W(t,e){let n=0,r=t.length;if(r!==e.length)return!1;if(r>0)do{if(t[n]!==e[n])return!1}while(++n<r);return!0}},"4cec":function(t,e,n){"use strict";t.exports=n("a93d")},5217:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return c}));var r=n("3eea"),i=n("b4b6");const s="function"===typeof t?t:null,o="function"===typeof TextDecoder&&"function"===typeof TextEncoder,a=(t=>{if(o||!s){const e=new t("utf-8");return t=>e.decode(t)}return t=>{const{buffer:e,byteOffset:n,length:i}=Object(r["toUint8Array"])(t);return s.from(e,n,i).toString()}})("undefined"!==typeof TextDecoder?TextDecoder:i["a"]),c=(t=>{if(o||!s){const e=new t;return t=>e.encode(t)}return(t="")=>Object(r["toUint8Array"])(s.from(t,"utf8"))})("undefined"!==typeof TextEncoder?TextEncoder:i["b"])}).call(this,n("b639").Buffer)},"6b0d":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=(t,e)=>{const n=t.__vccOpts||t;for(const[r,i]of e)n[r]=i;return n}},"6bfb":function(t,e,n){"use strict";var r=n("3eea");e["a"]={fromIterable(t){return i(s(t))},fromAsyncIterable(t){return i(o(t))},fromDOMStream(t){return i(a(t))},fromNodeStream(t){return i(h(t))},toDOMStream(t,e){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(t,e){throw new Error('"toNodeStream" not available in this environment')}};const i=t=>(t.next(),t);function*s(t){let e,n,i,s,o=!1,a=[],c=0;function u(){return"peek"===i?Object(r["joinUint8Arrays"])(a,s)[0]:([n,a,c]=Object(r["joinUint8Arrays"])(a,s),n)}({cmd:i,size:s}=yield null);let l=Object(r["toUint8ArrayIterator"])(t)[Symbol.iterator]();try{do{if(({done:e,value:n}=isNaN(s-c)?l.next(void 0):l.next(s-c)),!e&&n.byteLength>0&&(a.push(n),c+=n.byteLength),e||s<=c)do{({cmd:i,size:s}=yield u())}while(s<c)}while(!e)}catch(h){(o=!0)&&"function"===typeof l.throw&&l.throw(h)}finally{!1===o&&"function"===typeof l.return&&l.return()}}async function*o(t){let e,n,i,s,o=!1,a=[],c=0;function u(){return"peek"===i?Object(r["joinUint8Arrays"])(a,s)[0]:([n,a,c]=Object(r["joinUint8Arrays"])(a,s),n)}({cmd:i,size:s}=yield null);let l=Object(r["toUint8ArrayAsyncIterator"])(t)[Symbol.asyncIterator]();try{do{if(({done:e,value:n}=isNaN(s-c)?await l.next(void 0):await l.next(s-c)),!e&&n.byteLength>0&&(a.push(n),c+=n.byteLength),e||s<=c)do{({cmd:i,size:s}=yield u())}while(s<c)}while(!e)}catch(h){(o=!0)&&"function"===typeof l.throw&&await l.throw(h)}finally{!1===o&&"function"===typeof l.return&&await l.return()}}async function*a(t){let e,n,i,s=!1,o=!1,a=[],u=0;function l(){return"peek"===n?Object(r["joinUint8Arrays"])(a,i)[0]:([e,a,u]=Object(r["joinUint8Arrays"])(a,i),e)}({cmd:n,size:i}=yield null);let h=new c(t);try{do{if(({done:s,value:e}=isNaN(i-u)?await h["read"](void 0):await h["read"](i-u)),!s&&e.byteLength>0&&(a.push(Object(r["toUint8Array"])(e)),u+=e.byteLength),s||i<=u)do{({cmd:n,size:i}=yield l())}while(i<u)}while(!s)}catch(f){(o=!0)&&await h["cancel"](f)}finally{!1===o?await h["cancel"]():t["locked"]&&h.releaseLock()}}class c{constructor(t){this.source=t,this.byobReader=null,this.defaultReader=null;try{this.supportsBYOB=!!(this.reader=this.getBYOBReader())}catch(e){this.supportsBYOB=!(this.reader=this.getDefaultReader())}}get closed(){return this.reader?this.reader["closed"].catch(()=>{}):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=this.byobReader=this.defaultReader=null}async cancel(t){const{reader:e,source:n}=this;e&&await e["cancel"](t).catch(()=>{}),n&&n["locked"]&&this.releaseLock()}async read(t){if(0===t)return{done:null==this.reader,value:new Uint8Array(0)};const e=this.supportsBYOB&&"number"===typeof t?await this.readFromBYOBReader(t):await this.getDefaultReader().read();return!e.done&&(e.value=Object(r["toUint8Array"])(e)),e}getDefaultReader(){return this.byobReader&&this.releaseLock(),this.defaultReader||(this.defaultReader=this.source["getReader"](),this.defaultReader["closed"].catch(()=>{})),this.reader=this.defaultReader}getBYOBReader(){return this.defaultReader&&this.releaseLock(),this.byobReader||(this.byobReader=this.source["getReader"]({mode:"byob"}),this.byobReader["closed"].catch(()=>{})),this.reader=this.byobReader}async readFromBYOBReader(t){return await u(this.getBYOBReader(),new ArrayBuffer(t),0,t)}}async function u(t,e,n,r){if(n>=r)return{done:!1,value:new Uint8Array(e,0,r)};const{done:i,value:s}=await t.read(new Uint8Array(e,n,r-n));return(n+=s.byteLength)<r&&!i?await u(t,s.buffer,n,r):{done:i,value:new Uint8Array(s.buffer,0,n)}}const l=(t,e)=>{let n,r=t=>n([e,t]);return[e,r,new Promise(i=>(n=i)&&t["once"](e,r))]};async function*h(t){let e,n,i,s=[],o="error",a=!1,c=null,u=0,h=[];function f(){return"peek"===e?Object(r["joinUint8Arrays"])(h,n)[0]:([i,h,u]=Object(r["joinUint8Arrays"])(h,n),i)}if(({cmd:e,size:n}=yield null),t["isTTY"])return yield new Uint8Array(0);try{s[0]=l(t,"end"),s[1]=l(t,"error");do{if(s[2]=l(t,"readable"),[o,c]=await Promise.race(s.map(t=>t[2])),"error"===o)break;if((a="end"===o)||(isFinite(n-u)?(i=Object(r["toUint8Array"])(t["read"](n-u)),i.byteLength<n-u&&(i=Object(r["toUint8Array"])(t["read"](void 0)))):i=Object(r["toUint8Array"])(t["read"](void 0)),i.byteLength>0&&(h.push(i),u+=i.byteLength)),a||n<=u)do{({cmd:e,size:n}=yield f())}while(n<u)}while(!a)}finally{await d(s,"error"===o?c:null)}function d(e,n){return i=h=null,new Promise(async(r,i)=>{for(const[n,o]of e)t["off"](n,o);try{const e=t["destroy"];e&&e.call(t,n),n=void 0}catch(s){n=s||n}finally{null!=n?i(n):r()}})}}},"7a23":function(t,e,n){"use strict";n.d(e,"r",(function(){return Nt})),n.d(e,"i",(function(){return r["L"]})),n.d(e,"j",(function(){return r["M"]})),n.d(e,"v",(function(){return r["O"]})),n.d(e,"a",(function(){return Sr})),n.d(e,"c",(function(){return Kr})),n.d(e,"d",(function(){return Mr})),n.d(e,"e",(function(){return zr})),n.d(e,"f",(function(){return qr})),n.d(e,"g",(function(){return $r})),n.d(e,"h",(function(){return Je})),n.d(e,"k",(function(){return mn})),n.d(e,"l",(function(){return ln})),n.d(e,"m",(function(){return pn})),n.d(e,"n",(function(){return fn})),n.d(e,"o",(function(){return Er})),n.d(e,"p",(function(){return Oe})),n.d(e,"q",(function(){return we})),n.d(e,"s",(function(){return Sn})),n.d(e,"t",(function(){return An})),n.d(e,"u",(function(){return vn})),n.d(e,"w",(function(){return Fe})),n.d(e,"x",(function(){return Ie})),n.d(e,"b",(function(){return Es}));var r=n("9ff4");let i;class s{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=i,!t&&i&&(this.index=(i.scopes||(i.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const e=i;try{return i=this,t()}finally{i=e}}else 0}on(){i=this}off(){i=this.parent}stop(t){if(this._active){let e,n;for(e=0,n=this.effects.length;e<n;e++)this.effects[e].stop();for(e=0,n=this.cleanups.length;e<n;e++)this.cleanups[e]();if(this.scopes)for(e=0,n=this.scopes.length;e<n;e++)this.scopes[e].stop(!0);if(!this.detached&&this.parent&&!t){const t=this.parent.scopes.pop();t&&t!==this&&(this.parent.scopes[this.index]=t,t.index=this.index)}this.parent=void 0,this._active=!1}}}function o(t,e=i){e&&e.active&&e.effects.push(t)}function a(){return i}const c=t=>{const e=new Set(t);return e.w=0,e.n=0,e},u=t=>(t.w&y)>0,l=t=>(t.n&y)>0,h=({deps:t})=>{if(t.length)for(let e=0;e<t.length;e++)t[e].w|=y},f=t=>{const{deps:e}=t;if(e.length){let n=0;for(let r=0;r<e.length;r++){const i=e[r];u(i)&&!l(i)?i.delete(t):e[n++]=i,i.w&=~y,i.n&=~y}e.length=n}},d=new WeakMap;let p=0,y=1;const b=30;let g;const m=Symbol(""),_=Symbol("");class v{constructor(t,e=null,n){this.fn=t,this.scheduler=e,this.active=!0,this.deps=[],this.parent=void 0,o(this,n)}run(){if(!this.active)return this.fn();let t=g,e=O;while(t){if(t===this)return;t=t.parent}try{return this.parent=g,g=this,O=!0,y=1<<++p,p<=b?h(this):w(this),this.fn()}finally{p<=b&&f(this),y=1<<--p,g=this.parent,O=e,this.parent=void 0,this.deferStop&&this.stop()}}stop(){g===this?this.deferStop=!0:this.active&&(w(this),this.onStop&&this.onStop(),this.active=!1)}}function w(t){const{deps:e}=t;if(e.length){for(let n=0;n<e.length;n++)e[n].delete(t);e.length=0}}let O=!0;const I=[];function S(){I.push(O),O=!1}function A(){const t=I.pop();O=void 0===t||t}function T(t,e,n){if(O&&g){let e=d.get(t);e||d.set(t,e=new Map);let r=e.get(n);r||e.set(n,r=c());const i=void 0;B(r,i)}}function B(t,e){let n=!1;p<=b?l(t)||(t.n|=y,n=!u(t)):n=!t.has(g),n&&(t.add(g),g.deps.push(t))}function x(t,e,n,i,s,o){const a=d.get(t);if(!a)return;let u=[];if("clear"===e)u=[...a.values()];else if("length"===n&&Object(r["o"])(t)){const t=Number(i);a.forEach((e,n)=>{("length"===n||n>=t)&&u.push(e)})}else switch(void 0!==n&&u.push(a.get(n)),e){case"add":Object(r["o"])(t)?Object(r["t"])(n)&&u.push(a.get("length")):(u.push(a.get(m)),Object(r["u"])(t)&&u.push(a.get(_)));break;case"delete":Object(r["o"])(t)||(u.push(a.get(m)),Object(r["u"])(t)&&u.push(a.get(_)));break;case"set":Object(r["u"])(t)&&u.push(a.get(m));break}if(1===u.length)u[0]&&j(u[0]);else{const t=[];for(const e of u)e&&t.push(...e);j(c(t))}}function j(t,e){const n=Object(r["o"])(t)?t:[...t];for(const r of n)r.computed&&E(r,e);for(const r of n)r.computed||E(r,e)}function E(t,e){(t!==g||t.allowRecurse)&&(t.scheduler?t.scheduler():t.run())}const D=Object(r["K"])("__proto__,__v_isRef,__isVue"),L=new Set(Object.getOwnPropertyNames(Symbol).filter(t=>"arguments"!==t&&"caller"!==t).map(t=>Symbol[t]).filter(r["G"])),F=R(),U=R(!1,!0),M=R(!0),C=N();function N(){const t={};return["includes","indexOf","lastIndexOf"].forEach(e=>{t[e]=function(...t){const n=Et(this);for(let e=0,i=this.length;e<i;e++)T(n,"get",e+"");const r=n[e](...t);return-1===r||!1===r?n[e](...t.map(Et)):r}}),["push","pop","shift","unshift","splice"].forEach(e=>{t[e]=function(...t){S();const n=Et(this)[e].apply(this,t);return A(),n}}),t}function k(t){const e=Et(this);return T(e,"has",t),e.hasOwnProperty(t)}function R(t=!1,e=!1){return function(n,i,s){if("__v_isReactive"===i)return!t;if("__v_isReadonly"===i)return t;if("__v_isShallow"===i)return e;if("__v_raw"===i&&s===(t?e?_t:mt:e?gt:bt).get(n))return n;const o=Object(r["o"])(n);if(!t){if(o&&Object(r["k"])(C,i))return Reflect.get(C,i,s);if("hasOwnProperty"===i)return k}const a=Reflect.get(n,i,s);return(Object(r["G"])(i)?L.has(i):D(i))?a:(t||T(n,"get",i),e?a:Ct(a)?o&&Object(r["t"])(i)?a:a.value:Object(r["w"])(a)?t?St(a):Ot(a):a)}}const V=z(),P=z(!0);function z(t=!1){return function(e,n,i,s){let o=e[n];if(Bt(o)&&Ct(o)&&!Ct(i))return!1;if(!t&&(xt(i)||Bt(i)||(o=Et(o),i=Et(i)),!Object(r["o"])(e)&&Ct(o)&&!Ct(i)))return o.value=i,!0;const a=Object(r["o"])(e)&&Object(r["t"])(n)?Number(n)<e.length:Object(r["k"])(e,n),c=Reflect.set(e,n,i,s);return e===Et(s)&&(a?Object(r["j"])(i,o)&&x(e,"set",n,i,o):x(e,"add",n,i)),c}}function $(t,e){const n=Object(r["k"])(t,e),i=t[e],s=Reflect.deleteProperty(t,e);return s&&n&&x(t,"delete",e,void 0,i),s}function Y(t,e){const n=Reflect.has(t,e);return Object(r["G"])(e)&&L.has(e)||T(t,"has",e),n}function W(t){return T(t,"iterate",Object(r["o"])(t)?"length":m),Reflect.ownKeys(t)}const H={get:F,set:V,deleteProperty:$,has:Y,ownKeys:W},q={get:M,set(t,e){return!0},deleteProperty(t,e){return!0}},K=Object(r["h"])({},H,{get:U,set:P}),G=t=>t,J=t=>Reflect.getPrototypeOf(t);function Z(t,e,n=!1,r=!1){t=t["__v_raw"];const i=Et(t),s=Et(e);n||(e!==s&&T(i,"get",e),T(i,"get",s));const{has:o}=J(i),a=r?G:n?Ft:Lt;return o.call(i,e)?a(t.get(e)):o.call(i,s)?a(t.get(s)):void(t!==i&&t.get(e))}function X(t,e=!1){const n=this["__v_raw"],r=Et(n),i=Et(t);return e||(t!==i&&T(r,"has",t),T(r,"has",i)),t===i?n.has(t):n.has(t)||n.has(i)}function Q(t,e=!1){return t=t["__v_raw"],!e&&T(Et(t),"iterate",m),Reflect.get(t,"size",t)}function tt(t){t=Et(t);const e=Et(this),n=J(e),r=n.has.call(e,t);return r||(e.add(t),x(e,"add",t,t)),this}function et(t,e){e=Et(e);const n=Et(this),{has:i,get:s}=J(n);let o=i.call(n,t);o||(t=Et(t),o=i.call(n,t));const a=s.call(n,t);return n.set(t,e),o?Object(r["j"])(e,a)&&x(n,"set",t,e,a):x(n,"add",t,e),this}function nt(t){const e=Et(this),{has:n,get:r}=J(e);let i=n.call(e,t);i||(t=Et(t),i=n.call(e,t));const s=r?r.call(e,t):void 0,o=e.delete(t);return i&&x(e,"delete",t,void 0,s),o}function rt(){const t=Et(this),e=0!==t.size,n=void 0,r=t.clear();return e&&x(t,"clear",void 0,void 0,n),r}function it(t,e){return function(n,r){const i=this,s=i["__v_raw"],o=Et(s),a=e?G:t?Ft:Lt;return!t&&T(o,"iterate",m),s.forEach((t,e)=>n.call(r,a(t),a(e),i))}}function st(t,e,n){return function(...i){const s=this["__v_raw"],o=Et(s),a=Object(r["u"])(o),c="entries"===t||t===Symbol.iterator&&a,u="keys"===t&&a,l=s[t](...i),h=n?G:e?Ft:Lt;return!e&&T(o,"iterate",u?_:m),{next(){const{value:t,done:e}=l.next();return e?{value:t,done:e}:{value:c?[h(t[0]),h(t[1])]:h(t),done:e}},[Symbol.iterator](){return this}}}}function ot(t){return function(...e){return"delete"!==t&&this}}function at(){const t={get(t){return Z(this,t)},get size(){return Q(this)},has:X,add:tt,set:et,delete:nt,clear:rt,forEach:it(!1,!1)},e={get(t){return Z(this,t,!1,!0)},get size(){return Q(this)},has:X,add:tt,set:et,delete:nt,clear:rt,forEach:it(!1,!0)},n={get(t){return Z(this,t,!0)},get size(){return Q(this,!0)},has(t){return X.call(this,t,!0)},add:ot("add"),set:ot("set"),delete:ot("delete"),clear:ot("clear"),forEach:it(!0,!1)},r={get(t){return Z(this,t,!0,!0)},get size(){return Q(this,!0)},has(t){return X.call(this,t,!0)},add:ot("add"),set:ot("set"),delete:ot("delete"),clear:ot("clear"),forEach:it(!0,!0)},i=["keys","values","entries",Symbol.iterator];return i.forEach(i=>{t[i]=st(i,!1,!1),n[i]=st(i,!0,!1),e[i]=st(i,!1,!0),r[i]=st(i,!0,!0)}),[t,n,e,r]}const[ct,ut,lt,ht]=at();function ft(t,e){const n=e?t?ht:lt:t?ut:ct;return(e,i,s)=>"__v_isReactive"===i?!t:"__v_isReadonly"===i?t:"__v_raw"===i?e:Reflect.get(Object(r["k"])(n,i)&&i in e?n:e,i,s)}const dt={get:ft(!1,!1)},pt={get:ft(!1,!0)},yt={get:ft(!0,!1)};const bt=new WeakMap,gt=new WeakMap,mt=new WeakMap,_t=new WeakMap;function vt(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function wt(t){return t["__v_skip"]||!Object.isExtensible(t)?0:vt(Object(r["R"])(t))}function Ot(t){return Bt(t)?t:At(t,!1,H,dt,bt)}function It(t){return At(t,!1,K,pt,gt)}function St(t){return At(t,!0,q,yt,mt)}function At(t,e,n,i,s){if(!Object(r["w"])(t))return t;if(t["__v_raw"]&&(!e||!t["__v_isReactive"]))return t;const o=s.get(t);if(o)return o;const a=wt(t);if(0===a)return t;const c=new Proxy(t,2===a?i:n);return s.set(t,c),c}function Tt(t){return Bt(t)?Tt(t["__v_raw"]):!(!t||!t["__v_isReactive"])}function Bt(t){return!(!t||!t["__v_isReadonly"])}function xt(t){return!(!t||!t["__v_isShallow"])}function jt(t){return Tt(t)||Bt(t)}function Et(t){const e=t&&t["__v_raw"];return e?Et(e):t}function Dt(t){return Object(r["g"])(t,"__v_skip",!0),t}const Lt=t=>Object(r["w"])(t)?Ot(t):t,Ft=t=>Object(r["w"])(t)?St(t):t;function Ut(t){O&&g&&(t=Et(t),B(t.dep||(t.dep=c())))}function Mt(t,e){t=Et(t);const n=t.dep;n&&j(n)}function Ct(t){return!(!t||!0!==t.__v_isRef)}function Nt(t){return kt(t,!1)}function kt(t,e){return Ct(t)?t:new Rt(t,e)}class Rt{constructor(t,e){this.__v_isShallow=e,this.dep=void 0,this.__v_isRef=!0,this._rawValue=e?t:Et(t),this._value=e?t:Lt(t)}get value(){return Ut(this),this._value}set value(t){const e=this.__v_isShallow||xt(t)||Bt(t);t=e?t:Et(t),Object(r["j"])(t,this._rawValue)&&(this._rawValue=t,this._value=e?t:Lt(t),Mt(this,t))}}function Vt(t){return Ct(t)?t.value:t}const Pt={get:(t,e,n)=>Vt(Reflect.get(t,e,n)),set:(t,e,n,r)=>{const i=t[e];return Ct(i)&&!Ct(n)?(i.value=n,!0):Reflect.set(t,e,n,r)}};function zt(t){return Tt(t)?t:new Proxy(t,Pt)}class $t{constructor(t,e,n,r){this._setter=e,this.dep=void 0,this.__v_isRef=!0,this["__v_isReadonly"]=!1,this._dirty=!0,this.effect=new v(t,()=>{this._dirty||(this._dirty=!0,Mt(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!r,this["__v_isReadonly"]=n}get value(){const t=Et(this);return Ut(t),!t._dirty&&t._cacheable||(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function Yt(t,e,n=!1){let i,s;const o=Object(r["q"])(t);o?(i=t,s=r["d"]):(i=t.get,s=t.set);const a=new $t(i,s,o||!s,n);return a}function Wt(t,e,n,r){let i;try{i=r?t(...r):t()}catch(s){qt(s,e,n)}return i}function Ht(t,e,n,i){if(Object(r["q"])(t)){const s=Wt(t,e,n,i);return s&&Object(r["z"])(s)&&s.catch(t=>{qt(t,e,n)}),s}const s=[];for(let r=0;r<t.length;r++)s.push(Ht(t[r],e,n,i));return s}function qt(t,e,n,r=!0){const i=e?e.vnode:null;if(e){let r=e.parent;const i=e.proxy,s=n;while(r){const e=r.ec;if(e)for(let n=0;n<e.length;n++)if(!1===e[n](t,i,s))return;r=r.parent}const o=e.appContext.config.errorHandler;if(o)return void Wt(o,null,10,[t,i,s])}Kt(t,n,i,r)}function Kt(t,e,n,r=!0){console.error(t)}let Gt=!1,Jt=!1;const Zt=[];let Xt=0;const Qt=[];let te=null,ee=0;const ne=Promise.resolve();let re=null;function ie(t){const e=re||ne;return t?e.then(this?t.bind(this):t):e}function se(t){let e=Xt+1,n=Zt.length;while(e<n){const r=e+n>>>1,i=fe(Zt[r]);i<t?e=r+1:n=r}return e}function oe(t){Zt.length&&Zt.includes(t,Gt&&t.allowRecurse?Xt+1:Xt)||(null==t.id?Zt.push(t):Zt.splice(se(t.id),0,t),ae())}function ae(){Gt||Jt||(Jt=!0,re=ne.then(pe))}function ce(t){const e=Zt.indexOf(t);e>Xt&&Zt.splice(e,1)}function ue(t){Object(r["o"])(t)?Qt.push(...t):te&&te.includes(t,t.allowRecurse?ee+1:ee)||Qt.push(t),ae()}function le(t,e=(Gt?Xt+1:0)){for(0;e<Zt.length;e++){const t=Zt[e];t&&t.pre&&(Zt.splice(e,1),e--,t())}}function he(t){if(Qt.length){const t=[...new Set(Qt)];if(Qt.length=0,te)return void te.push(...t);for(te=t,te.sort((t,e)=>fe(t)-fe(e)),ee=0;ee<te.length;ee++)te[ee]();te=null,ee=0}}const fe=t=>null==t.id?1/0:t.id,de=(t,e)=>{const n=fe(t)-fe(e);if(0===n){if(t.pre&&!e.pre)return-1;if(e.pre&&!t.pre)return 1}return n};function pe(t){Jt=!1,Gt=!0,Zt.sort(de);r["d"];try{for(Xt=0;Xt<Zt.length;Xt++){const t=Zt[Xt];t&&!1!==t.active&&Wt(t,null,14)}}finally{Xt=0,Zt.length=0,he(t),Gt=!1,re=null,(Zt.length||Qt.length)&&pe(t)}}function ye(t,e,...n){if(t.isUnmounted)return;const i=t.vnode.props||r["b"];let s=n;const o=e.startsWith("update:"),a=o&&e.slice(7);if(a&&a in i){const t=("modelValue"===a?"model":a)+"Modifiers",{number:e,trim:o}=i[t]||r["b"];o&&(s=n.map(t=>Object(r["F"])(t)?t.trim():t)),e&&(s=n.map(r["J"]))}let c;let u=i[c=Object(r["P"])(e)]||i[c=Object(r["P"])(Object(r["e"])(e))];!u&&o&&(u=i[c=Object(r["P"])(Object(r["l"])(e))]),u&&Ht(u,t,6,s);const l=i[c+"Once"];if(l){if(t.emitted){if(t.emitted[c])return}else t.emitted={};t.emitted[c]=!0,Ht(l,t,6,s)}}function be(t,e,n=!1){const i=e.emitsCache,s=i.get(t);if(void 0!==s)return s;const o=t.emits;let a={},c=!1;if(!Object(r["q"])(t)){const i=t=>{const n=be(t,e,!0);n&&(c=!0,Object(r["h"])(a,n))};!n&&e.mixins.length&&e.mixins.forEach(i),t.extends&&i(t.extends),t.mixins&&t.mixins.forEach(i)}return o||c?(Object(r["o"])(o)?o.forEach(t=>a[t]=null):Object(r["h"])(a,o),Object(r["w"])(t)&&i.set(t,a),a):(Object(r["w"])(t)&&i.set(t,null),null)}function ge(t,e){return!(!t||!Object(r["x"])(e))&&(e=e.slice(2).replace(/Once$/,""),Object(r["k"])(t,e[0].toLowerCase()+e.slice(1))||Object(r["k"])(t,Object(r["l"])(e))||Object(r["k"])(t,e))}let me=null,_e=null;function ve(t){const e=me;return me=t,_e=t&&t.type.__scopeId||null,e}function we(t){_e=t}function Oe(){_e=null}function Ie(t,e=me,n){if(!e)return t;if(t._n)return t;const r=(...n)=>{r._d&&Fr(-1);const i=ve(e);let s;try{s=t(...n)}finally{ve(i),r._d&&Fr(1)}return s};return r._n=!0,r._c=!0,r._d=!0,r}function Se(t){const{type:e,vnode:n,proxy:i,withProxy:s,props:o,propsOptions:[a],slots:c,attrs:u,emit:l,render:h,renderCache:f,data:d,setupState:p,ctx:y,inheritAttrs:b}=t;let g,m;const _=ve(t);try{if(4&n.shapeFlag){const t=s||i;g=Gr(h.call(t,t,f,o,p,d,y)),m=u}else{const t=e;0,g=Gr(t.length>1?t(o,{attrs:u,slots:c,emit:l}):t(o,null)),m=e.props?u:Ae(u)}}catch(w){xr.length=0,qt(w,t,1),g=$r(Tr)}let v=g;if(m&&!1!==b){const t=Object.keys(m),{shapeFlag:e}=v;t.length&&7&e&&(a&&t.some(r["v"])&&(m=Te(m,a)),v=Hr(v,m))}return n.dirs&&(v=Hr(v),v.dirs=v.dirs?v.dirs.concat(n.dirs):n.dirs),n.transition&&(v.transition=n.transition),g=v,ve(_),g}const Ae=t=>{let e;for(const n in t)("class"===n||"style"===n||Object(r["x"])(n))&&((e||(e={}))[n]=t[n]);return e},Te=(t,e)=>{const n={};for(const i in t)Object(r["v"])(i)&&i.slice(9)in e||(n[i]=t[i]);return n};function Be(t,e,n){const{props:r,children:i,component:s}=t,{props:o,children:a,patchFlag:c}=e,u=s.emitsOptions;if(e.dirs||e.transition)return!0;if(!(n&&c>=0))return!(!i&&!a||a&&a.$stable)||r!==o&&(r?!o||xe(r,o,u):!!o);if(1024&c)return!0;if(16&c)return r?xe(r,o,u):!!o;if(8&c){const t=e.dynamicProps;for(let e=0;e<t.length;e++){const n=t[e];if(o[n]!==r[n]&&!ge(u,n))return!0}}return!1}function xe(t,e,n){const r=Object.keys(e);if(r.length!==Object.keys(t).length)return!0;for(let i=0;i<r.length;i++){const s=r[i];if(e[s]!==t[s]&&!ge(n,s))return!0}return!1}function je({vnode:t,parent:e},n){while(e&&e.subTree===t)(t=e.vnode).el=n,e=e.parent}const Ee=t=>t.__isSuspense;function De(t,e){e&&e.pendingBranch?Object(r["o"])(t)?e.effects.push(...t):e.effects.push(t):ue(t)}const Le={};function Fe(t,e,n){return Ue(t,e,n)}function Ue(t,e,{immediate:n,deep:i,flush:s,onTrack:o,onTrigger:c}=r["b"]){var u;const l=a()===(null==(u=ri)?void 0:u.scope)?ri:null;let h,f,d=!1,p=!1;if(Ct(t)?(h=()=>t.value,d=xt(t)):Tt(t)?(h=()=>t,i=!0):Object(r["o"])(t)?(p=!0,d=t.some(t=>Tt(t)||xt(t)),h=()=>t.map(t=>Ct(t)?t.value:Tt(t)?Ne(t):Object(r["q"])(t)?Wt(t,l,2):void 0)):h=Object(r["q"])(t)?e?()=>Wt(t,l,2):()=>{if(!l||!l.isUnmounted)return f&&f(),Ht(t,l,3,[b])}:r["d"],e&&i){const t=h;h=()=>Ne(t())}let y,b=t=>{f=w.onStop=()=>{Wt(t,l,4)}};if(di){if(b=r["d"],e?n&&Ht(e,l,3,[h(),p?[]:void 0,b]):h(),"sync"!==s)return r["d"];{const t=Ti();y=t.__watcherHandles||(t.__watcherHandles=[])}}let g=p?new Array(t.length).fill(Le):Le;const m=()=>{if(w.active)if(e){const t=w.run();(i||d||(p?t.some((t,e)=>Object(r["j"])(t,g[e])):Object(r["j"])(t,g)))&&(f&&f(),Ht(e,l,3,[t,g===Le?void 0:p&&g[0]===Le?[]:g,b]),g=t)}else w.run()};let _;m.allowRecurse=!!e,"sync"===s?_=m:"post"===s?_=()=>gr(m,l&&l.suspense):(m.pre=!0,l&&(m.id=l.uid),_=()=>oe(m));const w=new v(h,_);e?n?m():g=w.run():"post"===s?gr(w.run.bind(w),l&&l.suspense):w.run();const O=()=>{w.stop(),l&&l.scope&&Object(r["N"])(l.scope.effects,w)};return y&&y.push(O),O}function Me(t,e,n){const i=this.proxy,s=Object(r["F"])(t)?t.includes(".")?Ce(i,t):()=>i[t]:t.bind(i,i);let o;Object(r["q"])(e)?o=e:(o=e.handler,n=e);const a=ri;ci(this);const c=Ue(s,o.bind(i),n);return a?ci(a):ui(),c}function Ce(t,e){const n=e.split(".");return()=>{let e=t;for(let t=0;t<n.length&&e;t++)e=e[n[t]];return e}}function Ne(t,e){if(!Object(r["w"])(t)||t["__v_skip"])return t;if(e=e||new Set,e.has(t))return t;if(e.add(t),Ct(t))Ne(t.value,e);else if(Object(r["o"])(t))for(let n=0;n<t.length;n++)Ne(t[n],e);else if(Object(r["D"])(t)||Object(r["u"])(t))t.forEach(t=>{Ne(t,e)});else if(Object(r["y"])(t))for(const n in t)Ne(t[n],e);return t}function ke(t,e,n,r){const i=t.dirs,s=e&&e.dirs;for(let o=0;o<i.length;o++){const a=i[o];s&&(a.oldValue=s[o].value);let c=a.dir[r];c&&(S(),Ht(c,n,8,[t.el,a,t,e]),A())}}function Re(){const t={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ln(()=>{t.isMounted=!0}),dn(()=>{t.isUnmounting=!0}),t}const Ve=[Function,Array],Pe={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ve,onEnter:Ve,onAfterEnter:Ve,onEnterCancelled:Ve,onBeforeLeave:Ve,onLeave:Ve,onAfterLeave:Ve,onLeaveCancelled:Ve,onBeforeAppear:Ve,onAppear:Ve,onAfterAppear:Ve,onAppearCancelled:Ve},ze={name:"BaseTransition",props:Pe,setup(t,{slots:e}){const n=ii(),r=Re();let i;return()=>{const s=e.default&&Ge(e.default(),!0);if(!s||!s.length)return;let o=s[0];if(s.length>1){let t=!1;for(const e of s)if(e.type!==Tr){0,o=e,t=!0;break}}const a=Et(t),{mode:c}=a;if(r.isLeaving)return He(o);const u=qe(o);if(!u)return He(o);const l=We(u,a,r,n);Ke(u,l);const h=n.subTree,f=h&&qe(h);let d=!1;const{getTransitionKey:p}=u.type;if(p){const t=p();void 0===i?i=t:t!==i&&(i=t,d=!0)}if(f&&f.type!==Tr&&(!kr(u,f)||d)){const t=We(f,a,r,n);if(Ke(f,t),"out-in"===c)return r.isLeaving=!0,t.afterLeave=()=>{r.isLeaving=!1,!1!==n.update.active&&n.update()},He(o);"in-out"===c&&u.type!==Tr&&(t.delayLeave=(t,e,n)=>{const i=Ye(r,f);i[String(f.key)]=f,t._leaveCb=()=>{e(),t._leaveCb=void 0,delete l.delayedLeave},l.delayedLeave=n})}return o}}},$e=ze;function Ye(t,e){const{leavingVNodes:n}=t;let r=n.get(e.type);return r||(r=Object.create(null),n.set(e.type,r)),r}function We(t,e,n,i){const{appear:s,mode:o,persisted:a=!1,onBeforeEnter:c,onEnter:u,onAfterEnter:l,onEnterCancelled:h,onBeforeLeave:f,onLeave:d,onAfterLeave:p,onLeaveCancelled:y,onBeforeAppear:b,onAppear:g,onAfterAppear:m,onAppearCancelled:_}=e,v=String(t.key),w=Ye(n,t),O=(t,e)=>{t&&Ht(t,i,9,e)},I=(t,e)=>{const n=e[1];O(t,e),Object(r["o"])(t)?t.every(t=>t.length<=1)&&n():t.length<=1&&n()},S={mode:o,persisted:a,beforeEnter(e){let r=c;if(!n.isMounted){if(!s)return;r=b||c}e._leaveCb&&e._leaveCb(!0);const i=w[v];i&&kr(t,i)&&i.el._leaveCb&&i.el._leaveCb(),O(r,[e])},enter(t){let e=u,r=l,i=h;if(!n.isMounted){if(!s)return;e=g||u,r=m||l,i=_||h}let o=!1;const a=t._enterCb=e=>{o||(o=!0,O(e?i:r,[t]),S.delayedLeave&&S.delayedLeave(),t._enterCb=void 0)};e?I(e,[t,a]):a()},leave(e,r){const i=String(t.key);if(e._enterCb&&e._enterCb(!0),n.isUnmounting)return r();O(f,[e]);let s=!1;const o=e._leaveCb=n=>{s||(s=!0,r(),O(n?y:p,[e]),e._leaveCb=void 0,w[i]===t&&delete w[i])};w[i]=t,d?I(d,[e,o]):o()},clone(t){return We(t,e,n,i)}};return S}function He(t){if(Xe(t))return t=Hr(t),t.children=null,t}function qe(t){return Xe(t)?t.children?t.children[0]:void 0:t}function Ke(t,e){6&t.shapeFlag&&t.component?Ke(t.component.subTree,e):128&t.shapeFlag?(t.ssContent.transition=e.clone(t.ssContent),t.ssFallback.transition=e.clone(t.ssFallback)):t.transition=e}function Ge(t,e=!1,n){let r=[],i=0;for(let s=0;s<t.length;s++){let o=t[s];const a=null==n?o.key:String(n)+String(null!=o.key?o.key:s);o.type===Sr?(128&o.patchFlag&&i++,r=r.concat(Ge(o.children,e,a))):(e||o.type!==Tr)&&r.push(null!=a?Hr(o,{key:a}):o)}if(i>1)for(let s=0;s<r.length;s++)r[s].patchFlag=-2;return r}function Je(t,e){return Object(r["q"])(t)?(()=>Object(r["h"])({name:t.name},e,{setup:t}))():t}const Ze=t=>!!t.type.__asyncLoader;const Xe=t=>t.type.__isKeepAlive;RegExp,RegExp;function Qe(t,e){return Object(r["o"])(t)?t.some(t=>Qe(t,e)):Object(r["F"])(t)?t.split(",").includes(e):!!Object(r["A"])(t)&&t.test(e)}function tn(t,e){nn(t,"a",e)}function en(t,e){nn(t,"da",e)}function nn(t,e,n=ri){const r=t.__wdc||(t.__wdc=()=>{let e=n;while(e){if(e.isDeactivated)return;e=e.parent}return t()});if(an(e,r,n),n){let t=n.parent;while(t&&t.parent)Xe(t.parent.vnode)&&rn(r,e,n,t),t=t.parent}}function rn(t,e,n,i){const s=an(e,t,i,!0);pn(()=>{Object(r["N"])(i[e],s)},n)}function sn(t){t.shapeFlag&=-257,t.shapeFlag&=-513}function on(t){return 128&t.shapeFlag?t.ssContent:t}function an(t,e,n=ri,r=!1){if(n){const i=n[t]||(n[t]=[]),s=e.__weh||(e.__weh=(...r)=>{if(n.isUnmounted)return;S(),ci(n);const i=Ht(e,n,t,r);return ui(),A(),i});return r?i.unshift(s):i.push(s),s}}const cn=t=>(e,n=ri)=>(!di||"sp"===t)&&an(t,(...t)=>e(...t),n),un=cn("bm"),ln=cn("m"),hn=cn("bu"),fn=cn("u"),dn=cn("bum"),pn=cn("um"),yn=cn("sp"),bn=cn("rtg"),gn=cn("rtc");function mn(t,e=ri){an("ec",t,e)}const _n="components";function vn(t,e){return On(_n,t,!0,e)||t}const wn=Symbol.for("v-ndc");function On(t,e,n=!0,i=!1){const s=me||ri;if(s){const n=s.type;if(t===_n){const t=wi(n,!1);if(t&&(t===e||t===Object(r["e"])(e)||t===Object(r["f"])(Object(r["e"])(e))))return n}const o=In(s[t]||n[t],e)||In(s.appContext[t],e);return!o&&i?n:o}}function In(t,e){return t&&(t[e]||t[Object(r["e"])(e)]||t[Object(r["f"])(Object(r["e"])(e))])}function Sn(t,e,n,i){let s;const o=n&&n[i];if(Object(r["o"])(t)||Object(r["F"])(t)){s=new Array(t.length);for(let n=0,r=t.length;n<r;n++)s[n]=e(t[n],n,void 0,o&&o[n])}else if("number"===typeof t){0,s=new Array(t);for(let n=0;n<t;n++)s[n]=e(n+1,n,void 0,o&&o[n])}else if(Object(r["w"])(t))if(t[Symbol.iterator])s=Array.from(t,(t,n)=>e(t,n,void 0,o&&o[n]));else{const n=Object.keys(t);s=new Array(n.length);for(let r=0,i=n.length;r<i;r++){const i=n[r];s[r]=e(t[i],i,r,o&&o[r])}}else s=[];return n&&(n[i]=s),s}function An(t,e,n={},r,i){if(me.isCE||me.parent&&Ze(me.parent)&&me.parent.isCE)return"default"!==e&&(n.name=e),$r("slot",n,r&&r());let s=t[e];s&&s._c&&(s._d=!1),Er();const o=s&&Tn(s(n)),a=Cr(Sr,{key:n.key||o&&o.key||"_"+e},o||(r?r():[]),o&&1===t._?64:-2);return!i&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),s&&s._c&&(s._d=!0),a}function Tn(t){return t.some(t=>!Nr(t)||t.type!==Tr&&!(t.type===Sr&&!Tn(t.children)))?t:null}const Bn=t=>t?li(t)?vi(t)||t.proxy:Bn(t.parent):null,xn=Object(r["h"])(Object.create(null),{$:t=>t,$el:t=>t.vnode.el,$data:t=>t.data,$props:t=>t.props,$attrs:t=>t.attrs,$slots:t=>t.slots,$refs:t=>t.refs,$parent:t=>Bn(t.parent),$root:t=>Bn(t.root),$emit:t=>t.emit,$options:t=>Nn(t),$forceUpdate:t=>t.f||(t.f=()=>oe(t.update)),$nextTick:t=>t.n||(t.n=ie.bind(t.proxy)),$watch:t=>Me.bind(t)}),jn=(t,e)=>t!==r["b"]&&!t.__isScriptSetup&&Object(r["k"])(t,e),En={get({_:t},e){const{ctx:n,setupState:i,data:s,props:o,accessCache:a,type:c,appContext:u}=t;let l;if("$"!==e[0]){const c=a[e];if(void 0!==c)switch(c){case 1:return i[e];case 2:return s[e];case 4:return n[e];case 3:return o[e]}else{if(jn(i,e))return a[e]=1,i[e];if(s!==r["b"]&&Object(r["k"])(s,e))return a[e]=2,s[e];if((l=t.propsOptions[0])&&Object(r["k"])(l,e))return a[e]=3,o[e];if(n!==r["b"]&&Object(r["k"])(n,e))return a[e]=4,n[e];Ln&&(a[e]=0)}}const h=xn[e];let f,d;return h?("$attrs"===e&&T(t,"get",e),h(t)):(f=c.__cssModules)&&(f=f[e])?f:n!==r["b"]&&Object(r["k"])(n,e)?(a[e]=4,n[e]):(d=u.config.globalProperties,Object(r["k"])(d,e)?d[e]:void 0)},set({_:t},e,n){const{data:i,setupState:s,ctx:o}=t;return jn(s,e)?(s[e]=n,!0):i!==r["b"]&&Object(r["k"])(i,e)?(i[e]=n,!0):!Object(r["k"])(t.props,e)&&(("$"!==e[0]||!(e.slice(1)in t))&&(o[e]=n,!0))},has({_:{data:t,setupState:e,accessCache:n,ctx:i,appContext:s,propsOptions:o}},a){let c;return!!n[a]||t!==r["b"]&&Object(r["k"])(t,a)||jn(e,a)||(c=o[0])&&Object(r["k"])(c,a)||Object(r["k"])(i,a)||Object(r["k"])(xn,a)||Object(r["k"])(s.config.globalProperties,a)},defineProperty(t,e,n){return null!=n.get?t._.accessCache[e]=0:Object(r["k"])(n,"value")&&this.set(t,e,n.value,null),Reflect.defineProperty(t,e,n)}};function Dn(t){return Object(r["o"])(t)?t.reduce((t,e)=>(t[e]=null,t),{}):t}let Ln=!0;function Fn(t){const e=Nn(t),n=t.proxy,i=t.ctx;Ln=!1,e.beforeCreate&&Mn(e.beforeCreate,t,"bc");const{data:s,computed:o,methods:a,watch:c,provide:u,inject:l,created:h,beforeMount:f,mounted:d,beforeUpdate:p,updated:y,activated:b,deactivated:g,beforeDestroy:m,beforeUnmount:_,destroyed:v,unmounted:w,render:O,renderTracked:I,renderTriggered:S,errorCaptured:A,serverPrefetch:T,expose:B,inheritAttrs:x,components:j,directives:E,filters:D}=e,L=null;if(l&&Un(l,i,L),a)for(const U in a){const t=a[U];Object(r["q"])(t)&&(i[U]=t.bind(n))}if(s){0;const e=s.call(n,n);0,Object(r["w"])(e)&&(t.data=Ot(e))}if(Ln=!0,o)for(const U in o){const t=o[U],e=Object(r["q"])(t)?t.bind(n,n):Object(r["q"])(t.get)?t.get.bind(n,n):r["d"];0;const s=!Object(r["q"])(t)&&Object(r["q"])(t.set)?t.set.bind(n):r["d"],a=Ii({get:e,set:s});Object.defineProperty(i,U,{enumerable:!0,configurable:!0,get:()=>a.value,set:t=>a.value=t})}if(c)for(const r in c)Cn(c[r],i,n,r);if(u){const t=Object(r["q"])(u)?u.call(n):u;Reflect.ownKeys(t).forEach(e=>{Zn(e,t[e])})}function F(t,e){Object(r["o"])(e)?e.forEach(e=>t(e.bind(n))):e&&t(e.bind(n))}if(h&&Mn(h,t,"c"),F(un,f),F(ln,d),F(hn,p),F(fn,y),F(tn,b),F(en,g),F(mn,A),F(gn,I),F(bn,S),F(dn,_),F(pn,w),F(yn,T),Object(r["o"])(B))if(B.length){const e=t.exposed||(t.exposed={});B.forEach(t=>{Object.defineProperty(e,t,{get:()=>n[t],set:e=>n[t]=e})})}else t.exposed||(t.exposed={});O&&t.render===r["d"]&&(t.render=O),null!=x&&(t.inheritAttrs=x),j&&(t.components=j),E&&(t.directives=E)}function Un(t,e,n=r["d"]){Object(r["o"])(t)&&(t=zn(t));for(const i in t){const n=t[i];let s;s=Object(r["w"])(n)?"default"in n?Xn(n.from||i,n.default,!0):Xn(n.from||i):Xn(n),Ct(s)?Object.defineProperty(e,i,{enumerable:!0,configurable:!0,get:()=>s.value,set:t=>s.value=t}):e[i]=s}}function Mn(t,e,n){Ht(Object(r["o"])(t)?t.map(t=>t.bind(e.proxy)):t.bind(e.proxy),e,n)}function Cn(t,e,n,i){const s=i.includes(".")?Ce(n,i):()=>n[i];if(Object(r["F"])(t)){const n=e[t];Object(r["q"])(n)&&Fe(s,n)}else if(Object(r["q"])(t))Fe(s,t.bind(n));else if(Object(r["w"])(t))if(Object(r["o"])(t))t.forEach(t=>Cn(t,e,n,i));else{const i=Object(r["q"])(t.handler)?t.handler.bind(n):e[t.handler];Object(r["q"])(i)&&Fe(s,i,t)}else 0}function Nn(t){const e=t.type,{mixins:n,extends:i}=e,{mixins:s,optionsCache:o,config:{optionMergeStrategies:a}}=t.appContext,c=o.get(e);let u;return c?u=c:s.length||n||i?(u={},s.length&&s.forEach(t=>kn(u,t,a,!0)),kn(u,e,a)):u=e,Object(r["w"])(e)&&o.set(e,u),u}function kn(t,e,n,r=!1){const{mixins:i,extends:s}=e;s&&kn(t,s,n,!0),i&&i.forEach(e=>kn(t,e,n,!0));for(const o in e)if(r&&"expose"===o);else{const r=Rn[o]||n&&n[o];t[o]=r?r(t[o],e[o]):e[o]}return t}const Rn={data:Vn,props:Wn,emits:Wn,methods:Yn,computed:Yn,beforeCreate:$n,created:$n,beforeMount:$n,mounted:$n,beforeUpdate:$n,updated:$n,beforeDestroy:$n,beforeUnmount:$n,destroyed:$n,unmounted:$n,activated:$n,deactivated:$n,errorCaptured:$n,serverPrefetch:$n,components:Yn,directives:Yn,watch:Hn,provide:Vn,inject:Pn};function Vn(t,e){return e?t?function(){return Object(r["h"])(Object(r["q"])(t)?t.call(this,this):t,Object(r["q"])(e)?e.call(this,this):e)}:e:t}function Pn(t,e){return Yn(zn(t),zn(e))}function zn(t){if(Object(r["o"])(t)){const e={};for(let n=0;n<t.length;n++)e[t[n]]=t[n];return e}return t}function $n(t,e){return t?[...new Set([].concat(t,e))]:e}function Yn(t,e){return t?Object(r["h"])(Object.create(null),t,e):e}function Wn(t,e){return t?Object(r["o"])(t)&&Object(r["o"])(e)?[...new Set([...t,...e])]:Object(r["h"])(Object.create(null),Dn(t),Dn(null!=e?e:{})):e}function Hn(t,e){if(!t)return e;if(!e)return t;const n=Object(r["h"])(Object.create(null),t);for(const r in e)n[r]=$n(t[r],e[r]);return n}function qn(){return{app:null,config:{isNativeTag:r["c"],performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Kn=0;function Gn(t,e){return function(n,i=null){Object(r["q"])(n)||(n=Object(r["h"])({},n)),null==i||Object(r["w"])(i)||(i=null);const s=qn();const o=new Set;let a=!1;const c=s.app={_uid:Kn++,_component:n,_props:i,_container:null,_context:s,_instance:null,version:Bi,get config(){return s.config},set config(t){0},use(t,...e){return o.has(t)||(t&&Object(r["q"])(t.install)?(o.add(t),t.install(c,...e)):Object(r["q"])(t)&&(o.add(t),t(c,...e))),c},mixin(t){return s.mixins.includes(t)||s.mixins.push(t),c},component(t,e){return e?(s.components[t]=e,c):s.components[t]},directive(t,e){return e?(s.directives[t]=e,c):s.directives[t]},mount(r,o,u){if(!a){0;const l=$r(n,i);return l.appContext=s,o&&e?e(l,r):t(l,r,u),a=!0,c._container=r,r.__vue_app__=c,vi(l.component)||l.component.proxy}},unmount(){a&&(t(null,c._container),delete c._container.__vue_app__)},provide(t,e){return s.provides[t]=e,c},runWithContext(t){Jn=c;try{return t()}finally{Jn=null}}};return c}}let Jn=null;function Zn(t,e){if(ri){let n=ri.provides;const r=ri.parent&&ri.parent.provides;r===n&&(n=ri.provides=Object.create(r)),n[t]=e}else 0}function Xn(t,e,n=!1){const i=ri||me;if(i||Jn){const s=i?null==i.parent?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:Jn._context.provides;if(s&&t in s)return s[t];if(arguments.length>1)return n&&Object(r["q"])(e)?e.call(i&&i.proxy):e}else 0}function Qn(t,e,n,i=!1){const s={},o={};Object(r["g"])(o,Rr,1),t.propsDefaults=Object.create(null),er(t,e,s,o);for(const r in t.propsOptions[0])r in s||(s[r]=void 0);n?t.props=i?s:It(s):t.type.props?t.props=s:t.props=o,t.attrs=o}function tr(t,e,n,i){const{props:s,attrs:o,vnode:{patchFlag:a}}=t,c=Et(s),[u]=t.propsOptions;let l=!1;if(!(i||a>0)||16&a){let i;er(t,e,s,o)&&(l=!0);for(const o in c)e&&(Object(r["k"])(e,o)||(i=Object(r["l"])(o))!==o&&Object(r["k"])(e,i))||(u?!n||void 0===n[o]&&void 0===n[i]||(s[o]=nr(u,c,o,void 0,t,!0)):delete s[o]);if(o!==c)for(const t in o)e&&Object(r["k"])(e,t)||(delete o[t],l=!0)}else if(8&a){const n=t.vnode.dynamicProps;for(let i=0;i<n.length;i++){let a=n[i];if(ge(t.emitsOptions,a))continue;const h=e[a];if(u)if(Object(r["k"])(o,a))h!==o[a]&&(o[a]=h,l=!0);else{const e=Object(r["e"])(a);s[e]=nr(u,c,e,h,t,!1)}else h!==o[a]&&(o[a]=h,l=!0)}}l&&x(t,"set","$attrs")}function er(t,e,n,i){const[s,o]=t.propsOptions;let a,c=!1;if(e)for(let u in e){if(Object(r["B"])(u))continue;const l=e[u];let h;s&&Object(r["k"])(s,h=Object(r["e"])(u))?o&&o.includes(h)?(a||(a={}))[h]=l:n[h]=l:ge(t.emitsOptions,u)||u in i&&l===i[u]||(i[u]=l,c=!0)}if(o){const e=Et(n),i=a||r["b"];for(let a=0;a<o.length;a++){const c=o[a];n[c]=nr(s,e,c,i[c],t,!Object(r["k"])(i,c))}}return c}function nr(t,e,n,i,s,o){const a=t[n];if(null!=a){const t=Object(r["k"])(a,"default");if(t&&void 0===i){const t=a.default;if(a.type!==Function&&!a.skipFactory&&Object(r["q"])(t)){const{propsDefaults:r}=s;n in r?i=r[n]:(ci(s),i=r[n]=t.call(null,e),ui())}else i=t}a[0]&&(o&&!t?i=!1:!a[1]||""!==i&&i!==Object(r["l"])(n)||(i=!0))}return i}function rr(t,e,n=!1){const i=e.propsCache,s=i.get(t);if(s)return s;const o=t.props,a={},c=[];let u=!1;if(!Object(r["q"])(t)){const i=t=>{u=!0;const[n,i]=rr(t,e,!0);Object(r["h"])(a,n),i&&c.push(...i)};!n&&e.mixins.length&&e.mixins.forEach(i),t.extends&&i(t.extends),t.mixins&&t.mixins.forEach(i)}if(!o&&!u)return Object(r["w"])(t)&&i.set(t,r["a"]),r["a"];if(Object(r["o"])(o))for(let h=0;h<o.length;h++){0;const t=Object(r["e"])(o[h]);ir(t)&&(a[t]=r["b"])}else if(o){0;for(const t in o){const e=Object(r["e"])(t);if(ir(e)){const n=o[t],i=a[e]=Object(r["o"])(n)||Object(r["q"])(n)?{type:n}:Object(r["h"])({},n);if(i){const t=ar(Boolean,i.type),n=ar(String,i.type);i[0]=t>-1,i[1]=n<0||t<n,(t>-1||Object(r["k"])(i,"default"))&&c.push(e)}}}}const l=[a,c];return Object(r["w"])(t)&&i.set(t,l),l}function ir(t){return"$"!==t[0]}function sr(t){const e=t&&t.toString().match(/^\s*(function|class) (\w+)/);return e?e[2]:null===t?"null":""}function or(t,e){return sr(t)===sr(e)}function ar(t,e){return Object(r["o"])(e)?e.findIndex(e=>or(e,t)):Object(r["q"])(e)&&or(e,t)?0:-1}const cr=t=>"_"===t[0]||"$stable"===t,ur=t=>Object(r["o"])(t)?t.map(Gr):[Gr(t)],lr=(t,e,n)=>{if(e._n)return e;const r=Ie((...t)=>ur(e(...t)),n);return r._c=!1,r},hr=(t,e,n)=>{const i=t._ctx;for(const s in t){if(cr(s))continue;const n=t[s];if(Object(r["q"])(n))e[s]=lr(s,n,i);else if(null!=n){0;const t=ur(n);e[s]=()=>t}}},fr=(t,e)=>{const n=ur(e);t.slots.default=()=>n},dr=(t,e)=>{if(32&t.vnode.shapeFlag){const n=e._;n?(t.slots=Et(e),Object(r["g"])(e,"_",n)):hr(e,t.slots={})}else t.slots={},e&&fr(t,e);Object(r["g"])(t.slots,Rr,1)},pr=(t,e,n)=>{const{vnode:i,slots:s}=t;let o=!0,a=r["b"];if(32&i.shapeFlag){const t=e._;t?n&&1===t?o=!1:(Object(r["h"])(s,e),n||1!==t||delete s._):(o=!e.$stable,hr(e,s)),a=e}else e&&(fr(t,e),a={default:1});if(o)for(const r in s)cr(r)||r in a||delete s[r]};function yr(t,e,n,i,s=!1){if(Object(r["o"])(t))return void t.forEach((t,o)=>yr(t,e&&(Object(r["o"])(e)?e[o]:e),n,i,s));if(Ze(i)&&!s)return;const o=4&i.shapeFlag?vi(i.component)||i.component.proxy:i.el,a=s?null:o,{i:c,r:u}=t;const l=e&&e.r,h=c.refs===r["b"]?c.refs={}:c.refs,f=c.setupState;if(null!=l&&l!==u&&(Object(r["F"])(l)?(h[l]=null,Object(r["k"])(f,l)&&(f[l]=null)):Ct(l)&&(l.value=null)),Object(r["q"])(u))Wt(u,c,12,[a,h]);else{const e=Object(r["F"])(u),i=Ct(u);if(e||i){const c=()=>{if(t.f){const n=e?Object(r["k"])(f,u)?f[u]:h[u]:u.value;s?Object(r["o"])(n)&&Object(r["N"])(n,o):Object(r["o"])(n)?n.includes(o)||n.push(o):e?(h[u]=[o],Object(r["k"])(f,u)&&(f[u]=h[u])):(u.value=[o],t.k&&(h[t.k]=u.value))}else e?(h[u]=a,Object(r["k"])(f,u)&&(f[u]=a)):i&&(u.value=a,t.k&&(h[t.k]=a))};a?(c.id=-1,gr(c,n)):c()}else 0}}function br(){}const gr=De;function mr(t){return _r(t)}function _r(t,e){br();const n=Object(r["i"])();n.__VUE__=!0;const{insert:i,remove:s,patchProp:o,createElement:a,createText:c,createComment:u,setText:l,setElementText:h,parentNode:f,nextSibling:d,setScopeId:p=r["d"],insertStaticContent:y}=t,b=(t,e,n,r=null,i=null,s=null,o=!1,a=null,c=!!e.dynamicChildren)=>{if(t===e)return;t&&!kr(t,e)&&(r=q(t),z(t,i,s,!0),t=null),-2===e.patchFlag&&(c=!1,e.dynamicChildren=null);const{type:u,ref:l,shapeFlag:h}=e;switch(u){case Ar:g(t,e,n,r);break;case Tr:m(t,e,n,r);break;case Br:null==t&&_(e,n,r,o);break;case Sr:L(t,e,n,r,i,s,o,a,c);break;default:1&h?I(t,e,n,r,i,s,o,a,c):6&h?F(t,e,n,r,i,s,o,a,c):(64&h||128&h)&&u.process(t,e,n,r,i,s,o,a,c,G)}null!=l&&i&&yr(l,t&&t.ref,s,e||t,!e)},g=(t,e,n,r)=>{if(null==t)i(e.el=c(e.children),n,r);else{const n=e.el=t.el;e.children!==t.children&&l(n,e.children)}},m=(t,e,n,r)=>{null==t?i(e.el=u(e.children||""),n,r):e.el=t.el},_=(t,e,n,r)=>{[t.el,t.anchor]=y(t.children,e,n,r,t.el,t.anchor)},w=({el:t,anchor:e},n,r)=>{let s;while(t&&t!==e)s=d(t),i(t,n,r),t=s;i(e,n,r)},O=({el:t,anchor:e})=>{let n;while(t&&t!==e)n=d(t),s(t),t=n;s(e)},I=(t,e,n,r,i,s,o,a,c)=>{o=o||"svg"===e.type,null==t?T(e,n,r,i,s,o,a,c):j(t,e,i,s,o,a,c)},T=(t,e,n,s,c,u,l,f)=>{let d,p;const{type:y,props:b,shapeFlag:g,transition:m,dirs:_}=t;if(d=t.el=a(t.type,u,b&&b.is,b),8&g?h(d,t.children):16&g&&x(t.children,d,null,s,c,u&&"foreignObject"!==y,l,f),_&&ke(t,null,s,"created"),B(d,t,t.scopeId,l,s),b){for(const e in b)"value"===e||Object(r["B"])(e)||o(d,e,null,b[e],u,t.children,s,c,H);"value"in b&&o(d,"value",null,b.value),(p=b.onVnodeBeforeMount)&&Qr(p,s,t)}_&&ke(t,null,s,"beforeMount");const v=(!c||c&&!c.pendingBranch)&&m&&!m.persisted;v&&m.beforeEnter(d),i(d,e,n),((p=b&&b.onVnodeMounted)||v||_)&&gr(()=>{p&&Qr(p,s,t),v&&m.enter(d),_&&ke(t,null,s,"mounted")},c)},B=(t,e,n,r,i)=>{if(n&&p(t,n),r)for(let s=0;s<r.length;s++)p(t,r[s]);if(i){let n=i.subTree;if(e===n){const e=i.vnode;B(t,e,e.scopeId,e.slotScopeIds,i.parent)}}},x=(t,e,n,r,i,s,o,a,c=0)=>{for(let u=c;u<t.length;u++){const c=t[u]=a?Jr(t[u]):Gr(t[u]);b(null,c,e,n,r,i,s,o,a)}},j=(t,e,n,i,s,a,c)=>{const u=e.el=t.el;let{patchFlag:l,dynamicChildren:f,dirs:d}=e;l|=16&t.patchFlag;const p=t.props||r["b"],y=e.props||r["b"];let b;n&&vr(n,!1),(b=y.onVnodeBeforeUpdate)&&Qr(b,n,e,t),d&&ke(e,t,n,"beforeUpdate"),n&&vr(n,!0);const g=s&&"foreignObject"!==e.type;if(f?E(t.dynamicChildren,f,u,n,i,g,a):c||k(t,e,u,null,n,i,g,a,!1),l>0){if(16&l)D(u,e,p,y,n,i,s);else if(2&l&&p.class!==y.class&&o(u,"class",null,y.class,s),4&l&&o(u,"style",p.style,y.style,s),8&l){const r=e.dynamicProps;for(let e=0;e<r.length;e++){const a=r[e],c=p[a],l=y[a];l===c&&"value"!==a||o(u,a,c,l,s,t.children,n,i,H)}}1&l&&t.children!==e.children&&h(u,e.children)}else c||null!=f||D(u,e,p,y,n,i,s);((b=y.onVnodeUpdated)||d)&&gr(()=>{b&&Qr(b,n,e,t),d&&ke(e,t,n,"updated")},i)},E=(t,e,n,r,i,s,o)=>{for(let a=0;a<e.length;a++){const c=t[a],u=e[a],l=c.el&&(c.type===Sr||!kr(c,u)||70&c.shapeFlag)?f(c.el):n;b(c,u,l,null,r,i,s,o,!0)}},D=(t,e,n,i,s,a,c)=>{if(n!==i){if(n!==r["b"])for(const u in n)Object(r["B"])(u)||u in i||o(t,u,n[u],null,c,e.children,s,a,H);for(const u in i){if(Object(r["B"])(u))continue;const l=i[u],h=n[u];l!==h&&"value"!==u&&o(t,u,h,l,c,e.children,s,a,H)}"value"in i&&o(t,"value",n.value,i.value)}},L=(t,e,n,r,s,o,a,u,l)=>{const h=e.el=t?t.el:c(""),f=e.anchor=t?t.anchor:c("");let{patchFlag:d,dynamicChildren:p,slotScopeIds:y}=e;y&&(u=u?u.concat(y):y),null==t?(i(h,n,r),i(f,n,r),x(e.children,n,f,s,o,a,u,l)):d>0&&64&d&&p&&t.dynamicChildren?(E(t.dynamicChildren,p,n,s,o,a,u),(null!=e.key||s&&e===s.subTree)&&wr(t,e,!0)):k(t,e,n,f,s,o,a,u,l)},F=(t,e,n,r,i,s,o,a,c)=>{e.slotScopeIds=a,null==t?512&e.shapeFlag?i.ctx.activate(e,n,r,o,c):U(e,n,r,i,s,o,c):M(t,e,c)},U=(t,e,n,r,i,s,o)=>{const a=t.component=ni(t,r,i);if(Xe(t)&&(a.ctx.renderer=G),pi(a),a.asyncDep){if(i&&i.registerDep(a,C),!t.el){const t=a.subTree=$r(Tr);m(null,t,e,n)}}else C(a,t,e,n,i,s,o)},M=(t,e,n)=>{const r=e.component=t.component;if(Be(t,e,n)){if(r.asyncDep&&!r.asyncResolved)return void N(r,e,n);r.next=e,ce(r.update),r.update()}else e.el=t.el,r.vnode=e},C=(t,e,n,i,s,o,a)=>{const c=()=>{if(t.isMounted){let e,{next:n,bu:i,u:c,parent:u,vnode:l}=t,h=n;0,vr(t,!1),n?(n.el=l.el,N(t,n,a)):n=l,i&&Object(r["n"])(i),(e=n.props&&n.props.onVnodeBeforeUpdate)&&Qr(e,u,n,l),vr(t,!0);const d=Se(t);0;const p=t.subTree;t.subTree=d,b(p,d,f(p.el),q(p),t,s,o),n.el=d.el,null===h&&je(t,d.el),c&&gr(c,s),(e=n.props&&n.props.onVnodeUpdated)&&gr(()=>Qr(e,u,n,l),s)}else{let a;const{el:c,props:u}=e,{bm:l,m:h,parent:f}=t,d=Ze(e);if(vr(t,!1),l&&Object(r["n"])(l),!d&&(a=u&&u.onVnodeBeforeMount)&&Qr(a,f,e),vr(t,!0),c&&Z){const n=()=>{t.subTree=Se(t),Z(c,t.subTree,t,s,null)};d?e.type.__asyncLoader().then(()=>!t.isUnmounted&&n()):n()}else{0;const r=t.subTree=Se(t);0,b(null,r,n,i,t,s,o),e.el=r.el}if(h&&gr(h,s),!d&&(a=u&&u.onVnodeMounted)){const t=e;gr(()=>Qr(a,f,t),s)}(256&e.shapeFlag||f&&Ze(f.vnode)&&256&f.vnode.shapeFlag)&&t.a&&gr(t.a,s),t.isMounted=!0,e=n=i=null}},u=t.effect=new v(c,()=>oe(l),t.scope),l=t.update=()=>u.run();l.id=t.uid,vr(t,!0),l()},N=(t,e,n)=>{e.component=t;const r=t.vnode.props;t.vnode=e,t.next=null,tr(t,e.props,r,n),pr(t,e.children,n),S(),le(),A()},k=(t,e,n,r,i,s,o,a,c=!1)=>{const u=t&&t.children,l=t?t.shapeFlag:0,f=e.children,{patchFlag:d,shapeFlag:p}=e;if(d>0){if(128&d)return void V(u,f,n,r,i,s,o,a,c);if(256&d)return void R(u,f,n,r,i,s,o,a,c)}8&p?(16&l&&H(u,i,s),f!==u&&h(n,f)):16&l?16&p?V(u,f,n,r,i,s,o,a,c):H(u,i,s,!0):(8&l&&h(n,""),16&p&&x(f,n,r,i,s,o,a,c))},R=(t,e,n,i,s,o,a,c,u)=>{t=t||r["a"],e=e||r["a"];const l=t.length,h=e.length,f=Math.min(l,h);let d;for(d=0;d<f;d++){const r=e[d]=u?Jr(e[d]):Gr(e[d]);b(t[d],r,n,null,s,o,a,c,u)}l>h?H(t,s,o,!0,!1,f):x(e,n,i,s,o,a,c,u,f)},V=(t,e,n,i,s,o,a,c,u)=>{let l=0;const h=e.length;let f=t.length-1,d=h-1;while(l<=f&&l<=d){const r=t[l],i=e[l]=u?Jr(e[l]):Gr(e[l]);if(!kr(r,i))break;b(r,i,n,null,s,o,a,c,u),l++}while(l<=f&&l<=d){const r=t[f],i=e[d]=u?Jr(e[d]):Gr(e[d]);if(!kr(r,i))break;b(r,i,n,null,s,o,a,c,u),f--,d--}if(l>f){if(l<=d){const t=d+1,r=t<h?e[t].el:i;while(l<=d)b(null,e[l]=u?Jr(e[l]):Gr(e[l]),n,r,s,o,a,c,u),l++}}else if(l>d)while(l<=f)z(t[l],s,o,!0),l++;else{const p=l,y=l,g=new Map;for(l=y;l<=d;l++){const t=e[l]=u?Jr(e[l]):Gr(e[l]);null!=t.key&&g.set(t.key,l)}let m,_=0;const v=d-y+1;let w=!1,O=0;const I=new Array(v);for(l=0;l<v;l++)I[l]=0;for(l=p;l<=f;l++){const r=t[l];if(_>=v){z(r,s,o,!0);continue}let i;if(null!=r.key)i=g.get(r.key);else for(m=y;m<=d;m++)if(0===I[m-y]&&kr(r,e[m])){i=m;break}void 0===i?z(r,s,o,!0):(I[i-y]=l+1,i>=O?O=i:w=!0,b(r,e[i],n,null,s,o,a,c,u),_++)}const S=w?Or(I):r["a"];for(m=S.length-1,l=v-1;l>=0;l--){const t=y+l,r=e[t],f=t+1<h?e[t+1].el:i;0===I[l]?b(null,r,n,f,s,o,a,c,u):w&&(m<0||l!==S[m]?P(r,n,f,2):m--)}}},P=(t,e,n,r,s=null)=>{const{el:o,type:a,transition:c,children:u,shapeFlag:l}=t;if(6&l)return void P(t.component.subTree,e,n,r);if(128&l)return void t.suspense.move(e,n,r);if(64&l)return void a.move(t,e,n,G);if(a===Sr){i(o,e,n);for(let t=0;t<u.length;t++)P(u[t],e,n,r);return void i(t.anchor,e,n)}if(a===Br)return void w(t,e,n);const h=2!==r&&1&l&&c;if(h)if(0===r)c.beforeEnter(o),i(o,e,n),gr(()=>c.enter(o),s);else{const{leave:t,delayLeave:r,afterLeave:s}=c,a=()=>i(o,e,n),u=()=>{t(o,()=>{a(),s&&s()})};r?r(o,a,u):u()}else i(o,e,n)},z=(t,e,n,r=!1,i=!1)=>{const{type:s,props:o,ref:a,children:c,dynamicChildren:u,shapeFlag:l,patchFlag:h,dirs:f}=t;if(null!=a&&yr(a,null,n,t,!0),256&l)return void e.ctx.deactivate(t);const d=1&l&&f,p=!Ze(t);let y;if(p&&(y=o&&o.onVnodeBeforeUnmount)&&Qr(y,e,t),6&l)W(t.component,n,r);else{if(128&l)return void t.suspense.unmount(n,r);d&&ke(t,null,e,"beforeUnmount"),64&l?t.type.remove(t,e,n,i,G,r):u&&(s!==Sr||h>0&&64&h)?H(u,e,n,!1,!0):(s===Sr&&384&h||!i&&16&l)&&H(c,e,n),r&&$(t)}(p&&(y=o&&o.onVnodeUnmounted)||d)&&gr(()=>{y&&Qr(y,e,t),d&&ke(t,null,e,"unmounted")},n)},$=t=>{const{type:e,el:n,anchor:r,transition:i}=t;if(e===Sr)return void Y(n,r);if(e===Br)return void O(t);const o=()=>{s(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&t.shapeFlag&&i&&!i.persisted){const{leave:e,delayLeave:r}=i,s=()=>e(n,o);r?r(t.el,o,s):s()}else o()},Y=(t,e)=>{let n;while(t!==e)n=d(t),s(t),t=n;s(e)},W=(t,e,n)=>{const{bum:i,scope:s,update:o,subTree:a,um:c}=t;i&&Object(r["n"])(i),s.stop(),o&&(o.active=!1,z(a,t,e,n)),c&&gr(c,e),gr(()=>{t.isUnmounted=!0},e),e&&e.pendingBranch&&!e.isUnmounted&&t.asyncDep&&!t.asyncResolved&&t.suspenseId===e.pendingId&&(e.deps--,0===e.deps&&e.resolve())},H=(t,e,n,r=!1,i=!1,s=0)=>{for(let o=s;o<t.length;o++)z(t[o],e,n,r,i)},q=t=>6&t.shapeFlag?q(t.component.subTree):128&t.shapeFlag?t.suspense.next():d(t.anchor||t.el),K=(t,e,n)=>{null==t?e._vnode&&z(e._vnode,null,null,!0):b(e._vnode||null,t,e,null,null,null,n),le(),he(),e._vnode=t},G={p:b,um:z,m:P,r:$,mt:U,mc:x,pc:k,pbc:E,n:q,o:t};let J,Z;return e&&([J,Z]=e(G)),{render:K,hydrate:J,createApp:Gn(K,J)}}function vr({effect:t,update:e},n){t.allowRecurse=e.allowRecurse=n}function wr(t,e,n=!1){const i=t.children,s=e.children;if(Object(r["o"])(i)&&Object(r["o"])(s))for(let r=0;r<i.length;r++){const t=i[r];let e=s[r];1&e.shapeFlag&&!e.dynamicChildren&&((e.patchFlag<=0||32===e.patchFlag)&&(e=s[r]=Jr(s[r]),e.el=t.el),n||wr(t,e)),e.type===Ar&&(e.el=t.el)}}function Or(t){const e=t.slice(),n=[0];let r,i,s,o,a;const c=t.length;for(r=0;r<c;r++){const c=t[r];if(0!==c){if(i=n[n.length-1],t[i]<c){e[r]=i,n.push(r);continue}s=0,o=n.length-1;while(s<o)a=s+o>>1,t[n[a]]<c?s=a+1:o=a;c<t[n[s]]&&(s>0&&(e[r]=n[s-1]),n[s]=r)}}s=n.length,o=n[s-1];while(s-- >0)n[s]=o,o=e[o];return n}const Ir=t=>t.__isTeleport;const Sr=Symbol.for("v-fgt"),Ar=Symbol.for("v-txt"),Tr=Symbol.for("v-cmt"),Br=Symbol.for("v-stc"),xr=[];let jr=null;function Er(t=!1){xr.push(jr=t?null:[])}function Dr(){xr.pop(),jr=xr[xr.length-1]||null}let Lr=1;function Fr(t){Lr+=t}function Ur(t){return t.dynamicChildren=Lr>0?jr||r["a"]:null,Dr(),Lr>0&&jr&&jr.push(t),t}function Mr(t,e,n,r,i,s){return Ur(zr(t,e,n,r,i,s,!0))}function Cr(t,e,n,r,i){return Ur($r(t,e,n,r,i,!0))}function Nr(t){return!!t&&!0===t.__v_isVNode}function kr(t,e){return t.type===e.type&&t.key===e.key}const Rr="__vInternal",Vr=({key:t})=>null!=t?t:null,Pr=({ref:t,ref_key:e,ref_for:n})=>("number"===typeof t&&(t=""+t),null!=t?Object(r["F"])(t)||Ct(t)||Object(r["q"])(t)?{i:me,r:t,k:e,f:!!n}:t:null);function zr(t,e=null,n=null,i=0,s=null,o=(t===Sr?0:1),a=!1,c=!1){const u={__v_isVNode:!0,__v_skip:!0,type:t,props:e,key:e&&Vr(e),ref:e&&Pr(e),scopeId:_e,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:i,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:me};return c?(Zr(u,n),128&o&&t.normalize(u)):n&&(u.shapeFlag|=Object(r["F"])(n)?8:16),Lr>0&&!a&&jr&&(u.patchFlag>0||6&o)&&32!==u.patchFlag&&jr.push(u),u}const $r=Yr;function Yr(t,e=null,n=null,i=0,s=null,o=!1){if(t&&t!==wn||(t=Tr),Nr(t)){const r=Hr(t,e,!0);return n&&Zr(r,n),Lr>0&&!o&&jr&&(6&r.shapeFlag?jr[jr.indexOf(t)]=r:jr.push(r)),r.patchFlag|=-2,r}if(Oi(t)&&(t=t.__vccOpts),e){e=Wr(e);let{class:t,style:n}=e;t&&!Object(r["F"])(t)&&(e.class=Object(r["L"])(t)),Object(r["w"])(n)&&(jt(n)&&!Object(r["o"])(n)&&(n=Object(r["h"])({},n)),e.style=Object(r["M"])(n))}const a=Object(r["F"])(t)?1:Ee(t)?128:Ir(t)?64:Object(r["w"])(t)?4:Object(r["q"])(t)?2:0;return zr(t,e,n,i,s,a,o,!0)}function Wr(t){return t?jt(t)||Rr in t?Object(r["h"])({},t):t:null}function Hr(t,e,n=!1){const{props:i,ref:s,patchFlag:o,children:a}=t,c=e?Xr(i||{},e):i,u={__v_isVNode:!0,__v_skip:!0,type:t.type,props:c,key:c&&Vr(c),ref:e&&e.ref?n&&s?Object(r["o"])(s)?s.concat(Pr(e)):[s,Pr(e)]:Pr(e):s,scopeId:t.scopeId,slotScopeIds:t.slotScopeIds,children:a,target:t.target,targetAnchor:t.targetAnchor,staticCount:t.staticCount,shapeFlag:t.shapeFlag,patchFlag:e&&t.type!==Sr?-1===o?16:16|o:o,dynamicProps:t.dynamicProps,dynamicChildren:t.dynamicChildren,appContext:t.appContext,dirs:t.dirs,transition:t.transition,component:t.component,suspense:t.suspense,ssContent:t.ssContent&&Hr(t.ssContent),ssFallback:t.ssFallback&&Hr(t.ssFallback),el:t.el,anchor:t.anchor,ctx:t.ctx,ce:t.ce};return u}function qr(t=" ",e=0){return $r(Ar,null,t,e)}function Kr(t="",e=!1){return e?(Er(),Cr(Tr,null,t)):$r(Tr,null,t)}function Gr(t){return null==t||"boolean"===typeof t?$r(Tr):Object(r["o"])(t)?$r(Sr,null,t.slice()):"object"===typeof t?Jr(t):$r(Ar,null,String(t))}function Jr(t){return null===t.el&&-1!==t.patchFlag||t.memo?t:Hr(t)}function Zr(t,e){let n=0;const{shapeFlag:i}=t;if(null==e)e=null;else if(Object(r["o"])(e))n=16;else if("object"===typeof e){if(65&i){const n=e.default;return void(n&&(n._c&&(n._d=!1),Zr(t,n()),n._c&&(n._d=!0)))}{n=32;const r=e._;r||Rr in e?3===r&&me&&(1===me.slots._?e._=1:(e._=2,t.patchFlag|=1024)):e._ctx=me}}else Object(r["q"])(e)?(e={default:e,_ctx:me},n=32):(e=String(e),64&i?(n=16,e=[qr(e)]):n=8);t.children=e,t.shapeFlag|=n}function Xr(...t){const e={};for(let n=0;n<t.length;n++){const i=t[n];for(const t in i)if("class"===t)e.class!==i.class&&(e.class=Object(r["L"])([e.class,i.class]));else if("style"===t)e.style=Object(r["M"])([e.style,i.style]);else if(Object(r["x"])(t)){const n=e[t],s=i[t];!s||n===s||Object(r["o"])(n)&&n.includes(s)||(e[t]=n?[].concat(n,s):s)}else""!==t&&(e[t]=i[t])}return e}function Qr(t,e,n,r=null){Ht(t,e,7,[n,r])}const ti=qn();let ei=0;function ni(t,e,n){const i=t.type,o=(e?e.appContext:t.appContext)||ti,a={uid:ei++,vnode:t,type:i,parent:e,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new s(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:e?e.provides:Object.create(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:rr(i,o),emitsOptions:be(i,o),emit:null,emitted:null,propsDefaults:r["b"],inheritAttrs:i.inheritAttrs,ctx:r["b"],data:r["b"],props:r["b"],attrs:r["b"],slots:r["b"],refs:r["b"],setupState:r["b"],setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return a.ctx={_:a},a.root=e?e.root:a,a.emit=ye.bind(null,a),t.ce&&t.ce(a),a}let ri=null;const ii=()=>ri||me;let si,oi,ai="__VUE_INSTANCE_SETTERS__";(oi=Object(r["i"])()[ai])||(oi=Object(r["i"])()[ai]=[]),oi.push(t=>ri=t),si=t=>{oi.length>1?oi.forEach(e=>e(t)):oi[0](t)};const ci=t=>{si(t),t.scope.on()},ui=()=>{ri&&ri.scope.off(),si(null)};function li(t){return 4&t.vnode.shapeFlag}let hi,fi,di=!1;function pi(t,e=!1){di=e;const{props:n,children:r}=t.vnode,i=li(t);Qn(t,n,i,e),dr(t,r);const s=i?yi(t,e):void 0;return di=!1,s}function yi(t,e){const n=t.type;t.accessCache=Object.create(null),t.proxy=Dt(new Proxy(t.ctx,En));const{setup:i}=n;if(i){const n=t.setupContext=i.length>1?_i(t):null;ci(t),S();const s=Wt(i,t,0,[t.props,n]);if(A(),ui(),Object(r["z"])(s)){if(s.then(ui,ui),e)return s.then(n=>{bi(t,n,e)}).catch(e=>{qt(e,t,0)});t.asyncDep=s}else bi(t,s,e)}else gi(t,e)}function bi(t,e,n){Object(r["q"])(e)?t.type.__ssrInlineRender?t.ssrRender=e:t.render=e:Object(r["w"])(e)&&(t.setupState=zt(e)),gi(t,n)}function gi(t,e,n){const i=t.type;if(!t.render){if(!e&&hi&&!i.render){const e=i.template||Nn(t).template;if(e){0;const{isCustomElement:n,compilerOptions:s}=t.appContext.config,{delimiters:o,compilerOptions:a}=i,c=Object(r["h"])(Object(r["h"])({isCustomElement:n,delimiters:o},s),a);i.render=hi(e,c)}}t.render=i.render||r["d"],fi&&fi(t)}ci(t),S(),Fn(t),A(),ui()}function mi(t){return t.attrsProxy||(t.attrsProxy=new Proxy(t.attrs,{get(e,n){return T(t,"get","$attrs"),e[n]}}))}function _i(t){const e=e=>{t.exposed=e||{}};return{get attrs(){return mi(t)},slots:t.slots,emit:t.emit,expose:e}}function vi(t){if(t.exposed)return t.exposeProxy||(t.exposeProxy=new Proxy(zt(Dt(t.exposed)),{get(e,n){return n in e?e[n]:n in xn?xn[n](t):void 0},has(t,e){return e in t||e in xn}}))}function wi(t,e=!0){return Object(r["q"])(t)?t.displayName||t.name:t.name||e&&t.__name}function Oi(t){return Object(r["q"])(t)&&"__vccOpts"in t}const Ii=(t,e)=>Yt(t,e,di);function Si(t,e,n){const i=arguments.length;return 2===i?Object(r["w"])(e)&&!Object(r["o"])(e)?Nr(e)?$r(t,null,[e]):$r(t,e):$r(t,null,e):(i>3?n=Array.prototype.slice.call(arguments,2):3===i&&Nr(n)&&(n=[n]),$r(t,e,n))}const Ai=Symbol.for("v-scx"),Ti=()=>{{const t=Xn(Ai);return t}};const Bi="3.3.2",xi="http://www.w3.org/2000/svg",ji="undefined"!==typeof document?document:null,Ei=ji&&ji.createElement("template"),Di={insert:(t,e,n)=>{e.insertBefore(t,n||null)},remove:t=>{const e=t.parentNode;e&&e.removeChild(t)},createElement:(t,e,n,r)=>{const i=e?ji.createElementNS(xi,t):ji.createElement(t,n?{is:n}:void 0);return"select"===t&&r&&null!=r.multiple&&i.setAttribute("multiple",r.multiple),i},createText:t=>ji.createTextNode(t),createComment:t=>ji.createComment(t),setText:(t,e)=>{t.nodeValue=e},setElementText:(t,e)=>{t.textContent=e},parentNode:t=>t.parentNode,nextSibling:t=>t.nextSibling,querySelector:t=>ji.querySelector(t),setScopeId(t,e){t.setAttribute(e,"")},insertStaticContent(t,e,n,r,i,s){const o=n?n.previousSibling:e.lastChild;if(i&&(i===s||i.nextSibling)){while(1)if(e.insertBefore(i.cloneNode(!0),n),i===s||!(i=i.nextSibling))break}else{Ei.innerHTML=r?`<svg>${t}</svg>`:t;const i=Ei.content;if(r){const t=i.firstChild;while(t.firstChild)i.appendChild(t.firstChild);i.removeChild(t)}e.insertBefore(i,n)}return[o?o.nextSibling:e.firstChild,n?n.previousSibling:e.lastChild]}};function Li(t,e,n){const r=t._vtc;r&&(e=(e?[e,...r]:[...r]).join(" ")),null==e?t.removeAttribute("class"):n?t.setAttribute("class",e):t.className=e}function Fi(t,e,n){const i=t.style,s=Object(r["F"])(n);if(n&&!s){if(e&&!Object(r["F"])(e))for(const t in e)null==n[t]&&Mi(i,t,"");for(const t in n)Mi(i,t,n[t])}else{const r=i.display;s?e!==n&&(i.cssText=n):e&&t.removeAttribute("style"),"_vod"in t&&(i.display=r)}}const Ui=/\s*!important$/;function Mi(t,e,n){if(Object(r["o"])(n))n.forEach(n=>Mi(t,e,n));else if(null==n&&(n=""),e.startsWith("--"))t.setProperty(e,n);else{const i=ki(t,e);Ui.test(n)?t.setProperty(Object(r["l"])(i),n.replace(Ui,""),"important"):t[i]=n}}const Ci=["Webkit","Moz","ms"],Ni={};function ki(t,e){const n=Ni[e];if(n)return n;let i=Object(r["e"])(e);if("filter"!==i&&i in t)return Ni[e]=i;i=Object(r["f"])(i);for(let r=0;r<Ci.length;r++){const n=Ci[r]+i;if(n in t)return Ni[e]=n}return e}const Ri="http://www.w3.org/1999/xlink";function Vi(t,e,n,i,s){if(i&&e.startsWith("xlink:"))null==n?t.removeAttributeNS(Ri,e.slice(6,e.length)):t.setAttributeNS(Ri,e,n);else{const i=Object(r["E"])(e);null==n||i&&!Object(r["m"])(n)?t.removeAttribute(e):t.setAttribute(e,i?"":n)}}function Pi(t,e,n,i,s,o,a){if("innerHTML"===e||"textContent"===e)return i&&a(i,s,o),void(t[e]=null==n?"":n);const c=t.tagName;if("value"===e&&"PROGRESS"!==c&&!c.includes("-")){t._value=n;const r="OPTION"===c?t.getAttribute("value"):t.value,i=null==n?"":n;return r!==i&&(t.value=i),void(null==n&&t.removeAttribute(e))}let u=!1;if(""===n||null==n){const i=typeof t[e];"boolean"===i?n=Object(r["m"])(n):null==n&&"string"===i?(n="",u=!0):"number"===i&&(n=0,u=!0)}try{t[e]=n}catch(l){0}u&&t.removeAttribute(e)}function zi(t,e,n,r){t.addEventListener(e,n,r)}function $i(t,e,n,r){t.removeEventListener(e,n,r)}function Yi(t,e,n,r,i=null){const s=t._vei||(t._vei={}),o=s[e];if(r&&o)o.value=r;else{const[n,a]=Hi(e);if(r){const o=s[e]=Ji(r,i);zi(t,n,o,a)}else o&&($i(t,n,o,a),s[e]=void 0)}}const Wi=/(?:Once|Passive|Capture)$/;function Hi(t){let e;if(Wi.test(t)){let n;e={};while(n=t.match(Wi))t=t.slice(0,t.length-n[0].length),e[n[0].toLowerCase()]=!0}const n=":"===t[2]?t.slice(3):Object(r["l"])(t.slice(2));return[n,e]}let qi=0;const Ki=Promise.resolve(),Gi=()=>qi||(Ki.then(()=>qi=0),qi=Date.now());function Ji(t,e){const n=t=>{if(t._vts){if(t._vts<=n.attached)return}else t._vts=Date.now();Ht(Zi(t,n.value),e,5,[t])};return n.value=t,n.attached=Gi(),n}function Zi(t,e){if(Object(r["o"])(e)){const n=t.stopImmediatePropagation;return t.stopImmediatePropagation=()=>{n.call(t),t._stopped=!0},e.map(t=>e=>!e._stopped&&t&&t(e))}return e}const Xi=/^on[a-z]/,Qi=(t,e,n,i,s=!1,o,a,c,u)=>{"class"===e?Li(t,i,s):"style"===e?Fi(t,n,i):Object(r["x"])(e)?Object(r["v"])(e)||Yi(t,e,n,i,a):("."===e[0]?(e=e.slice(1),1):"^"===e[0]?(e=e.slice(1),0):ts(t,e,i,s))?Pi(t,e,i,o,a,c,u):("true-value"===e?t._trueValue=i:"false-value"===e&&(t._falseValue=i),Vi(t,e,i,s))};function ts(t,e,n,i){return i?"innerHTML"===e||"textContent"===e||!!(e in t&&Xi.test(e)&&Object(r["q"])(n)):"spellcheck"!==e&&"draggable"!==e&&"translate"!==e&&("form"!==e&&(("list"!==e||"INPUT"!==t.tagName)&&(("type"!==e||"TEXTAREA"!==t.tagName)&&((!Xi.test(e)||!Object(r["F"])(n))&&e in t))))}"undefined"!==typeof HTMLElement&&HTMLElement;const es="transition",ns="animation",rs=(t,{slots:e})=>Si($e,cs(t),e);rs.displayName="Transition";const is={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},ss=rs.props=Object(r["h"])({},Pe,is),os=(t,e=[])=>{Object(r["o"])(t)?t.forEach(t=>t(...e)):t&&t(...e)},as=t=>!!t&&(Object(r["o"])(t)?t.some(t=>t.length>1):t.length>1);function cs(t){const e={};for(const r in t)r in is||(e[r]=t[r]);if(!1===t.css)return e;const{name:n="v",type:i,duration:s,enterFromClass:o=n+"-enter-from",enterActiveClass:a=n+"-enter-active",enterToClass:c=n+"-enter-to",appearFromClass:u=o,appearActiveClass:l=a,appearToClass:h=c,leaveFromClass:f=n+"-leave-from",leaveActiveClass:d=n+"-leave-active",leaveToClass:p=n+"-leave-to"}=t,y=us(s),b=y&&y[0],g=y&&y[1],{onBeforeEnter:m,onEnter:_,onEnterCancelled:v,onLeave:w,onLeaveCancelled:O,onBeforeAppear:I=m,onAppear:S=_,onAppearCancelled:A=v}=e,T=(t,e,n)=>{fs(t,e?h:c),fs(t,e?l:a),n&&n()},B=(t,e)=>{t._isLeaving=!1,fs(t,f),fs(t,p),fs(t,d),e&&e()},x=t=>(e,n)=>{const r=t?S:_,s=()=>T(e,t,n);os(r,[e,s]),ds(()=>{fs(e,t?u:o),hs(e,t?h:c),as(r)||ys(e,i,b,s)})};return Object(r["h"])(e,{onBeforeEnter(t){os(m,[t]),hs(t,o),hs(t,a)},onBeforeAppear(t){os(I,[t]),hs(t,u),hs(t,l)},onEnter:x(!1),onAppear:x(!0),onLeave(t,e){t._isLeaving=!0;const n=()=>B(t,e);hs(t,f),_s(),hs(t,d),ds(()=>{t._isLeaving&&(fs(t,f),hs(t,p),as(w)||ys(t,i,g,n))}),os(w,[t,n])},onEnterCancelled(t){T(t,!1),os(v,[t])},onAppearCancelled(t){T(t,!0),os(A,[t])},onLeaveCancelled(t){B(t),os(O,[t])}})}function us(t){if(null==t)return null;if(Object(r["w"])(t))return[ls(t.enter),ls(t.leave)];{const e=ls(t);return[e,e]}}function ls(t){const e=Object(r["Q"])(t);return e}function hs(t,e){e.split(/\s+/).forEach(e=>e&&t.classList.add(e)),(t._vtc||(t._vtc=new Set)).add(e)}function fs(t,e){e.split(/\s+/).forEach(e=>e&&t.classList.remove(e));const{_vtc:n}=t;n&&(n.delete(e),n.size||(t._vtc=void 0))}function ds(t){requestAnimationFrame(()=>{requestAnimationFrame(t)})}let ps=0;function ys(t,e,n,r){const i=t._endId=++ps,s=()=>{i===t._endId&&r()};if(n)return setTimeout(s,n);const{type:o,timeout:a,propCount:c}=bs(t,e);if(!o)return r();const u=o+"end";let l=0;const h=()=>{t.removeEventListener(u,f),s()},f=e=>{e.target===t&&++l>=c&&h()};setTimeout(()=>{l<c&&h()},a+1),t.addEventListener(u,f)}function bs(t,e){const n=window.getComputedStyle(t),r=t=>(n[t]||"").split(", "),i=r(es+"Delay"),s=r(es+"Duration"),o=gs(i,s),a=r(ns+"Delay"),c=r(ns+"Duration"),u=gs(a,c);let l=null,h=0,f=0;e===es?o>0&&(l=es,h=o,f=s.length):e===ns?u>0&&(l=ns,h=u,f=c.length):(h=Math.max(o,u),l=h>0?o>u?es:ns:null,f=l?l===es?s.length:c.length:0);const d=l===es&&/\b(transform|all)(,|$)/.test(r(es+"Property").toString());return{type:l,timeout:h,propCount:f,hasTransform:d}}function gs(t,e){while(t.length<e.length)t=t.concat(t);return Math.max(...e.map((e,n)=>ms(e)+ms(t[n])))}function ms(t){return 1e3*Number(t.slice(0,-1).replace(",","."))}function _s(){return document.body.offsetHeight}const vs=new WeakMap,ws=new WeakMap,Os={name:"TransitionGroup",props:Object(r["h"])({},ss,{tag:String,moveClass:String}),setup(t,{slots:e}){const n=ii(),r=Re();let i,s;return fn(()=>{if(!i.length)return;const e=t.moveClass||(t.name||"v")+"-move";if(!Ts(i[0].el,n.vnode.el,e))return;i.forEach(Is),i.forEach(Ss);const r=i.filter(As);_s(),r.forEach(t=>{const n=t.el,r=n.style;hs(n,e),r.transform=r.webkitTransform=r.transitionDuration="";const i=n._moveCb=t=>{t&&t.target!==n||t&&!/transform$/.test(t.propertyName)||(n.removeEventListener("transitionend",i),n._moveCb=null,fs(n,e))};n.addEventListener("transitionend",i)})}),()=>{const o=Et(t),a=cs(o);let c=o.tag||Sr;i=s,s=e.default?Ge(e.default()):[];for(let t=0;t<s.length;t++){const e=s[t];null!=e.key&&Ke(e,We(e,a,r,n))}if(i)for(let t=0;t<i.length;t++){const e=i[t];Ke(e,We(e,a,r,n)),vs.set(e,e.el.getBoundingClientRect())}return $r(c,null,s)}}};Os.props;function Is(t){const e=t.el;e._moveCb&&e._moveCb(),e._enterCb&&e._enterCb()}function Ss(t){ws.set(t,t.el.getBoundingClientRect())}function As(t){const e=vs.get(t),n=ws.get(t),r=e.left-n.left,i=e.top-n.top;if(r||i){const e=t.el.style;return e.transform=e.webkitTransform=`translate(${r}px,${i}px)`,e.transitionDuration="0s",t}}function Ts(t,e,n){const r=t.cloneNode();t._vtc&&t._vtc.forEach(t=>{t.split(/\s+/).forEach(t=>t&&r.classList.remove(t))}),n.split(/\s+/).forEach(t=>t&&r.classList.add(t)),r.style.display="none";const i=1===e.nodeType?e:e.parentNode;i.appendChild(r);const{hasTransform:s}=bs(r);return i.removeChild(r),s}const Bs=Object(r["h"])({patchProp:Qi},Di);let xs;function js(){return xs||(xs=mr(Bs))}const Es=(...t)=>{const e=js().createApp(...t);const{mount:n}=e;return e.mount=t=>{const i=Ds(t);if(!i)return;const s=e._component;Object(r["q"])(s)||s.render||s.template||(s.template=i.innerHTML),i.innerHTML="";const o=n(i,!1,i instanceof SVGElement);return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),o},e};function Ds(t){if(Object(r["F"])(t)){const e=document.querySelector(t);return e}return t}},"841f":function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"b",(function(){return o})),n.d(e,"d",(function(){return c})),n.d(e,"k",(function(){return d})),n.d(e,"l",(function(){return p})),n.d(e,"i",(function(){return y})),n.d(e,"f",(function(){return b})),n.d(e,"e",(function(){return g})),n.d(e,"j",(function(){return m})),n.d(e,"h",(function(){return _})),n.d(e,"g",(function(){return v})),n.d(e,"o",(function(){return w})),n.d(e,"m",(function(){return O})),n.d(e,"p",(function(){return I})),n.d(e,"n",(function(){return S}));var r=n("f673");const[i,s]=(()=>{const t=()=>{throw new Error("BigInt is not available in this environment")};function e(){throw t()}return e.asIntN=()=>{throw t()},e.asUintN=()=>{throw t()},"undefined"!==typeof BigInt?[BigInt,!0]:[e,!1]})(),[o,a]=(()=>{const t=()=>{throw new Error("BigInt64Array is not available in this environment")};class e{static get BYTES_PER_ELEMENT(){return 8}static of(){throw t()}static from(){throw t()}constructor(){throw t()}}return"undefined"!==typeof BigInt64Array?[BigInt64Array,!0]:[e,!1]})(),[c,u]=(()=>{const t=()=>{throw new Error("BigUint64Array is not available in this environment")};class e{static get BYTES_PER_ELEMENT(){return 8}static of(){throw t()}static from(){throw t()}constructor(){throw t()}}return"undefined"!==typeof BigUint64Array?[BigUint64Array,!0]:[e,!1]})(),l=t=>"number"===typeof t,h=t=>"boolean"===typeof t,f=t=>"function"===typeof t,d=t=>null!=t&&Object(t)===t,p=t=>d(t)&&f(t.then),y=t=>d(t)&&f(t[Symbol.iterator]),b=t=>d(t)&&f(t[Symbol.asyncIterator]),g=t=>d(t)&&d(t["schema"]),m=t=>d(t)&&"done"in t&&"value"in t,_=t=>d(t)&&f(t["stat"])&&l(t["fd"]),v=t=>d(t)&&O(t["body"]),w=t=>d(t)&&f(t["abort"])&&f(t["getWriter"])&&!(t instanceof r["d"]),O=t=>d(t)&&f(t["cancel"])&&f(t["getReader"])&&!(t instanceof r["d"]),I=t=>d(t)&&f(t["end"])&&f(t["write"])&&h(t["writable"])&&!(t instanceof r["d"]),S=t=>d(t)&&f(t["read"])&&f(t["pipe"])&&h(t["readable"])&&!(t instanceof r["d"])},9152:function(t,e){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
e.read=function(t,e,n,r,i){var s,o,a=8*i-r-1,c=(1<<a)-1,u=c>>1,l=-7,h=n?i-1:0,f=n?-1:1,d=t[e+h];for(h+=f,s=d&(1<<-l)-1,d>>=-l,l+=a;l>0;s=256*s+t[e+h],h+=f,l-=8);for(o=s&(1<<-l)-1,s>>=-l,l+=r;l>0;o=256*o+t[e+h],h+=f,l-=8);if(0===s)s=1-u;else{if(s===c)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,r),s-=u}return(d?-1:1)*o*Math.pow(2,s-r)},e.write=function(t,e,n,r,i,s){var o,a,c,u=8*s-i-1,l=(1<<u)-1,h=l>>1,f=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:s-1,p=r?1:-1,y=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,o=l):(o=Math.floor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-o))<1&&(o--,c*=2),e+=o+h>=1?f/c:f*Math.pow(2,1-h),e*c>=2&&(o++,c/=2),o+h>=l?(a=0,o=l):o+h>=1?(a=(e*c-1)*Math.pow(2,i),o+=h):(a=e*Math.pow(2,h-1)*Math.pow(2,i),o=0));i>=8;t[n+d]=255&a,d+=p,a/=256,i-=8);for(o=o<<i|a,u+=i;u>0;t[n+d]=255&o,d+=p,o/=256,u-=8);t[n+d-p]|=128*y}},"9ff4":function(t,e,n){"use strict";(function(t){function r(t,e){const n=Object.create(null),r=t.split(",");for(let i=0;i<r.length;i++)n[r[i]]=!0;return e?t=>!!n[t.toLowerCase()]:t=>!!n[t]}n.d(e,"a",(function(){return s})),n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"d",(function(){return o})),n.d(e,"e",(function(){return U})),n.d(e,"f",(function(){return N})),n.d(e,"g",(function(){return P})),n.d(e,"h",(function(){return h})),n.d(e,"i",(function(){return W})),n.d(e,"j",(function(){return R})),n.d(e,"k",(function(){return p})),n.d(e,"l",(function(){return C})),n.d(e,"m",(function(){return ot})),n.d(e,"n",(function(){return V})),n.d(e,"o",(function(){return y})),n.d(e,"p",(function(){return D})),n.d(e,"q",(function(){return v})),n.d(e,"r",(function(){return q})),n.d(e,"s",(function(){return nt})),n.d(e,"t",(function(){return j})),n.d(e,"u",(function(){return b})),n.d(e,"v",(function(){return l})),n.d(e,"w",(function(){return I})),n.d(e,"x",(function(){return u})),n.d(e,"y",(function(){return x})),n.d(e,"z",(function(){return S})),n.d(e,"A",(function(){return _})),n.d(e,"B",(function(){return E})),n.d(e,"C",(function(){return rt})),n.d(e,"D",(function(){return g})),n.d(e,"E",(function(){return st})),n.d(e,"F",(function(){return w})),n.d(e,"G",(function(){return O})),n.d(e,"H",(function(){return ct})),n.d(e,"I",(function(){return ut})),n.d(e,"J",(function(){return z})),n.d(e,"K",(function(){return r})),n.d(e,"L",(function(){return Q})),n.d(e,"M",(function(){return K})),n.d(e,"N",(function(){return f})),n.d(e,"O",(function(){return lt})),n.d(e,"P",(function(){return k})),n.d(e,"Q",(function(){return $})),n.d(e,"R",(function(){return B}));const i={},s=[],o=()=>{},a=()=>!1,c=/^on[^a-z]/,u=t=>c.test(t),l=t=>t.startsWith("onUpdate:"),h=Object.assign,f=(t,e)=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)},d=Object.prototype.hasOwnProperty,p=(t,e)=>d.call(t,e),y=Array.isArray,b=t=>"[object Map]"===T(t),g=t=>"[object Set]"===T(t),m=t=>"[object Date]"===T(t),_=t=>"[object RegExp]"===T(t),v=t=>"function"===typeof t,w=t=>"string"===typeof t,O=t=>"symbol"===typeof t,I=t=>null!==t&&"object"===typeof t,S=t=>I(t)&&v(t.then)&&v(t.catch),A=Object.prototype.toString,T=t=>A.call(t),B=t=>T(t).slice(8,-1),x=t=>"[object Object]"===T(t),j=t=>w(t)&&"NaN"!==t&&"-"!==t[0]&&""+parseInt(t,10)===t,E=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),D=r("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),L=t=>{const e=Object.create(null);return n=>{const r=e[n];return r||(e[n]=t(n))}},F=/-(\w)/g,U=L(t=>t.replace(F,(t,e)=>e?e.toUpperCase():"")),M=/\B([A-Z])/g,C=L(t=>t.replace(M,"-$1").toLowerCase()),N=L(t=>t.charAt(0).toUpperCase()+t.slice(1)),k=L(t=>t?"on"+N(t):""),R=(t,e)=>!Object.is(t,e),V=(t,e)=>{for(let n=0;n<t.length;n++)t[n](e)},P=(t,e,n)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:n})},z=t=>{const e=parseFloat(t);return isNaN(e)?t:e},$=t=>{const e=w(t)?Number(t):NaN;return isNaN(e)?t:e};let Y;const W=()=>Y||(Y="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof t?t:{});const H="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console",q=r(H);function K(t){if(y(t)){const e={};for(let n=0;n<t.length;n++){const r=t[n],i=w(r)?X(r):K(r);if(i)for(const t in i)e[t]=i[t]}return e}return w(t)||I(t)?t:void 0}const G=/;(?![^(]*\))/g,J=/:([^]+)/,Z=new RegExp("\\/\\*.*?\\*\\/","gs");function X(t){const e={};return t.replace(Z,"").split(G).forEach(t=>{if(t){const n=t.split(J);n.length>1&&(e[n[0].trim()]=n[1].trim())}}),e}function Q(t){let e="";if(w(t))e=t;else if(y(t))for(let n=0;n<t.length;n++){const r=Q(t[n]);r&&(e+=r+" ")}else if(I(t))for(const n in t)t[n]&&(e+=n+" ");return e.trim()}const tt="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",et="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",nt=r(tt),rt=r(et),it="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",st=r(it);function ot(t){return!!t||""===t}function at(t,e){if(t.length!==e.length)return!1;let n=!0;for(let r=0;n&&r<t.length;r++)n=ct(t[r],e[r]);return n}function ct(t,e){if(t===e)return!0;let n=m(t),r=m(e);if(n||r)return!(!n||!r)&&t.getTime()===e.getTime();if(n=O(t),r=O(e),n||r)return t===e;if(n=y(t),r=y(e),n||r)return!(!n||!r)&&at(t,e);if(n=I(t),r=I(e),n||r){if(!n||!r)return!1;const i=Object.keys(t).length,s=Object.keys(e).length;if(i!==s)return!1;for(const n in t){const r=t.hasOwnProperty(n),i=e.hasOwnProperty(n);if(r&&!i||!r&&i||!ct(t[n],e[n]))return!1}}return String(t)===String(e)}function ut(t,e){return t.findIndex(t=>ct(t,e))}const lt=t=>w(t)?t:null==t?"":y(t)||I(t)&&(t.toString===A||!v(t.toString))?JSON.stringify(t,ht,2):String(t),ht=(t,e)=>e&&e.__v_isRef?ht(t,e.value):b(e)?{[`Map(${e.size})`]:[...e.entries()].reduce((t,[e,n])=>(t[e+" =>"]=n,t),{})}:g(e)?{[`Set(${e.size})`]:[...e.values()]}:!I(e)||y(e)||x(e)?e:String(e)}).call(this,n("c8ba"))},a6b2:function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var r={};r.Offset,r.Table,r.SIZEOF_SHORT=2,r.SIZEOF_INT=4,r.FILE_IDENTIFIER_LENGTH=4,r.Encoding={UTF8_BYTES:1,UTF16_STRING:2},r.int32=new Int32Array(2),r.float32=new Float32Array(r.int32.buffer),r.float64=new Float64Array(r.int32.buffer),r.isLittleEndian=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0],r.Long=function(t,e){this.low=0|t,this.high=0|e},r.Long.create=function(t,e){return 0==t&&0==e?r.Long.ZERO:new r.Long(t,e)},r.Long.prototype.toFloat64=function(){return(this.low>>>0)+4294967296*this.high},r.Long.prototype.equals=function(t){return this.low==t.low&&this.high==t.high},r.Long.ZERO=new r.Long(0,0),r.Builder=function(t){if(t)e=t;else var e=1024;this.bb=r.ByteBuffer.allocate(e),this.space=e,this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1},r.Builder.prototype.clear=function(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1},r.Builder.prototype.forceDefaults=function(t){this.force_defaults=t},r.Builder.prototype.dataBuffer=function(){return this.bb},r.Builder.prototype.asUint8Array=function(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())},r.Builder.prototype.prep=function(t,e){t>this.minalign&&(this.minalign=t);var n=1+~(this.bb.capacity()-this.space+e)&t-1;while(this.space<n+t+e){var i=this.bb.capacity();this.bb=r.Builder.growByteBuffer(this.bb),this.space+=this.bb.capacity()-i}this.pad(n)},r.Builder.prototype.pad=function(t){for(var e=0;e<t;e++)this.bb.writeInt8(--this.space,0)},r.Builder.prototype.writeInt8=function(t){this.bb.writeInt8(this.space-=1,t)},r.Builder.prototype.writeInt16=function(t){this.bb.writeInt16(this.space-=2,t)},r.Builder.prototype.writeInt32=function(t){this.bb.writeInt32(this.space-=4,t)},r.Builder.prototype.writeInt64=function(t){this.bb.writeInt64(this.space-=8,t)},r.Builder.prototype.writeFloat32=function(t){this.bb.writeFloat32(this.space-=4,t)},r.Builder.prototype.writeFloat64=function(t){this.bb.writeFloat64(this.space-=8,t)},r.Builder.prototype.addInt8=function(t){this.prep(1,0),this.writeInt8(t)},r.Builder.prototype.addInt16=function(t){this.prep(2,0),this.writeInt16(t)},r.Builder.prototype.addInt32=function(t){this.prep(4,0),this.writeInt32(t)},r.Builder.prototype.addInt64=function(t){this.prep(8,0),this.writeInt64(t)},r.Builder.prototype.addFloat32=function(t){this.prep(4,0),this.writeFloat32(t)},r.Builder.prototype.addFloat64=function(t){this.prep(8,0),this.writeFloat64(t)},r.Builder.prototype.addFieldInt8=function(t,e,n){(this.force_defaults||e!=n)&&(this.addInt8(e),this.slot(t))},r.Builder.prototype.addFieldInt16=function(t,e,n){(this.force_defaults||e!=n)&&(this.addInt16(e),this.slot(t))},r.Builder.prototype.addFieldInt32=function(t,e,n){(this.force_defaults||e!=n)&&(this.addInt32(e),this.slot(t))},r.Builder.prototype.addFieldInt64=function(t,e,n){!this.force_defaults&&e.equals(n)||(this.addInt64(e),this.slot(t))},r.Builder.prototype.addFieldFloat32=function(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat32(e),this.slot(t))},r.Builder.prototype.addFieldFloat64=function(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat64(e),this.slot(t))},r.Builder.prototype.addFieldOffset=function(t,e,n){(this.force_defaults||e!=n)&&(this.addOffset(e),this.slot(t))},r.Builder.prototype.addFieldStruct=function(t,e,n){e!=n&&(this.nested(e),this.slot(t))},r.Builder.prototype.nested=function(t){if(t!=this.offset())throw new Error("FlatBuffers: struct must be serialized inline.")},r.Builder.prototype.notNested=function(){if(this.isNested)throw new Error("FlatBuffers: object serialization must not be nested.")},r.Builder.prototype.slot=function(t){this.vtable[t]=this.offset()},r.Builder.prototype.offset=function(){return this.bb.capacity()-this.space},r.Builder.growByteBuffer=function(t){var e=t.capacity();if(3221225472&e)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");var n=e<<1,i=r.ByteBuffer.allocate(n);return i.setPosition(n-e),i.bytes().set(t.bytes(),n-e),i},r.Builder.prototype.addOffset=function(t){this.prep(r.SIZEOF_INT,0),this.writeInt32(this.offset()-t+r.SIZEOF_INT)},r.Builder.prototype.startObject=function(t){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=t;for(var e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()},r.Builder.prototype.endObject=function(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);for(var t=this.offset(),e=this.vtable_in_use-1;e>=0&&0==this.vtable[e];e--);for(var n=e+1;e>=0;e--)this.addInt16(0!=this.vtable[e]?t-this.vtable[e]:0);var i=2;this.addInt16(t-this.object_start);var s=(n+i)*r.SIZEOF_SHORT;this.addInt16(s);var o=0,a=this.space;t:for(e=0;e<this.vtables.length;e++){var c=this.bb.capacity()-this.vtables[e];if(s==this.bb.readInt16(c)){for(var u=r.SIZEOF_SHORT;u<s;u+=r.SIZEOF_SHORT)if(this.bb.readInt16(a+u)!=this.bb.readInt16(c+u))continue t;o=this.vtables[e];break}}return o?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,o-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t},r.Builder.prototype.finish=function(t,e){if(e){var n=e;if(this.prep(this.minalign,r.SIZEOF_INT+r.FILE_IDENTIFIER_LENGTH),n.length!=r.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+r.FILE_IDENTIFIER_LENGTH);for(var i=r.FILE_IDENTIFIER_LENGTH-1;i>=0;i--)this.writeInt8(n.charCodeAt(i))}this.prep(this.minalign,r.SIZEOF_INT),this.addOffset(t),this.bb.setPosition(this.space)},r.Builder.prototype.requiredField=function(t,e){var n=this.bb.capacity()-t,r=n-this.bb.readInt32(n),i=0!=this.bb.readInt16(r+e);if(!i)throw new Error("FlatBuffers: field "+e+" must be set")},r.Builder.prototype.startVector=function(t,e,n){this.notNested(),this.vector_num_elems=e,this.prep(r.SIZEOF_INT,t*e),this.prep(n,t*e)},r.Builder.prototype.endVector=function(){return this.writeInt32(this.vector_num_elems),this.offset()},r.Builder.prototype.createString=function(t){if(t instanceof Uint8Array)var e=t;else{e=[];var n=0;while(n<t.length){var r,i=t.charCodeAt(n++);if(i<55296||i>=56320)r=i;else{var s=t.charCodeAt(n++);r=(i<<10)+s+-56613888}r<128?e.push(r):(r<2048?e.push(r>>6&31|192):(r<65536?e.push(r>>12&15|224):e.push(r>>18&7|240,r>>12&63|128),e.push(r>>6&63|128)),e.push(63&r|128))}}this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length);n=0;for(var o=this.space,a=this.bb.bytes();n<e.length;n++)a[o++]=e[n];return this.endVector()},r.Builder.prototype.createLong=function(t,e){return r.Long.create(t,e)},r.ByteBuffer=function(t){this.bytes_=t,this.position_=0},r.ByteBuffer.allocate=function(t){return new r.ByteBuffer(new Uint8Array(t))},r.ByteBuffer.prototype.clear=function(){this.position_=0},r.ByteBuffer.prototype.bytes=function(){return this.bytes_},r.ByteBuffer.prototype.position=function(){return this.position_},r.ByteBuffer.prototype.setPosition=function(t){this.position_=t},r.ByteBuffer.prototype.capacity=function(){return this.bytes_.length},r.ByteBuffer.prototype.readInt8=function(t){return this.readUint8(t)<<24>>24},r.ByteBuffer.prototype.readUint8=function(t){return this.bytes_[t]},r.ByteBuffer.prototype.readInt16=function(t){return this.readUint16(t)<<16>>16},r.ByteBuffer.prototype.readUint16=function(t){return this.bytes_[t]|this.bytes_[t+1]<<8},r.ByteBuffer.prototype.readInt32=function(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24},r.ByteBuffer.prototype.readUint32=function(t){return this.readInt32(t)>>>0},r.ByteBuffer.prototype.readInt64=function(t){return new r.Long(this.readInt32(t),this.readInt32(t+4))},r.ByteBuffer.prototype.readUint64=function(t){return new r.Long(this.readUint32(t),this.readUint32(t+4))},r.ByteBuffer.prototype.readFloat32=function(t){return r.int32[0]=this.readInt32(t),r.float32[0]},r.ByteBuffer.prototype.readFloat64=function(t){return r.int32[r.isLittleEndian?0:1]=this.readInt32(t),r.int32[r.isLittleEndian?1:0]=this.readInt32(t+4),r.float64[0]},r.ByteBuffer.prototype.writeInt8=function(t,e){this.bytes_[t]=e},r.ByteBuffer.prototype.writeUint8=function(t,e){this.bytes_[t]=e},r.ByteBuffer.prototype.writeInt16=function(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8},r.ByteBuffer.prototype.writeUint16=function(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8},r.ByteBuffer.prototype.writeInt32=function(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24},r.ByteBuffer.prototype.writeUint32=function(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24},r.ByteBuffer.prototype.writeInt64=function(t,e){this.writeInt32(t,e.low),this.writeInt32(t+4,e.high)},r.ByteBuffer.prototype.writeUint64=function(t,e){this.writeUint32(t,e.low),this.writeUint32(t+4,e.high)},r.ByteBuffer.prototype.writeFloat32=function(t,e){r.float32[0]=e,this.writeInt32(t,r.int32[0])},r.ByteBuffer.prototype.writeFloat64=function(t,e){r.float64[0]=e,this.writeInt32(t,r.int32[r.isLittleEndian?0:1]),this.writeInt32(t+4,r.int32[r.isLittleEndian?1:0])},r.ByteBuffer.prototype.getBufferIdentifier=function(){if(this.bytes_.length<this.position_+r.SIZEOF_INT+r.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");for(var t="",e=0;e<r.FILE_IDENTIFIER_LENGTH;e++)t+=String.fromCharCode(this.readInt8(this.position_+r.SIZEOF_INT+e));return t},r.ByteBuffer.prototype.__offset=function(t,e){var n=t-this.readInt32(t);return e<this.readInt16(n)?this.readInt16(n+e):0},r.ByteBuffer.prototype.__union=function(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t},r.ByteBuffer.prototype.__string=function(t,e){t+=this.readInt32(t);var n=this.readInt32(t),i="",s=0;if(t+=r.SIZEOF_INT,e===r.Encoding.UTF8_BYTES)return this.bytes_.subarray(t,t+n);while(s<n){var o,a=this.readUint8(t+s++);if(a<192)o=a;else{var c=this.readUint8(t+s++);if(a<224)o=(31&a)<<6|63&c;else{var u=this.readUint8(t+s++);if(a<240)o=(15&a)<<12|(63&c)<<6|63&u;else{var l=this.readUint8(t+s++);o=(7&a)<<18|(63&c)<<12|(63&u)<<6|63&l}}}o<65536?i+=String.fromCharCode(o):(o-=65536,i+=String.fromCharCode(55296+(o>>10),56320+(1023&o)))}return i},r.ByteBuffer.prototype.__indirect=function(t){return t+this.readInt32(t)},r.ByteBuffer.prototype.__vector=function(t){return t+this.readInt32(t)+r.SIZEOF_INT},r.ByteBuffer.prototype.__vector_len=function(t){return this.readInt32(t+this.readInt32(t))},r.ByteBuffer.prototype.__has_identifier=function(t){if(t.length!=r.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+r.FILE_IDENTIFIER_LENGTH);for(var e=0;e<r.FILE_IDENTIFIER_LENGTH;e++)if(t.charCodeAt(e)!=this.readInt8(this.position_+r.SIZEOF_INT+e))return!1;return!0},r.ByteBuffer.prototype.createLong=function(t,e){return r.Long.create(t,e)}},a93d:function(t,e,n){"use strict";
/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r="function"===typeof Symbol&&Symbol.for,i=r?Symbol.for("react.element"):60103,s=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,c=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,l=r?Symbol.for("react.context"):60110,h=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,y=r?Symbol.for("react.suspense_list"):60120,b=r?Symbol.for("react.memo"):60115,g=r?Symbol.for("react.lazy"):60116,m=r?Symbol.for("react.block"):60121,_=r?Symbol.for("react.fundamental"):60117,v=r?Symbol.for("react.responder"):60118,w=r?Symbol.for("react.scope"):60119;function O(t){if("object"===typeof t&&null!==t){var e=t.$$typeof;switch(e){case i:switch(t=t.type,t){case h:case f:case o:case c:case a:case p:return t;default:switch(t=t&&t.$$typeof,t){case l:case d:case g:case b:case u:return t;default:return e}}case s:return e}}}function I(t){return O(t)===f}e.AsyncMode=h,e.ConcurrentMode=f,e.ContextConsumer=l,e.ContextProvider=u,e.Element=i,e.ForwardRef=d,e.Fragment=o,e.Lazy=g,e.Memo=b,e.Portal=s,e.Profiler=c,e.StrictMode=a,e.Suspense=p,e.isAsyncMode=function(t){return I(t)||O(t)===h},e.isConcurrentMode=I,e.isContextConsumer=function(t){return O(t)===l},e.isContextProvider=function(t){return O(t)===u},e.isElement=function(t){return"object"===typeof t&&null!==t&&t.$$typeof===i},e.isForwardRef=function(t){return O(t)===d},e.isFragment=function(t){return O(t)===o},e.isLazy=function(t){return O(t)===g},e.isMemo=function(t){return O(t)===b},e.isPortal=function(t){return O(t)===s},e.isProfiler=function(t){return O(t)===c},e.isStrictMode=function(t){return O(t)===a},e.isSuspense=function(t){return O(t)===p},e.isValidElementType=function(t){return"string"===typeof t||"function"===typeof t||t===o||t===f||t===c||t===a||t===p||t===y||"object"===typeof t&&null!==t&&(t.$$typeof===g||t.$$typeof===b||t.$$typeof===u||t.$$typeof===l||t.$$typeof===d||t.$$typeof===_||t.$$typeof===v||t.$$typeof===w||t.$$typeof===m)},e.typeOf=O},ab5b:function(t,e,n){"use strict";t.exports=n("be24")},ab8b:function(t,e,n){},b4b6:function(t,e,n){"use strict";function r(t,e,n){return e<=t&&t<=n}function i(t){if(void 0===t)return{};if(t===Object(t))return t;throw TypeError("Could not convert argument to dictionary")}function s(t){var e=String(t),n=e.length,r=0,i=[];while(r<n){var s=e.charCodeAt(r);if(s<55296||s>57343)i.push(s);else if(56320<=s&&s<=57343)i.push(65533);else if(55296<=s&&s<=56319)if(r===n-1)i.push(65533);else{var o=t.charCodeAt(r+1);if(56320<=o&&o<=57343){var a=1023&s,c=1023&o;i.push(65536+(a<<10)+c),r+=1}else i.push(65533)}r+=1}return i}function o(t){for(var e="",n=0;n<t.length;++n){var r=t[n];r<=65535?e+=String.fromCharCode(r):(r-=65536,e+=String.fromCharCode(55296+(r>>10),56320+(1023&r)))}return e}n.d(e,"b",(function(){return y})),n.d(e,"a",(function(){return p}));var a=-1;function c(t){this.tokens=[].slice.call(t)}c.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.shift():a},prepend:function(t){if(Array.isArray(t)){var e=t;while(e.length)this.tokens.unshift(e.pop())}else this.tokens.unshift(t)},push:function(t){if(Array.isArray(t)){var e=t;while(e.length)this.tokens.push(e.shift())}else this.tokens.push(t)}};var u=-1;function l(t,e){if(t)throw TypeError("Decoder error");return e||65533}function h(){}function f(){}h.prototype={handler:function(t,e){}},f.prototype={handler:function(t,e){}};var d="utf-8";function p(t,e){if(!(this instanceof p))return new p(t,e);if(t=void 0!==t?String(t).toLowerCase():d,t!==d)throw new Error("Encoding not supported. Only utf-8 is supported");e=i(e),this._streaming=!1,this._BOMseen=!1,this._decoder=null,this._fatal=Boolean(e["fatal"]),this._ignoreBOM=Boolean(e["ignoreBOM"]),Object.defineProperty(this,"encoding",{value:"utf-8"}),Object.defineProperty(this,"fatal",{value:this._fatal}),Object.defineProperty(this,"ignoreBOM",{value:this._ignoreBOM})}function y(t,e){if(!(this instanceof y))return new y(t,e);if(t=void 0!==t?String(t).toLowerCase():d,t!==d)throw new Error("Encoding not supported. Only utf-8 is supported");e=i(e),this._streaming=!1,this._encoder=null,this._options={fatal:Boolean(e["fatal"])},Object.defineProperty(this,"encoding",{value:"utf-8"})}function b(t){var e=t.fatal,n=0,i=0,s=0,o=128,c=191;this.handler=function(t,h){if(h===a&&0!==s)return s=0,l(e);if(h===a)return u;if(0===s){if(r(h,0,127))return h;if(r(h,194,223))s=1,n=h-192;else if(r(h,224,239))224===h&&(o=160),237===h&&(c=159),s=2,n=h-224;else{if(!r(h,240,244))return l(e);240===h&&(o=144),244===h&&(c=143),s=3,n=h-240}return n<<=6*s,null}if(!r(h,o,c))return n=s=i=0,o=128,c=191,t.prepend(h),l(e);if(o=128,c=191,i+=1,n+=h-128<<6*(s-i),i!==s)return null;var f=n;return n=s=i=0,f}}function g(t){t.fatal;this.handler=function(t,e){if(e===a)return u;if(r(e,0,127))return e;var n,i;r(e,128,2047)?(n=1,i=192):r(e,2048,65535)?(n=2,i=224):r(e,65536,1114111)&&(n=3,i=240);var s=[(e>>6*n)+i];while(n>0){var o=e>>6*(n-1);s.push(128|63&o),n-=1}return s}}p.prototype={decode:function(t,e){var n;n="object"===typeof t&&t instanceof ArrayBuffer?new Uint8Array(t):"object"===typeof t&&"buffer"in t&&t.buffer instanceof ArrayBuffer?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):new Uint8Array(0),e=i(e),this._streaming||(this._decoder=new b({fatal:this._fatal}),this._BOMseen=!1),this._streaming=Boolean(e["stream"]);var r,s=new c(n),a=[];while(!s.endOfStream()){if(r=this._decoder.handler(s,s.read()),r===u)break;null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r))}if(!this._streaming){do{if(r=this._decoder.handler(s,s.read()),r===u)break;null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r))}while(!s.endOfStream());this._decoder=null}return a.length&&(-1===["utf-8"].indexOf(this.encoding)||this._ignoreBOM||this._BOMseen||(65279===a[0]?(this._BOMseen=!0,a.shift()):this._BOMseen=!0)),o(a)}},y.prototype={encode:function(t,e){t=t?String(t):"",e=i(e),this._streaming||(this._encoder=new g(this._options)),this._streaming=Boolean(e["stream"]);var n,r=[],o=new c(s(t));while(!o.endOfStream()){if(n=this._encoder.handler(o,o.read()),n===u)break;Array.isArray(n)?r.push.apply(r,n):r.push(n)}if(!this._streaming){while(1){if(n=this._encoder.handler(o,o.read()),n===u)break;Array.isArray(n)?r.push.apply(r,n):r.push(n)}this._encoder=null}return new Uint8Array(r)}}},b639:function(t,e,n){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var r=n("1fb5"),i=n("9152"),s=n("e3db");function o(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"===typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(e){return!1}}function a(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function c(t,e){if(a()<e)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e),t.__proto__=u.prototype):(null===t&&(t=new u(e)),t.length=e),t}function u(t,e,n){if(!u.TYPED_ARRAY_SUPPORT&&!(this instanceof u))return new u(t,e,n);if("number"===typeof t){if("string"===typeof e)throw new Error("If encoding is specified then the first argument must be a string");return d(this,t)}return l(this,t,e,n)}function l(t,e,n,r){if("number"===typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!==typeof ArrayBuffer&&e instanceof ArrayBuffer?b(t,e,n,r):"string"===typeof e?p(t,e,n):g(t,e)}function h(t){if("number"!==typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function f(t,e,n,r){return h(e),e<=0?c(t,e):void 0!==n?"string"===typeof r?c(t,e).fill(n,r):c(t,e).fill(n):c(t,e)}function d(t,e){if(h(e),t=c(t,e<0?0:0|m(e)),!u.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)t[n]=0;return t}function p(t,e,n){if("string"===typeof n&&""!==n||(n="utf8"),!u.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|v(e,n);t=c(t,r);var i=t.write(e,n);return i!==r&&(t=t.slice(0,i)),t}function y(t,e){var n=e.length<0?0:0|m(e.length);t=c(t,n);for(var r=0;r<n;r+=1)t[r]=255&e[r];return t}function b(t,e,n,r){if(e.byteLength,n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return e=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r),u.TYPED_ARRAY_SUPPORT?(t=e,t.__proto__=u.prototype):t=y(t,e),t}function g(t,e){if(u.isBuffer(e)){var n=0|m(e.length);return t=c(t,n),0===t.length?t:(e.copy(t,0,0,n),t)}if(e){if("undefined"!==typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!==typeof e.length||et(e.length)?c(t,0):y(t,e);if("Buffer"===e.type&&s(e.data))return y(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function m(t){if(t>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|t}function _(t){return+t!=t&&(t=0),u.alloc(+t)}function v(t,e){if(u.isBuffer(t))return t.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!==typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return J(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Q(t).length;default:if(r)return J(t).length;e=(""+e).toLowerCase(),r=!0}}function w(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,e>>>=0,n<=e)return"";t||(t="utf8");while(1)switch(t){case"hex":return N(this,e,n);case"utf8":case"utf-8":return L(this,e,n);case"ascii":return M(this,e,n);case"latin1":case"binary":return C(this,e,n);case"base64":return D(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function O(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function I(t,e,n,r,i){if(0===t.length)return-1;if("string"===typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"===typeof e&&(e=u.from(e,r)),u.isBuffer(e))return 0===e.length?-1:S(t,e,n,r,i);if("number"===typeof e)return e&=255,u.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):S(t,[e],n,r,i);throw new TypeError("val must be string, number or Buffer")}function S(t,e,n,r,i){var s,o=1,a=t.length,c=e.length;if(void 0!==r&&(r=String(r).toLowerCase(),"ucs2"===r||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;o=2,a/=2,c/=2,n/=2}function u(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(i){var l=-1;for(s=n;s<a;s++)if(u(t,s)===u(e,-1===l?0:s-l)){if(-1===l&&(l=s),s-l+1===c)return l*o}else-1!==l&&(s-=s-l),l=-1}else for(n+c>a&&(n=a-c),s=n;s>=0;s--){for(var h=!0,f=0;f<c;f++)if(u(t,s+f)!==u(e,f)){h=!1;break}if(h)return s}return-1}function A(t,e,n,r){n=Number(n)||0;var i=t.length-n;r?(r=Number(r),r>i&&(r=i)):r=i;var s=e.length;if(s%2!==0)throw new TypeError("Invalid hex string");r>s/2&&(r=s/2);for(var o=0;o<r;++o){var a=parseInt(e.substr(2*o,2),16);if(isNaN(a))return o;t[n+o]=a}return o}function T(t,e,n,r){return tt(J(e,t.length-n),t,n,r)}function B(t,e,n,r){return tt(Z(e),t,n,r)}function x(t,e,n,r){return B(t,e,n,r)}function j(t,e,n,r){return tt(Q(e),t,n,r)}function E(t,e,n,r){return tt(X(e,t.length-n),t,n,r)}function D(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function L(t,e,n){n=Math.min(t.length,n);var r=[],i=e;while(i<n){var s,o,a,c,u=t[i],l=null,h=u>239?4:u>223?3:u>191?2:1;if(i+h<=n)switch(h){case 1:u<128&&(l=u);break;case 2:s=t[i+1],128===(192&s)&&(c=(31&u)<<6|63&s,c>127&&(l=c));break;case 3:s=t[i+1],o=t[i+2],128===(192&s)&&128===(192&o)&&(c=(15&u)<<12|(63&s)<<6|63&o,c>2047&&(c<55296||c>57343)&&(l=c));break;case 4:s=t[i+1],o=t[i+2],a=t[i+3],128===(192&s)&&128===(192&o)&&128===(192&a)&&(c=(15&u)<<18|(63&s)<<12|(63&o)<<6|63&a,c>65535&&c<1114112&&(l=c))}null===l?(l=65533,h=1):l>65535&&(l-=65536,r.push(l>>>10&1023|55296),l=56320|1023&l),r.push(l),i+=h}return U(r)}e.Buffer=u,e.SlowBuffer=_,e.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:o(),e.kMaxLength=a(),u.poolSize=8192,u._augment=function(t){return t.__proto__=u.prototype,t},u.from=function(t,e,n){return l(null,t,e,n)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(t,e,n){return f(null,t,e,n)},u.allocUnsafe=function(t){return d(null,t)},u.allocUnsafeSlow=function(t){return d(null,t)},u.isBuffer=function(t){return!(null==t||!t._isBuffer)},u.compare=function(t,e){if(!u.isBuffer(t)||!u.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,i=0,s=Math.min(n,r);i<s;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!s(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=u.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){var o=t[n];if(!u.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},u.byteLength=v,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)O(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)O(this,e,e+3),O(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)O(this,e,e+7),O(this,e+1,e+6),O(this,e+2,e+5),O(this,e+3,e+4);return this},u.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?L(this,0,t):w.apply(this,arguments)},u.prototype.equals=function(t){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},u.prototype.compare=function(t,e,n,r,i){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return-1;if(e>=n)return 1;if(e>>>=0,n>>>=0,r>>>=0,i>>>=0,this===t)return 0;for(var s=i-r,o=n-e,a=Math.min(s,o),c=this.slice(r,i),l=t.slice(e,n),h=0;h<a;++h)if(c[h]!==l[h]){s=c[h],o=l[h];break}return s<o?-1:o<s?1:0},u.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},u.prototype.indexOf=function(t,e,n){return I(this,t,e,n,!0)},u.prototype.lastIndexOf=function(t,e,n){return I(this,t,e,n,!1)},u.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"===typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-e;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var s=!1;;)switch(r){case"hex":return A(this,t,e,n);case"utf8":case"utf-8":return T(this,t,e,n);case"ascii":return B(this,t,e,n);case"latin1":case"binary":return x(this,t,e,n);case"base64":return j(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,t,e,n);default:if(s)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),s=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var F=4096;function U(t){var e=t.length;if(e<=F)return String.fromCharCode.apply(String,t);var n="",r=0;while(r<e)n+=String.fromCharCode.apply(String,t.slice(r,r+=F));return n}function M(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function C(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function N(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var i="",s=e;s<n;++s)i+=G(t[s]);return i}function k(t,e,n){for(var r=t.slice(e,n),i="",s=0;s<r.length;s+=2)i+=String.fromCharCode(r[s]+256*r[s+1]);return i}function R(t,e,n){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function V(t,e,n,r,i,s){if(!u.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<s)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function P(t,e,n,r){e<0&&(e=65535+e+1);for(var i=0,s=Math.min(t.length-n,2);i<s;++i)t[n+i]=(e&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function z(t,e,n,r){e<0&&(e=4294967295+e+1);for(var i=0,s=Math.min(t.length-n,4);i<s;++i)t[n+i]=e>>>8*(r?i:3-i)&255}function $(t,e,n,r,i,s){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function Y(t,e,n,r,s){return s||$(t,e,n,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,n,r,23,4),n+4}function W(t,e,n,r,s){return s||$(t,e,n,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,n,r,52,8),n+8}u.prototype.slice=function(t,e){var n,r=this.length;if(t=~~t,e=void 0===e?r:~~e,t<0?(t+=r,t<0&&(t=0)):t>r&&(t=r),e<0?(e+=r,e<0&&(e=0)):e>r&&(e=r),e<t&&(e=t),u.TYPED_ARRAY_SUPPORT)n=this.subarray(t,e),n.__proto__=u.prototype;else{var i=e-t;n=new u(i,void 0);for(var s=0;s<i;++s)n[s]=this[s+t]}return n},u.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||R(t,e,this.length);var r=this[t],i=1,s=0;while(++s<e&&(i*=256))r+=this[t+s]*i;return r},u.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||R(t,e,this.length);var r=this[t+--e],i=1;while(e>0&&(i*=256))r+=this[t+--e]*i;return r},u.prototype.readUInt8=function(t,e){return e||R(t,1,this.length),this[t]},u.prototype.readUInt16LE=function(t,e){return e||R(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUInt16BE=function(t,e){return e||R(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUInt32LE=function(t,e){return e||R(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUInt32BE=function(t,e){return e||R(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||R(t,e,this.length);var r=this[t],i=1,s=0;while(++s<e&&(i*=256))r+=this[t+s]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*e)),r},u.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||R(t,e,this.length);var r=e,i=1,s=this[t+--r];while(r>0&&(i*=256))s+=this[t+--r]*i;return i*=128,s>=i&&(s-=Math.pow(2,8*e)),s},u.prototype.readInt8=function(t,e){return e||R(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},u.prototype.readInt16LE=function(t,e){e||R(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},u.prototype.readInt16BE=function(t,e){e||R(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},u.prototype.readInt32LE=function(t,e){return e||R(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return e||R(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return e||R(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return e||R(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return e||R(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return e||R(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUIntLE=function(t,e,n,r){if(t=+t,e|=0,n|=0,!r){var i=Math.pow(2,8*n)-1;V(this,t,e,n,i,0)}var s=1,o=0;this[e]=255&t;while(++o<n&&(s*=256))this[e+o]=t/s&255;return e+n},u.prototype.writeUIntBE=function(t,e,n,r){if(t=+t,e|=0,n|=0,!r){var i=Math.pow(2,8*n)-1;V(this,t,e,n,i,0)}var s=n-1,o=1;this[e+s]=255&t;while(--s>=0&&(o*=256))this[e+s]=t/o&255;return e+n},u.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,1,255,0),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},u.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):P(this,t,e,!0),e+2},u.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):P(this,t,e,!1),e+2},u.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):z(this,t,e,!0),e+4},u.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):z(this,t,e,!1),e+4},u.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);V(this,t,e,n,i-1,-i)}var s=0,o=1,a=0;this[e]=255&t;while(++s<n&&(o*=256))t<0&&0===a&&0!==this[e+s-1]&&(a=1),this[e+s]=(t/o>>0)-a&255;return e+n},u.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);V(this,t,e,n,i-1,-i)}var s=n-1,o=1,a=0;this[e+s]=255&t;while(--s>=0&&(o*=256))t<0&&0===a&&0!==this[e+s+1]&&(a=1),this[e+s]=(t/o>>0)-a&255;return e+n},u.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,1,127,-128),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):P(this,t,e,!0),e+2},u.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):P(this,t,e,!1),e+2},u.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):z(this,t,e,!0),e+4},u.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||V(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):z(this,t,e,!1),e+4},u.prototype.writeFloatLE=function(t,e,n){return Y(this,t,e,!0,n)},u.prototype.writeFloatBE=function(t,e,n){return Y(this,t,e,!1,n)},u.prototype.writeDoubleLE=function(t,e,n){return W(this,t,e,!0,n)},u.prototype.writeDoubleBE=function(t,e,n){return W(this,t,e,!1,n)},u.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var i,s=r-n;if(this===t&&n<e&&e<r)for(i=s-1;i>=0;--i)t[i+e]=this[i+n];else if(s<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<s;++i)t[i+e]=this[i+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+s),e);return s},u.prototype.fill=function(t,e,n,r){if("string"===typeof t){if("string"===typeof e?(r=e,e=0,n=this.length):"string"===typeof n&&(r=n,n=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==r&&"string"!==typeof r)throw new TypeError("encoding must be a string");if("string"===typeof r&&!u.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"===typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var s;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"===typeof t)for(s=e;s<n;++s)this[s]=t;else{var o=u.isBuffer(t)?t:J(new u(t,r).toString()),a=o.length;for(s=0;s<n-e;++s)this[s+e]=o[s%a]}return this};var H=/[^+\/0-9A-Za-z-_]/g;function q(t){if(t=K(t).replace(H,""),t.length<2)return"";while(t.length%4!==0)t+="=";return t}function K(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function G(t){return t<16?"0"+t.toString(16):t.toString(16)}function J(t,e){var n;e=e||1/0;for(var r=t.length,i=null,s=[],o=0;o<r;++o){if(n=t.charCodeAt(o),n>55295&&n<57344){if(!i){if(n>56319){(e-=3)>-1&&s.push(239,191,189);continue}if(o+1===r){(e-=3)>-1&&s.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&s.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(e-=3)>-1&&s.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;s.push(n)}else if(n<2048){if((e-=2)<0)break;s.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;s.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;s.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return s}function Z(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}function X(t,e){for(var n,r,i,s=[],o=0;o<t.length;++o){if((e-=2)<0)break;n=t.charCodeAt(o),r=n>>8,i=n%256,s.push(i),s.push(r)}return s}function Q(t){return r.toByteArray(q(t))}function tt(t,e,n,r){for(var i=0;i<r;++i){if(i+n>=e.length||i>=t.length)break;e[i+n]=t[i]}return i}function et(t){return t!==t}}).call(this,n("c8ba"))},be24:function(t,e,n){"use strict";
/** @license React v16.14.0
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n("320c"),i="function"===typeof Symbol&&Symbol.for,s=i?Symbol.for("react.element"):60103,o=i?Symbol.for("react.portal"):60106,a=i?Symbol.for("react.fragment"):60107,c=i?Symbol.for("react.strict_mode"):60108,u=i?Symbol.for("react.profiler"):60114,l=i?Symbol.for("react.provider"):60109,h=i?Symbol.for("react.context"):60110,f=i?Symbol.for("react.forward_ref"):60112,d=i?Symbol.for("react.suspense"):60113,p=i?Symbol.for("react.memo"):60115,y=i?Symbol.for("react.lazy"):60116,b="function"===typeof Symbol&&Symbol.iterator;function g(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_={};function v(t,e,n){this.props=t,this.context=e,this.refs=_,this.updater=n||m}function w(){}function O(t,e,n){this.props=t,this.context=e,this.refs=_,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(t,e){if("object"!==typeof t&&"function"!==typeof t&&null!=t)throw Error(g(85));this.updater.enqueueSetState(this,t,e,"setState")},v.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")},w.prototype=v.prototype;var I=O.prototype=new w;I.constructor=O,r(I,v.prototype),I.isPureReactComponent=!0;var S={current:null},A=Object.prototype.hasOwnProperty,T={key:!0,ref:!0,__self:!0,__source:!0};function B(t,e,n){var r,i={},o=null,a=null;if(null!=e)for(r in void 0!==e.ref&&(a=e.ref),void 0!==e.key&&(o=""+e.key),e)A.call(e,r)&&!T.hasOwnProperty(r)&&(i[r]=e[r]);var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){for(var u=Array(c),l=0;l<c;l++)u[l]=arguments[l+2];i.children=u}if(t&&t.defaultProps)for(r in c=t.defaultProps,c)void 0===i[r]&&(i[r]=c[r]);return{$$typeof:s,type:t,key:o,ref:a,props:i,_owner:S.current}}function x(t,e){return{$$typeof:s,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function j(t){return"object"===typeof t&&null!==t&&t.$$typeof===s}function E(t){var e={"=":"=0",":":"=2"};return"$"+(""+t).replace(/[=:]/g,(function(t){return e[t]}))}var D=/\/+/g,L=[];function F(t,e,n,r){if(L.length){var i=L.pop();return i.result=t,i.keyPrefix=e,i.func=n,i.context=r,i.count=0,i}return{result:t,keyPrefix:e,func:n,context:r,count:0}}function U(t){t.result=null,t.keyPrefix=null,t.func=null,t.context=null,t.count=0,10>L.length&&L.push(t)}function M(t,e,n,r){var i=typeof t;"undefined"!==i&&"boolean"!==i||(t=null);var a=!1;if(null===t)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case s:case o:a=!0}}if(a)return n(r,t,""===e?"."+N(t,0):e),1;if(a=0,e=""===e?".":e+":",Array.isArray(t))for(var c=0;c<t.length;c++){i=t[c];var u=e+N(i,c);a+=M(i,u,n,r)}else if(null===t||"object"!==typeof t?u=null:(u=b&&t[b]||t["@@iterator"],u="function"===typeof u?u:null),"function"===typeof u)for(t=u.call(t),c=0;!(i=t.next()).done;)i=i.value,u=e+N(i,c++),a+=M(i,u,n,r);else if("object"===i)throw n=""+t,Error(g(31,"[object Object]"===n?"object with keys {"+Object.keys(t).join(", ")+"}":n,""));return a}function C(t,e,n){return null==t?0:M(t,"",e,n)}function N(t,e){return"object"===typeof t&&null!==t&&null!=t.key?E(t.key):e.toString(36)}function k(t,e){t.func.call(t.context,e,t.count++)}function R(t,e,n){var r=t.result,i=t.keyPrefix;t=t.func.call(t.context,e,t.count++),Array.isArray(t)?V(t,r,n,(function(t){return t})):null!=t&&(j(t)&&(t=x(t,i+(!t.key||e&&e.key===t.key?"":(""+t.key).replace(D,"$&/")+"/")+n)),r.push(t))}function V(t,e,n,r,i){var s="";null!=n&&(s=(""+n).replace(D,"$&/")+"/"),e=F(e,s,r,i),C(t,R,e),U(e)}var P={current:null};function z(){var t=P.current;if(null===t)throw Error(g(321));return t}var $={ReactCurrentDispatcher:P,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:S,IsSomeRendererActing:{current:!1},assign:r};e.Children={map:function(t,e,n){if(null==t)return t;var r=[];return V(t,r,null,e,n),r},forEach:function(t,e,n){if(null==t)return t;e=F(null,null,e,n),C(t,k,e),U(e)},count:function(t){return C(t,(function(){return null}),null)},toArray:function(t){var e=[];return V(t,e,null,(function(t){return t})),e},only:function(t){if(!j(t))throw Error(g(143));return t}},e.Component=v,e.Fragment=a,e.Profiler=u,e.PureComponent=O,e.StrictMode=c,e.Suspense=d,e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=$,e.cloneElement=function(t,e,n){if(null===t||void 0===t)throw Error(g(267,t));var i=r({},t.props),o=t.key,a=t.ref,c=t._owner;if(null!=e){if(void 0!==e.ref&&(a=e.ref,c=S.current),void 0!==e.key&&(o=""+e.key),t.type&&t.type.defaultProps)var u=t.type.defaultProps;for(l in e)A.call(e,l)&&!T.hasOwnProperty(l)&&(i[l]=void 0===e[l]&&void 0!==u?u[l]:e[l])}var l=arguments.length-2;if(1===l)i.children=n;else if(1<l){u=Array(l);for(var h=0;h<l;h++)u[h]=arguments[h+2];i.children=u}return{$$typeof:s,type:t.type,key:o,ref:a,props:i,_owner:c}},e.createContext=function(t,e){return void 0===e&&(e=null),t={$$typeof:h,_calculateChangedBits:e,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null},t.Provider={$$typeof:l,_context:t},t.Consumer=t},e.createElement=B,e.createFactory=function(t){var e=B.bind(null,t);return e.type=t,e},e.createRef=function(){return{current:null}},e.forwardRef=function(t){return{$$typeof:f,render:t}},e.isValidElement=j,e.lazy=function(t){return{$$typeof:y,_ctor:t,_status:-1,_result:null}},e.memo=function(t,e){return{$$typeof:p,type:t,compare:void 0===e?null:e}},e.useCallback=function(t,e){return z().useCallback(t,e)},e.useContext=function(t,e){return z().useContext(t,e)},e.useDebugValue=function(){},e.useEffect=function(t,e){return z().useEffect(t,e)},e.useImperativeHandle=function(t,e,n){return z().useImperativeHandle(t,e,n)},e.useLayoutEffect=function(t,e){return z().useLayoutEffect(t,e)},e.useMemo=function(t,e){return z().useMemo(t,e)},e.useReducer=function(t,e,n){return z().useReducer(t,e,n)},e.useRef=function(t){return z().useRef(t)},e.useState=function(t){return z().useState(t)},e.version="16.14.0"},c8ba:function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}t.exports=n},cd74:function(t,e,n){},d092:function(t,e,n){"use strict";n.d(e,"a",(function(){return il}));var r={};n.r(r),n.d(r,"getBool",(function(){return q})),n.d(r,"getBit",(function(){return K})),n.d(r,"setBool",(function(){return G})),n.d(r,"truncateBitmap",(function(){return J})),n.d(r,"packBools",(function(){return Z})),n.d(r,"iterateBits",(function(){return X})),n.d(r,"popcnt_bit_range",(function(){return Q})),n.d(r,"popcnt_array",(function(){return tt})),n.d(r,"popcnt_uint32",(function(){return et}));var i={};n.r(i),n.d(i,"uint16ToFloat64",(function(){return Le})),n.d(i,"float64ToUint16",(function(){return Fe}));var s={};n.r(s),n.d(s,"isArrowBigNumSymbol",(function(){return ke})),n.d(s,"bignumToString",(function(){return Ye})),n.d(s,"bignumToBigInt",(function(){return We})),n.d(s,"BN",(function(){return qe}));var o={};n.r(o),n.d(o,"clampIndex",(function(){return Ln})),n.d(o,"clampRange",(function(){return Un})),n.d(o,"createElementComparator",(function(){return Nn}));var a={};n.r(a),n.d(a,"BaseInt64",(function(){return Ci})),n.d(a,"Uint64",(function(){return Ni})),n.d(a,"Int64",(function(){return ki})),n.d(a,"Int128",(function(){return Ri}));n("da6a");var c=n("ab5b"),u=n.n(c);const l=new WeakMap,h=new WeakMap;function f(t){const e=l.get(t);return console.assert(null!=e,"'this' is expected an Event object, but got",t),e}function d(t){null==t.passiveListener?t.event.cancelable&&(t.canceled=!0,"function"===typeof t.event.preventDefault&&t.event.preventDefault()):"undefined"!==typeof console&&"function"===typeof console.error&&console.error("Unable to preventDefault inside passive event listener invocation.",t.passiveListener)}function p(t,e){l.set(this,{eventTarget:t,event:e,eventPhase:2,currentTarget:t,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:e.timeStamp||Date.now()}),Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});const n=Object.keys(e);for(let r=0;r<n.length;++r){const t=n[r];t in this||Object.defineProperty(this,t,y(t))}}function y(t){return{get(){return f(this).event[t]},set(e){f(this).event[t]=e},configurable:!0,enumerable:!0}}function b(t){return{value(){const e=f(this).event;return e[t].apply(e,arguments)},configurable:!0,enumerable:!0}}function g(t,e){const n=Object.keys(e);if(0===n.length)return t;function r(e,n){t.call(this,e,n)}r.prototype=Object.create(t.prototype,{constructor:{value:r,configurable:!0,writable:!0}});for(let i=0;i<n.length;++i){const s=n[i];if(!(s in t.prototype)){const t=Object.getOwnPropertyDescriptor(e,s),n="function"===typeof t.value;Object.defineProperty(r.prototype,s,n?b(s):y(s))}}return r}function m(t){if(null==t||t===Object.prototype)return p;let e=h.get(t);return null==e&&(e=g(m(Object.getPrototypeOf(t)),t),h.set(t,e)),e}function _(t,e){const n=m(Object.getPrototypeOf(e));return new n(t,e)}function v(t){return f(t).immediateStopped}function w(t,e){f(t).eventPhase=e}function O(t,e){f(t).currentTarget=e}function I(t,e){f(t).passiveListener=e}p.prototype={get type(){return f(this).event.type},get target(){return f(this).eventTarget},get currentTarget(){return f(this).currentTarget},composedPath(){const t=f(this).currentTarget;return null==t?[]:[t]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return f(this).eventPhase},stopPropagation(){const t=f(this);t.stopped=!0,"function"===typeof t.event.stopPropagation&&t.event.stopPropagation()},stopImmediatePropagation(){const t=f(this);t.stopped=!0,t.immediateStopped=!0,"function"===typeof t.event.stopImmediatePropagation&&t.event.stopImmediatePropagation()},get bubbles(){return Boolean(f(this).event.bubbles)},get cancelable(){return Boolean(f(this).event.cancelable)},preventDefault(){d(f(this))},get defaultPrevented(){return f(this).canceled},get composed(){return Boolean(f(this).event.composed)},get timeStamp(){return f(this).timeStamp},get srcElement(){return f(this).eventTarget},get cancelBubble(){return f(this).stopped},set cancelBubble(t){if(!t)return;const e=f(this);e.stopped=!0,"boolean"===typeof e.event.cancelBubble&&(e.event.cancelBubble=!0)},get returnValue(){return!f(this).canceled},set returnValue(t){t||d(f(this))},initEvent(){}},Object.defineProperty(p.prototype,"constructor",{value:p,configurable:!0,writable:!0}),"undefined"!==typeof window&&"undefined"!==typeof window.Event&&(Object.setPrototypeOf(p.prototype,window.Event.prototype),h.set(window.Event.prototype,p));const S=new WeakMap,A=1,T=2,B=3;function x(t){return null!==t&&"object"===typeof t}function j(t){const e=S.get(t);if(null==e)throw new TypeError("'this' is expected an EventTarget object, but got another value.");return e}function E(t){return{get(){const e=j(this);let n=e.get(t);while(null!=n){if(n.listenerType===B)return n.listener;n=n.next}return null},set(e){"function"===typeof e||x(e)||(e=null);const n=j(this);let r=null,i=n.get(t);while(null!=i)i.listenerType===B?null!==r?r.next=i.next:null!==i.next?n.set(t,i.next):n.delete(t):r=i,i=i.next;if(null!==e){const i={listener:e,listenerType:B,passive:!1,once:!1,next:null};null===r?n.set(t,i):r.next=i}},configurable:!0,enumerable:!0}}function D(t,e){Object.defineProperty(t,"on"+e,E(e))}function L(t){function e(){F.call(this)}e.prototype=Object.create(F.prototype,{constructor:{value:e,configurable:!0,writable:!0}});for(let n=0;n<t.length;++n)D(e.prototype,t[n]);return e}function F(){if(!(this instanceof F)){if(1===arguments.length&&Array.isArray(arguments[0]))return L(arguments[0]);if(arguments.length>0){const t=new Array(arguments.length);for(let e=0;e<arguments.length;++e)t[e]=arguments[e];return L(t)}throw new TypeError("Cannot call a class as a function")}S.set(this,new Map)}F.prototype={addEventListener(t,e,n){if(null==e)return;if("function"!==typeof e&&!x(e))throw new TypeError("'listener' should be a function or an object.");const r=j(this),i=x(n),s=i?Boolean(n.capture):Boolean(n),o=s?A:T,a={listener:e,listenerType:o,passive:i&&Boolean(n.passive),once:i&&Boolean(n.once),next:null};let c=r.get(t);if(void 0===c)return void r.set(t,a);let u=null;while(null!=c){if(c.listener===e&&c.listenerType===o)return;u=c,c=c.next}u.next=a},removeEventListener(t,e,n){if(null==e)return;const r=j(this),i=x(n)?Boolean(n.capture):Boolean(n),s=i?A:T;let o=null,a=r.get(t);while(null!=a){if(a.listener===e&&a.listenerType===s)return void(null!==o?o.next=a.next:null!==a.next?r.set(t,a.next):r.delete(t));o=a,a=a.next}},dispatchEvent(t){if(null==t||"string"!==typeof t.type)throw new TypeError('"event.type" should be a string.');const e=j(this),n=t.type;let r=e.get(n);if(null==r)return!0;const i=_(this,t);let s=null;while(null!=r){if(r.once?null!==s?s.next=r.next:null!==r.next?e.set(n,r.next):e.delete(n):s=r,I(i,r.passive?r.listener:null),"function"===typeof r.listener)try{r.listener.call(this,i)}catch(o){"undefined"!==typeof console&&"function"===typeof console.error&&console.error(o)}else r.listenerType!==B&&"function"===typeof r.listener.handleEvent&&r.listener.handleEvent(i);if(v(i))break;r=r.next}return I(i,null),w(i,0),O(i,null),!i.defaultPrevented}},Object.defineProperty(F.prototype,"constructor",{value:F,configurable:!0,writable:!0}),"undefined"!==typeof window&&"undefined"!==typeof window.EventTarget&&Object.setPrototypeOf(F.prototype,window.EventTarget.prototype);var U,M,C=n("6bfb");class N{}(function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["V1"]=0]="V1",t[t["V2"]=1]="V2",t[t["V3"]=2]="V3",t[t["V4"]=3]="V4"})(e=t.MetadataVersion||(t.MetadataVersion={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))})(U||(U={})),function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["Sparse"]=0]="Sparse",t[t["Dense"]=1]="Dense"})(e=t.UnionMode||(t.UnionMode={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["HALF"]=0]="HALF",t[t["SINGLE"]=1]="SINGLE",t[t["DOUBLE"]=2]="DOUBLE"})(e=t.Precision||(t.Precision={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["DAY"]=0]="DAY",t[t["MILLISECOND"]=1]="MILLISECOND"})(e=t.DateUnit||(t.DateUnit={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["SECOND"]=0]="SECOND",t[t["MILLISECOND"]=1]="MILLISECOND",t[t["MICROSECOND"]=2]="MICROSECOND",t[t["NANOSECOND"]=3]="NANOSECOND"})(e=t.TimeUnit||(t.TimeUnit={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["YEAR_MONTH"]=0]="YEAR_MONTH",t[t["DAY_TIME"]=1]="DAY_TIME"})(e=t.IntervalUnit||(t.IntervalUnit={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["NONE"]=0]="NONE",t[t["Null"]=1]="Null",t[t["Int"]=2]="Int",t[t["FloatingPoint"]=3]="FloatingPoint",t[t["Binary"]=4]="Binary",t[t["Utf8"]=5]="Utf8",t[t["Bool"]=6]="Bool",t[t["Decimal"]=7]="Decimal",t[t["Date"]=8]="Date",t[t["Time"]=9]="Time",t[t["Timestamp"]=10]="Timestamp",t[t["Interval"]=11]="Interval",t[t["List"]=12]="List",t[t["Struct_"]=13]="Struct_",t[t["Union"]=14]="Union",t[t["FixedSizeBinary"]=15]="FixedSizeBinary",t[t["FixedSizeList"]=16]="FixedSizeList",t[t["Map"]=17]="Map",t[t["Duration"]=18]="Duration",t[t["LargeBinary"]=19]="LargeBinary",t[t["LargeUtf8"]=20]="LargeUtf8",t[t["LargeList"]=21]="LargeList"})(e=t.Type||(t.Type={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["Little"]=0]="Little",t[t["Big"]=1]="Big"})(e=t.Endianness||(t.Endianness={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startNull(t){t.startObject(0)}static endNull(t){let e=t.endObject();return e}static createNull(t){return e.startNull(t),e.endNull(t)}}t.Null=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){let e=t.endObject();return e}static createStruct_(t){return e.startStruct_(t),e.endStruct_(t)}}t.Struct_=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startList(t){t.startObject(0)}static endList(t){let e=t.endObject();return e}static createList(t){return e.startList(t),e.endList(t)}}t.List=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeList(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startLargeList(t){t.startObject(0)}static endLargeList(t){let e=t.endObject();return e}static createLargeList(t){return e.startLargeList(t),e.endLargeList(t)}}t.LargeList=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}listSize(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){let e=t.endObject();return e}static createFixedSizeList(t,n){return e.startFixedSizeList(t),e.addListSize(t,n),e.endFixedSizeList(t)}}t.FixedSizeList=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}keysSorted(){let t=this.bb.__offset(this.bb_pos,4);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){let e=t.endObject();return e}static createMap(t,n){return e.startMap(t),e.addKeysSorted(t,n),e.endMap(t)}}t.Map=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}mode(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):t.apache.arrow.flatbuf.UnionMode.Sparse}typeIds(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+4*t):0}typeIdsLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){let t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(e,n){e.addFieldInt16(0,n,t.apache.arrow.flatbuf.UnionMode.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addInt32(e[n]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){let e=t.endObject();return e}static createUnion(t,e,r){return n.startUnion(t),n.addMode(t,e),n.addTypeIds(t,r),n.endUnion(t)}}e.Union=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){let t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){let e=t.endObject();return e}static createInt(t,n,r){return e.startInt(t),e.addBitWidth(t,n),e.addIsSigned(t,r),e.endInt(t)}}t.Int=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}precision(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):t.apache.arrow.flatbuf.Precision.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(e,n){e.addFieldInt16(0,n,t.apache.arrow.flatbuf.Precision.HALF)}static endFloatingPoint(t){let e=t.endObject();return e}static createFloatingPoint(t,e){return n.startFloatingPoint(t),n.addPrecision(t,e),n.endFloatingPoint(t)}}e.FloatingPoint=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startUtf8(t){t.startObject(0)}static endUtf8(t){let e=t.endObject();return e}static createUtf8(t){return e.startUtf8(t),e.endUtf8(t)}}t.Utf8=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startBinary(t){t.startObject(0)}static endBinary(t){let e=t.endObject();return e}static createBinary(t){return e.startBinary(t),e.endBinary(t)}}t.Binary=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeUtf8(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startLargeUtf8(t){t.startObject(0)}static endLargeUtf8(t){let e=t.endObject();return e}static createLargeUtf8(t){return e.startLargeUtf8(t),e.endLargeUtf8(t)}}t.LargeUtf8=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeBinary(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startLargeBinary(t){t.startObject(0)}static endLargeBinary(t){let e=t.endObject();return e}static createLargeBinary(t){return e.startLargeBinary(t),e.endLargeBinary(t)}}t.LargeBinary=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}byteWidth(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){let e=t.endObject();return e}static createFixedSizeBinary(t,n){return e.startFixedSizeBinary(t),e.addByteWidth(t,n),e.endFixedSizeBinary(t)}}t.FixedSizeBinary=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static startBool(t){t.startObject(0)}static endBool(t){let e=t.endObject();return e}static createBool(t){return e.startBool(t),e.endBool(t)}}t.Bool=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}precision(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}static startDecimal(t){t.startObject(2)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static endDecimal(t){let e=t.endObject();return e}static createDecimal(t,n,r){return e.startDecimal(t),e.addPrecision(t,n),e.addScale(t,r),e.endDecimal(t)}}t.Decimal=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}unit(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):t.apache.arrow.flatbuf.DateUnit.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(e,n){e.addFieldInt16(0,n,t.apache.arrow.flatbuf.DateUnit.MILLISECOND)}static endDate(t){let e=t.endObject();return e}static createDate(t,e){return n.startDate(t),n.addUnit(t,e),n.endDate(t)}}e.Date=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}unit(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):t.apache.arrow.flatbuf.TimeUnit.MILLISECOND}bitWidth(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(e,n){e.addFieldInt16(0,n,t.apache.arrow.flatbuf.TimeUnit.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){let e=t.endObject();return e}static createTime(t,e,r){return n.startTime(t),n.addUnit(t,e),n.addBitWidth(t,r),n.endTime(t)}}e.Time=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}unit(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):t.apache.arrow.flatbuf.TimeUnit.SECOND}timezone(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(e,n){e.addFieldInt16(0,n,t.apache.arrow.flatbuf.TimeUnit.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){let e=t.endObject();return e}static createTimestamp(t,e,r){return n.startTimestamp(t),n.addUnit(t,e),n.addTimezone(t,r),n.endTimestamp(t)}}e.Timestamp=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}unit(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):t.apache.arrow.flatbuf.IntervalUnit.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(e,n){e.addFieldInt16(0,n,t.apache.arrow.flatbuf.IntervalUnit.YEAR_MONTH)}static endInterval(t){let e=t.endObject();return e}static createInterval(t,e){return n.startInterval(t),n.addUnit(t,e),n.endInterval(t)}}e.Interval=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDuration(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}unit(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):t.apache.arrow.flatbuf.TimeUnit.MILLISECOND}static startDuration(t){t.startObject(1)}static addUnit(e,n){e.addFieldInt16(0,n,t.apache.arrow.flatbuf.TimeUnit.MILLISECOND)}static endDuration(t){let e=t.endObject();return e}static createDuration(t,e){return n.startDuration(t),n.addUnit(t,e),n.endDuration(t)}}e.Duration=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}key(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){let e=t.endObject();return e}static createKeyValue(t,n,r){return e.startKeyValue(t),e.addKey(t,n),e.addValue(t,r),e.endKeyValue(t)}}t.KeyValue=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}id(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}indexType(e){let n=this.bb.__offset(this.bb_pos,6);return n?(e||new t.apache.arrow.flatbuf.Int).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}isOrdered(){let t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startDictionaryEncoding(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryEncoding(t){let e=t.endObject();return e}static createDictionaryEncoding(t,e,r,i){return n.startDictionaryEncoding(t),n.addId(t,e),n.addIndexType(t,r),n.addIsOrdered(t,i),n.endDictionaryEncoding(t)}}e.DictionaryEncoding=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}name(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){let t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}typeType(){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.readUint8(this.bb_pos+e):t.apache.arrow.flatbuf.Type.NONE}type(t){let e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(e){let n=this.bb.__offset(this.bb_pos,12);return n?(e||new t.apache.arrow.flatbuf.DictionaryEncoding).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}children(e,n){let r=this.bb.__offset(this.bb_pos,14);return r?(n||new t.apache.arrow.flatbuf.Field).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}childrenLength(){let t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(e,n){let r=this.bb.__offset(this.bb_pos,16);return r?(n||new t.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}customMetadataLength(){let t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(e,n){e.addFieldInt8(2,n,t.apache.arrow.flatbuf.Type.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){let e=t.endObject();return e}static createField(t,e,r,i,s,o,a,c){return n.startField(t),n.addName(t,e),n.addNullable(t,r),n.addTypeType(t,i),n.addType(t,s),n.addDictionary(t,o),n.addChildren(t,a),n.addCustomMetadata(t,c),n.endField(t)}}e.Field=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static createBuffer(t,e,n){return t.prep(8,16),t.writeInt64(n),t.writeInt64(e),t.offset()}}t.Buffer=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}endianness(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):t.apache.arrow.flatbuf.Endianness.Little}fields(e,n){let r=this.bb.__offset(this.bb_pos,6);return r?(n||new t.apache.arrow.flatbuf.Field).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}fieldsLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(e,n){let r=this.bb.__offset(this.bb_pos,8);return r?(n||new t.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}customMetadataLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(3)}static addEndianness(e,n){e.addFieldInt16(0,n,t.apache.arrow.flatbuf.Endianness.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endSchema(t){let e=t.endObject();return e}static finishSchemaBuffer(t,e){t.finish(e)}static createSchema(t,e,r,i){return n.startSchema(t),n.addEndianness(t,e),n.addFields(t,r),n.addCustomMetadata(t,i),n.endSchema(t)}}e.Schema=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(U||(U={})),function(t){(function(t){(function(t){(function(t){t.Schema=U.apache.arrow.flatbuf.Schema})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(M||(M={})),function(t){(function(t){(function(t){(function(t){let e;(function(t){t[t["NONE"]=0]="NONE",t[t["Schema"]=1]="Schema",t[t["DictionaryBatch"]=2]="DictionaryBatch",t[t["RecordBatch"]=3]="RecordBatch",t[t["Tensor"]=4]="Tensor",t[t["SparseTensor"]=5]="SparseTensor"})(e=t.MessageHeader||(t.MessageHeader={}))})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(M||(M={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static createFieldNode(t,e,n){return t.prep(8,16),t.writeInt64(n),t.writeInt64(e),t.offset()}}t.FieldNode=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(M||(M={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}length(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}nodes(e,n){let r=this.bb.__offset(this.bb_pos,6);return r?(n||new t.apache.arrow.flatbuf.FieldNode).__init(this.bb.__vector(this.bb_pos+r)+16*e,this.bb):null}nodesLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){let n=this.bb.__offset(this.bb_pos,8);return n?(e||new U.apache.arrow.flatbuf.Buffer).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}buffersLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}static startRecordBatch(t){t.startObject(3)}static addLength(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static endRecordBatch(t){let e=t.endObject();return e}static createRecordBatch(t,e,r,i){return n.startRecordBatch(t),n.addLength(t,e),n.addNodes(t,r),n.addBuffers(t,i),n.endRecordBatch(t)}}e.RecordBatch=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(M||(M={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}id(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}data(e){let n=this.bb.__offset(this.bb_pos,6);return n?(e||new t.apache.arrow.flatbuf.RecordBatch).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}isDelta(){let t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){let e=t.endObject();return e}static createDictionaryBatch(t,e,r,i){return n.startDictionaryBatch(t),n.addId(t,e),n.addData(t,r),n.addIsDelta(t,i),n.endDictionaryBatch(t)}}e.DictionaryBatch=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(M||(M={})),function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}version(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):U.apache.arrow.flatbuf.MetadataVersion.V1}headerType(){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readUint8(this.bb_pos+e):t.apache.arrow.flatbuf.MessageHeader.NONE}header(t){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}customMetadata(t,e){let n=this.bb.__offset(this.bb_pos,12);return n?(e||new U.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,U.apache.arrow.flatbuf.MetadataVersion.V1)}static addHeaderType(e,n){e.addFieldInt8(1,n,t.apache.arrow.flatbuf.MessageHeader.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,t.createLong(0,0))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){let e=t.endObject();return e}static finishMessageBuffer(t,e){t.finish(e)}static createMessage(t,e,r,i,s,o){return n.startMessage(t),n.addVersion(t,e),n.addHeaderType(t,r),n.addHeader(t,i),n.addBodyLength(t,s),n.addCustomMetadata(t,o),n.endMessage(t)}}e.Message=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(M||(M={}));U.apache.arrow.flatbuf.Type;var k,R,V=U.apache.arrow.flatbuf.DateUnit,P=U.apache.arrow.flatbuf.TimeUnit,z=U.apache.arrow.flatbuf.Precision,$=U.apache.arrow.flatbuf.UnionMode,Y=U.apache.arrow.flatbuf.IntervalUnit,W=M.apache.arrow.flatbuf.MessageHeader,H=U.apache.arrow.flatbuf.MetadataVersion;function q(t,e,n,r){return 0!==(n&1<<r)}function K(t,e,n,r){return(n&1<<r)>>r}function G(t,e,n){return n?!!(t[e>>3]|=1<<e%8)||!0:!(t[e>>3]&=~(1<<e%8))&&!1}function J(t,e,n){const r=n.byteLength+7&-8;if(t>0||n.byteLength<r){const i=new Uint8Array(r);return i.set(t%8===0?n.subarray(t>>3):Z(X(n,t,e,null,q)).subarray(0,r)),i}return n}function Z(t){let e=[],n=0,r=0,i=0;for(const o of t)o&&(i|=1<<r),8===++r&&(e[n++]=i,i=r=0);(0===n||r>0)&&(e[n++]=i);let s=new Uint8Array(e.length+7&-8);return s.set(e),s}function*X(t,e,n,r,i){let s=e%8,o=e>>3,a=0,c=n;for(;c>0;s=0){let e=t[o++];do{yield i(r,a++,e,s)}while(--c>0&&++s<8)}}function Q(t,e,n){if(n-e<=0)return 0;if(n-e<8){let r=0;for(const i of X(t,e,n-e,t,K))r+=i;return r}const r=n>>3<<3,i=e+(e%8===0?0:8-e%8);return Q(t,e,i)+Q(t,r,n)+tt(t,i>>3,r-i>>3)}function tt(t,e,n){let r=0,i=0|e;const s=new DataView(t.buffer,t.byteOffset,t.byteLength),o=void 0===n?t.byteLength:i+n;while(o-i>=4)r+=et(s.getUint32(i)),i+=4;while(o-i>=2)r+=et(s.getUint16(i)),i+=2;while(o-i>=1)r+=et(s.getUint8(i)),i+=1;return r}function et(t){let e=0|t;return e-=e>>>1&1431655765,e=(858993459&e)+(e>>>2&858993459),16843009*(e+(e>>>4)&252645135)>>>24}(function(t){t[t["NONE"]=0]="NONE",t[t["Null"]=1]="Null",t[t["Int"]=2]="Int",t[t["Float"]=3]="Float",t[t["Binary"]=4]="Binary",t[t["Utf8"]=5]="Utf8",t[t["Bool"]=6]="Bool",t[t["Decimal"]=7]="Decimal",t[t["Date"]=8]="Date",t[t["Time"]=9]="Time",t[t["Timestamp"]=10]="Timestamp",t[t["Interval"]=11]="Interval",t[t["List"]=12]="List",t[t["Struct"]=13]="Struct",t[t["Union"]=14]="Union",t[t["FixedSizeBinary"]=15]="FixedSizeBinary",t[t["FixedSizeList"]=16]="FixedSizeList",t[t["Map"]=17]="Map",t[t["Dictionary"]=-1]="Dictionary",t[t["Int8"]=-2]="Int8",t[t["Int16"]=-3]="Int16",t[t["Int32"]=-4]="Int32",t[t["Int64"]=-5]="Int64",t[t["Uint8"]=-6]="Uint8",t[t["Uint16"]=-7]="Uint16",t[t["Uint32"]=-8]="Uint32",t[t["Uint64"]=-9]="Uint64",t[t["Float16"]=-10]="Float16",t[t["Float32"]=-11]="Float32",t[t["Float64"]=-12]="Float64",t[t["DateDay"]=-13]="DateDay",t[t["DateMillisecond"]=-14]="DateMillisecond",t[t["TimestampSecond"]=-15]="TimestampSecond",t[t["TimestampMillisecond"]=-16]="TimestampMillisecond",t[t["TimestampMicrosecond"]=-17]="TimestampMicrosecond",t[t["TimestampNanosecond"]=-18]="TimestampNanosecond",t[t["TimeSecond"]=-19]="TimeSecond",t[t["TimeMillisecond"]=-20]="TimeMillisecond",t[t["TimeMicrosecond"]=-21]="TimeMicrosecond",t[t["TimeNanosecond"]=-22]="TimeNanosecond",t[t["DenseUnion"]=-23]="DenseUnion",t[t["SparseUnion"]=-24]="SparseUnion",t[t["IntervalDayTime"]=-25]="IntervalDayTime",t[t["IntervalYearMonth"]=-26]="IntervalYearMonth"})(k||(k={})),function(t){t[t["OFFSET"]=0]="OFFSET",t[t["DATA"]=1]="DATA",t[t["VALIDITY"]=2]="VALIDITY",t[t["TYPE"]=3]="TYPE"}(R||(R={}));class nt{visitMany(t,...e){return t.map((t,n)=>this.visit(t,...e.map(t=>t[n])))}visit(...t){return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(t,e=!0){return rt(this,t,e)}visitNull(t,...e){return null}visitBool(t,...e){return null}visitInt(t,...e){return null}visitFloat(t,...e){return null}visitUtf8(t,...e){return null}visitBinary(t,...e){return null}visitFixedSizeBinary(t,...e){return null}visitDate(t,...e){return null}visitTimestamp(t,...e){return null}visitTime(t,...e){return null}visitDecimal(t,...e){return null}visitList(t,...e){return null}visitStruct(t,...e){return null}visitUnion(t,...e){return null}visitDictionary(t,...e){return null}visitInterval(t,...e){return null}visitFixedSizeList(t,...e){return null}visitMap(t,...e){return null}}function rt(t,e,n=!0){let r=null,i=k.NONE;switch(e instanceof ie||e instanceof N?i=it(e.type):e instanceof Ot?i=it(e):"number"!==typeof(i=e)&&(i=k[e]),i){case k.Null:r=t.visitNull;break;case k.Bool:r=t.visitBool;break;case k.Int:r=t.visitInt;break;case k.Int8:r=t.visitInt8||t.visitInt;break;case k.Int16:r=t.visitInt16||t.visitInt;break;case k.Int32:r=t.visitInt32||t.visitInt;break;case k.Int64:r=t.visitInt64||t.visitInt;break;case k.Uint8:r=t.visitUint8||t.visitInt;break;case k.Uint16:r=t.visitUint16||t.visitInt;break;case k.Uint32:r=t.visitUint32||t.visitInt;break;case k.Uint64:r=t.visitUint64||t.visitInt;break;case k.Float:r=t.visitFloat;break;case k.Float16:r=t.visitFloat16||t.visitFloat;break;case k.Float32:r=t.visitFloat32||t.visitFloat;break;case k.Float64:r=t.visitFloat64||t.visitFloat;break;case k.Utf8:r=t.visitUtf8;break;case k.Binary:r=t.visitBinary;break;case k.FixedSizeBinary:r=t.visitFixedSizeBinary;break;case k.Date:r=t.visitDate;break;case k.DateDay:r=t.visitDateDay||t.visitDate;break;case k.DateMillisecond:r=t.visitDateMillisecond||t.visitDate;break;case k.Timestamp:r=t.visitTimestamp;break;case k.TimestampSecond:r=t.visitTimestampSecond||t.visitTimestamp;break;case k.TimestampMillisecond:r=t.visitTimestampMillisecond||t.visitTimestamp;break;case k.TimestampMicrosecond:r=t.visitTimestampMicrosecond||t.visitTimestamp;break;case k.TimestampNanosecond:r=t.visitTimestampNanosecond||t.visitTimestamp;break;case k.Time:r=t.visitTime;break;case k.TimeSecond:r=t.visitTimeSecond||t.visitTime;break;case k.TimeMillisecond:r=t.visitTimeMillisecond||t.visitTime;break;case k.TimeMicrosecond:r=t.visitTimeMicrosecond||t.visitTime;break;case k.TimeNanosecond:r=t.visitTimeNanosecond||t.visitTime;break;case k.Decimal:r=t.visitDecimal;break;case k.List:r=t.visitList;break;case k.Struct:r=t.visitStruct;break;case k.Union:r=t.visitUnion;break;case k.DenseUnion:r=t.visitDenseUnion||t.visitUnion;break;case k.SparseUnion:r=t.visitSparseUnion||t.visitUnion;break;case k.Dictionary:r=t.visitDictionary;break;case k.Interval:r=t.visitInterval;break;case k.IntervalDayTime:r=t.visitIntervalDayTime||t.visitInterval;break;case k.IntervalYearMonth:r=t.visitIntervalYearMonth||t.visitInterval;break;case k.FixedSizeList:r=t.visitFixedSizeList;break;case k.Map:r=t.visitMap;break}if("function"===typeof r)return r;if(!n)return()=>null;throw new Error(`Unrecognized type '${k[i]}'`)}function it(t){switch(t.typeId){case k.Null:return k.Null;case k.Int:const{bitWidth:e,isSigned:n}=t;switch(e){case 8:return n?k.Int8:k.Uint8;case 16:return n?k.Int16:k.Uint16;case 32:return n?k.Int32:k.Uint32;case 64:return n?k.Int64:k.Uint64}return k.Int;case k.Float:switch(t.precision){case z.HALF:return k.Float16;case z.SINGLE:return k.Float32;case z.DOUBLE:return k.Float64}return k.Float;case k.Binary:return k.Binary;case k.Utf8:return k.Utf8;case k.Bool:return k.Bool;case k.Decimal:return k.Decimal;case k.Time:switch(t.unit){case P.SECOND:return k.TimeSecond;case P.MILLISECOND:return k.TimeMillisecond;case P.MICROSECOND:return k.TimeMicrosecond;case P.NANOSECOND:return k.TimeNanosecond}return k.Time;case k.Timestamp:switch(t.unit){case P.SECOND:return k.TimestampSecond;case P.MILLISECOND:return k.TimestampMillisecond;case P.MICROSECOND:return k.TimestampMicrosecond;case P.NANOSECOND:return k.TimestampNanosecond}return k.Timestamp;case k.Date:switch(t.unit){case V.DAY:return k.DateDay;case V.MILLISECOND:return k.DateMillisecond}return k.Date;case k.Interval:switch(t.unit){case Y.DAY_TIME:return k.IntervalDayTime;case Y.YEAR_MONTH:return k.IntervalYearMonth}return k.Interval;case k.Map:return k.Map;case k.List:return k.List;case k.Struct:return k.Struct;case k.Union:switch(t.mode){case $.Dense:return k.DenseUnion;case $.Sparse:return k.SparseUnion}return k.Union;case k.FixedSizeBinary:return k.FixedSizeBinary;case k.FixedSizeList:return k.FixedSizeList;case k.Dictionary:return k.Dictionary}throw new Error(`Unrecognized type '${k[t.typeId]}'`)}nt.prototype.visitInt8=null,nt.prototype.visitInt16=null,nt.prototype.visitInt32=null,nt.prototype.visitInt64=null,nt.prototype.visitUint8=null,nt.prototype.visitUint16=null,nt.prototype.visitUint32=null,nt.prototype.visitUint64=null,nt.prototype.visitFloat16=null,nt.prototype.visitFloat32=null,nt.prototype.visitFloat64=null,nt.prototype.visitDateDay=null,nt.prototype.visitDateMillisecond=null,nt.prototype.visitTimestampSecond=null,nt.prototype.visitTimestampMillisecond=null,nt.prototype.visitTimestampMicrosecond=null,nt.prototype.visitTimestampNanosecond=null,nt.prototype.visitTimeSecond=null,nt.prototype.visitTimeMillisecond=null,nt.prototype.visitTimeMicrosecond=null,nt.prototype.visitTimeNanosecond=null,nt.prototype.visitDenseUnion=null,nt.prototype.visitSparseUnion=null,nt.prototype.visitIntervalDayTime=null,nt.prototype.visitIntervalYearMonth=null;class st extends nt{compareSchemas(t,e){return t===e||e instanceof t.constructor&&wt.compareFields(t.fields,e.fields)}compareFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every((t,n)=>wt.compareField(t,e[n]))}compareField(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&wt.visit(t.type,e.type)}}function ot(t,e){return e instanceof t.constructor}function at(t,e){return t===e||ot(t,e)}function ct(t,e){return t===e||ot(t,e)&&t.bitWidth===e.bitWidth&&t.isSigned===e.isSigned}function ut(t,e){return t===e||ot(t,e)&&t.precision===e.precision}function lt(t,e){return t===e||ot(t,e)&&t.byteWidth===e.byteWidth}function ht(t,e){return t===e||ot(t,e)&&t.unit===e.unit}function ft(t,e){return t===e||ot(t,e)&&t.unit===e.unit&&t.timezone===e.timezone}function dt(t,e){return t===e||ot(t,e)&&t.unit===e.unit&&t.bitWidth===e.bitWidth}function pt(t,e){return t===e||ot(t,e)&&t.children.length===e.children.length&&wt.compareFields(t.children,e.children)}function yt(t,e){return t===e||ot(t,e)&&t.children.length===e.children.length&&wt.compareFields(t.children,e.children)}function bt(t,e){return t===e||ot(t,e)&&t.mode===e.mode&&t.typeIds.every((t,n)=>t===e.typeIds[n])&&wt.compareFields(t.children,e.children)}function gt(t,e){return t===e||ot(t,e)&&t.id===e.id&&t.isOrdered===e.isOrdered&&wt.visit(t.indices,e.indices)&&wt.visit(t.dictionary,e.dictionary)}function mt(t,e){return t===e||ot(t,e)&&t.unit===e.unit}function _t(t,e){return t===e||ot(t,e)&&t.listSize===e.listSize&&t.children.length===e.children.length&&wt.compareFields(t.children,e.children)}function vt(t,e){return t===e||ot(t,e)&&t.keysSorted===e.keysSorted&&t.children.length===e.children.length&&wt.compareFields(t.children,e.children)}st.prototype.visitNull=at,st.prototype.visitBool=at,st.prototype.visitInt=ct,st.prototype.visitInt8=ct,st.prototype.visitInt16=ct,st.prototype.visitInt32=ct,st.prototype.visitInt64=ct,st.prototype.visitUint8=ct,st.prototype.visitUint16=ct,st.prototype.visitUint32=ct,st.prototype.visitUint64=ct,st.prototype.visitFloat=ut,st.prototype.visitFloat16=ut,st.prototype.visitFloat32=ut,st.prototype.visitFloat64=ut,st.prototype.visitUtf8=at,st.prototype.visitBinary=at,st.prototype.visitFixedSizeBinary=lt,st.prototype.visitDate=ht,st.prototype.visitDateDay=ht,st.prototype.visitDateMillisecond=ht,st.prototype.visitTimestamp=ft,st.prototype.visitTimestampSecond=ft,st.prototype.visitTimestampMillisecond=ft,st.prototype.visitTimestampMicrosecond=ft,st.prototype.visitTimestampNanosecond=ft,st.prototype.visitTime=dt,st.prototype.visitTimeSecond=dt,st.prototype.visitTimeMillisecond=dt,st.prototype.visitTimeMicrosecond=dt,st.prototype.visitTimeNanosecond=dt,st.prototype.visitDecimal=at,st.prototype.visitList=pt,st.prototype.visitStruct=yt,st.prototype.visitUnion=bt,st.prototype.visitDenseUnion=bt,st.prototype.visitSparseUnion=bt,st.prototype.visitDictionary=gt,st.prototype.visitInterval=mt,st.prototype.visitIntervalDayTime=mt,st.prototype.visitIntervalYearMonth=mt,st.prototype.visitFixedSizeList=_t,st.prototype.visitMap=vt;const wt=new st;class Ot{static isNull(t){return t&&t.typeId===k.Null}static isInt(t){return t&&t.typeId===k.Int}static isFloat(t){return t&&t.typeId===k.Float}static isBinary(t){return t&&t.typeId===k.Binary}static isUtf8(t){return t&&t.typeId===k.Utf8}static isBool(t){return t&&t.typeId===k.Bool}static isDecimal(t){return t&&t.typeId===k.Decimal}static isDate(t){return t&&t.typeId===k.Date}static isTime(t){return t&&t.typeId===k.Time}static isTimestamp(t){return t&&t.typeId===k.Timestamp}static isInterval(t){return t&&t.typeId===k.Interval}static isList(t){return t&&t.typeId===k.List}static isStruct(t){return t&&t.typeId===k.Struct}static isUnion(t){return t&&t.typeId===k.Union}static isFixedSizeBinary(t){return t&&t.typeId===k.FixedSizeBinary}static isFixedSizeList(t){return t&&t.typeId===k.FixedSizeList}static isMap(t){return t&&t.typeId===k.Map}static isDictionary(t){return t&&t.typeId===k.Dictionary}get typeId(){return k.NONE}compareTo(t){return wt.visit(this,t)}}Ot[Symbol.toStringTag]=(t=>(t.children=null,t.ArrayType=Array,t[Symbol.toStringTag]="DataType"))(Ot.prototype);class It extends Ot{toString(){return"Null"}get typeId(){return k.Null}}It[Symbol.toStringTag]=(t=>t[Symbol.toStringTag]="Null")(It.prototype);class St extends Ot{constructor(t,e){super(),this.isSigned=t,this.bitWidth=e}get typeId(){return k.Int}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?Int32Array:Uint32Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}St[Symbol.toStringTag]=(t=>(t.isSigned=null,t.bitWidth=null,t[Symbol.toStringTag]="Int"))(St.prototype);class At extends St{constructor(){super(!0,8)}}class Tt extends St{constructor(){super(!0,16)}}class Bt extends St{constructor(){super(!0,32)}}class xt extends St{constructor(){super(!0,64)}}class jt extends St{constructor(){super(!1,8)}}class Et extends St{constructor(){super(!1,16)}}class Dt extends St{constructor(){super(!1,32)}}class Lt extends St{constructor(){super(!1,64)}}Object.defineProperty(At.prototype,"ArrayType",{value:Int8Array}),Object.defineProperty(Tt.prototype,"ArrayType",{value:Int16Array}),Object.defineProperty(Bt.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(xt.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(jt.prototype,"ArrayType",{value:Uint8Array}),Object.defineProperty(Et.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(Dt.prototype,"ArrayType",{value:Uint32Array}),Object.defineProperty(Lt.prototype,"ArrayType",{value:Uint32Array});class Ft extends Ot{constructor(t){super(),this.precision=t}get typeId(){return k.Float}get ArrayType(){switch(this.precision){case z.HALF:return Uint16Array;case z.SINGLE:return Float32Array;case z.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return"Float"+(this.precision<<5||16)}}Ft[Symbol.toStringTag]=(t=>(t.precision=null,t[Symbol.toStringTag]="Float"))(Ft.prototype);class Ut extends Ft{constructor(){super(z.HALF)}}class Mt extends Ft{constructor(){super(z.SINGLE)}}class Ct extends Ft{constructor(){super(z.DOUBLE)}}Object.defineProperty(Ut.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(Mt.prototype,"ArrayType",{value:Float32Array}),Object.defineProperty(Ct.prototype,"ArrayType",{value:Float64Array});class Nt extends Ot{constructor(){super()}get typeId(){return k.Binary}toString(){return"Binary"}}Nt[Symbol.toStringTag]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Binary"))(Nt.prototype);class kt extends Ot{constructor(){super()}get typeId(){return k.Utf8}toString(){return"Utf8"}}kt[Symbol.toStringTag]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Utf8"))(kt.prototype);class Rt extends Ot{constructor(){super()}get typeId(){return k.Bool}toString(){return"Bool"}}Rt[Symbol.toStringTag]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Bool"))(Rt.prototype);class Vt extends Ot{constructor(t,e){super(),this.scale=t,this.precision=e}get typeId(){return k.Decimal}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}}Vt[Symbol.toStringTag]=(t=>(t.scale=null,t.precision=null,t.ArrayType=Uint32Array,t[Symbol.toStringTag]="Decimal"))(Vt.prototype);class Pt extends Ot{constructor(t){super(),this.unit=t}get typeId(){return k.Date}toString(){return`Date${32*(this.unit+1)}<${V[this.unit]}>`}}Pt[Symbol.toStringTag]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Date"))(Pt.prototype);class zt extends Pt{constructor(){super(V.DAY)}}class $t extends Pt{constructor(){super(V.MILLISECOND)}}class Yt extends Ot{constructor(t,e){super(),this.unit=t,this.bitWidth=e}get typeId(){return k.Time}toString(){return`Time${this.bitWidth}<${P[this.unit]}>`}}Yt[Symbol.toStringTag]=(t=>(t.unit=null,t.bitWidth=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Time"))(Yt.prototype);class Wt extends Ot{constructor(t,e){super(),this.unit=t,this.timezone=e}get typeId(){return k.Timestamp}toString(){return`Timestamp<${P[this.unit]}${this.timezone?", "+this.timezone:""}>`}}Wt[Symbol.toStringTag]=(t=>(t.unit=null,t.timezone=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Timestamp"))(Wt.prototype);class Ht extends Ot{constructor(t){super(),this.unit=t}get typeId(){return k.Interval}toString(){return`Interval<${Y[this.unit]}>`}}Ht[Symbol.toStringTag]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Interval"))(Ht.prototype);class qt extends Ot{constructor(t){super(),this.children=[t]}get typeId(){return k.List}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}qt[Symbol.toStringTag]=(t=>(t.children=null,t[Symbol.toStringTag]="List"))(qt.prototype);class Kt extends Ot{constructor(t){super(),this.children=t}get typeId(){return k.Struct}toString(){return`Struct<{${this.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}Kt[Symbol.toStringTag]=(t=>(t.children=null,t[Symbol.toStringTag]="Struct"))(Kt.prototype);class Gt extends Ot{constructor(t,e,n){super(),this.mode=t,this.children=n,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce((t,e,n)=>(t[e]=n)&&t||t,Object.create(null))}get typeId(){return k.Union}toString(){return`${this[Symbol.toStringTag]}<${this.children.map(t=>""+t.type).join(" | ")}>`}}Gt[Symbol.toStringTag]=(t=>(t.mode=null,t.typeIds=null,t.children=null,t.typeIdToChildIndex=null,t.ArrayType=Int8Array,t[Symbol.toStringTag]="Union"))(Gt.prototype);class Jt extends Ot{constructor(t){super(),this.byteWidth=t}get typeId(){return k.FixedSizeBinary}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}Jt[Symbol.toStringTag]=(t=>(t.byteWidth=null,t.ArrayType=Uint8Array,t[Symbol.toStringTag]="FixedSizeBinary"))(Jt.prototype);class Zt extends Ot{constructor(t,e){super(),this.listSize=t,this.children=[e]}get typeId(){return k.FixedSizeList}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}Zt[Symbol.toStringTag]=(t=>(t.children=null,t.listSize=null,t[Symbol.toStringTag]="FixedSizeList"))(Zt.prototype);class Xt extends Ot{constructor(t,e=!1){super(),this.children=[t],this.keysSorted=e}get typeId(){return k.Map}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}toString(){return`Map<{${this.children[0].type.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}Xt[Symbol.toStringTag]=(t=>(t.children=null,t.keysSorted=null,t[Symbol.toStringTag]="Map_"))(Xt.prototype);const Qt=(t=>()=>++t)(-1);class te extends Ot{constructor(t,e,n,r){super(),this.indices=e,this.dictionary=t,this.isOrdered=r||!1,this.id=null==n?Qt():"number"===typeof n?n:n.low}get typeId(){return k.Dictionary}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}function ee(t){let e=t;switch(t.typeId){case k.Decimal:return 4;case k.Timestamp:return 2;case k.Date:return 1+e.unit;case k.Interval:return 1+e.unit;case k.Int:return+(e.bitWidth>32)+1;case k.Time:return+(e.bitWidth>32)+1;case k.FixedSizeList:return e.listSize;case k.FixedSizeBinary:return e.byteWidth;default:return 1}}te[Symbol.toStringTag]=(t=>(t.id=null,t.indices=null,t.isOrdered=null,t.dictionary=null,t[Symbol.toStringTag]="Dictionary"))(te.prototype);var ne=n("3eea");const re=-1;class ie{constructor(t,e,n,r,i,s,o){let a;this.type=t,this.dictionary=o,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(n||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1)),this.childData=(s||[]).map(t=>t instanceof ie?t:t.data),i instanceof ie?(this.stride=i.stride,this.values=i.values,this.typeIds=i.typeIds,this.nullBitmap=i.nullBitmap,this.valueOffsets=i.valueOffsets):(this.stride=ee(t),i&&((a=i[0])&&(this.valueOffsets=a),(a=i[1])&&(this.values=a),(a=i[2])&&(this.nullBitmap=a),(a=i[3])&&(this.typeIds=a)))}get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get byteLength(){let t=0,{valueOffsets:e,values:n,nullBitmap:r,typeIds:i}=this;return e&&(t+=e.byteLength),n&&(t+=n.byteLength),r&&(t+=r.byteLength),i&&(t+=i.byteLength),this.childData.reduce((t,e)=>t+e.byteLength,t)}get nullCount(){let t,e=this._nullCount;return e<=re&&(t=this.nullBitmap)&&(this._nullCount=e=this.length-Q(t,this.offset,this.offset+this.length)),e}clone(t,e=this.offset,n=this.length,r=this._nullCount,i=this,s=this.childData){return new ie(t,e,n,r,i,s,this.dictionary)}slice(t,e){const{stride:n,typeId:r,childData:i}=this,s=+(0===this._nullCount)-1,o=16===r?n:1,a=this._sliceBuffers(t,e,n,r);return this.clone(this.type,this.offset+t,e,s,a,!i.length||this.valueOffsets?i:this._sliceChildren(i,o*t,o*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===k.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:n}=this,r=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);r[e>>3]=(1<<e-(-8&e))-1,n>0&&r.set(J(this.offset,e,this.nullBitmap),0);const i=this.buffers;return i[R.VALIDITY]=r,this.clone(this.type,0,t,n+(t-e),i)}_sliceBuffers(t,e,n,r){let i,{buffers:s}=this;return(i=s[R.TYPE])&&(s[R.TYPE]=i.subarray(t,t+e)),(i=s[R.OFFSET])&&(s[R.OFFSET]=i.subarray(t,t+e+1))||(i=s[R.DATA])&&(s[R.DATA]=6===r?i:i.subarray(n*t,n*(t+e))),s}_sliceChildren(t,e,n){return t.map(t=>t.slice(e,n))}static new(t,e,n,r,i,s,o){switch(i instanceof ie?i=i.buffers:i||(i=[]),t.typeId){case k.Null:return ie.Null(t,e,n);case k.Int:return ie.Int(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.Dictionary:return ie.Dictionary(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[],o);case k.Float:return ie.Float(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.Bool:return ie.Bool(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.Decimal:return ie.Decimal(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.Date:return ie.Date(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.Time:return ie.Time(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.Timestamp:return ie.Timestamp(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.Interval:return ie.Interval(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.FixedSizeBinary:return ie.FixedSizeBinary(t,e,n,r||0,i[R.VALIDITY],i[R.DATA]||[]);case k.Binary:return ie.Binary(t,e,n,r||0,i[R.VALIDITY],i[R.OFFSET]||[],i[R.DATA]||[]);case k.Utf8:return ie.Utf8(t,e,n,r||0,i[R.VALIDITY],i[R.OFFSET]||[],i[R.DATA]||[]);case k.List:return ie.List(t,e,n,r||0,i[R.VALIDITY],i[R.OFFSET]||[],(s||[])[0]);case k.FixedSizeList:return ie.FixedSizeList(t,e,n,r||0,i[R.VALIDITY],(s||[])[0]);case k.Struct:return ie.Struct(t,e,n,r||0,i[R.VALIDITY],s||[]);case k.Map:return ie.Map(t,e,n,r||0,i[R.VALIDITY],i[R.OFFSET]||[],(s||[])[0]);case k.Union:return ie.Union(t,e,n,r||0,i[R.VALIDITY],i[R.TYPE]||[],i[R.OFFSET]||s,s)}throw new Error("Unrecognized typeId "+t.typeId)}static Null(t,e,n){return new ie(t,e,n,0)}static Int(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static Dictionary(t,e,n,r,i,s,o){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.indices.ArrayType,s),Object(ne["toUint8Array"])(i)],[],o)}static Float(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static Bool(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static Decimal(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static Date(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static Time(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static Timestamp(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static Interval(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static FixedSizeBinary(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,Object(ne["toArrayBufferView"])(t.ArrayType,s),Object(ne["toUint8Array"])(i)])}static Binary(t,e,n,r,i,s,o){return new ie(t,e,n,r,[Object(ne["toInt32Array"])(s),Object(ne["toUint8Array"])(o),Object(ne["toUint8Array"])(i)])}static Utf8(t,e,n,r,i,s,o){return new ie(t,e,n,r,[Object(ne["toInt32Array"])(s),Object(ne["toUint8Array"])(o),Object(ne["toUint8Array"])(i)])}static List(t,e,n,r,i,s,o){return new ie(t,e,n,r,[Object(ne["toInt32Array"])(s),void 0,Object(ne["toUint8Array"])(i)],[o])}static FixedSizeList(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,void 0,Object(ne["toUint8Array"])(i)],[s])}static Struct(t,e,n,r,i,s){return new ie(t,e,n,r,[void 0,void 0,Object(ne["toUint8Array"])(i)],s)}static Map(t,e,n,r,i,s,o){return new ie(t,e,n,r,[Object(ne["toInt32Array"])(s),void 0,Object(ne["toUint8Array"])(i)],[o])}static Union(t,e,n,r,i,s,o,a){const c=[void 0,void 0,Object(ne["toUint8Array"])(i),Object(ne["toArrayBufferView"])(t.ArrayType,s)];return t.mode===$.Sparse?new ie(t,e,n,r,c,o):(c[R.OFFSET]=Object(ne["toInt32Array"])(o),new ie(t,e,n,r,c,a))}}ie.prototype.childData=Object.freeze([]);const se=void 0;function oe(t){if(null===t)return"null";if(t===se)return"undefined";switch(typeof t){case"number":return""+t;case"bigint":return""+t;case"string":return`"${t}"`}return"function"===typeof t[Symbol.toPrimitive]?t[Symbol.toPrimitive]("string"):ArrayBuffer.isView(t)?`[${t}]`:JSON.stringify(t)}var ae=n("841f");function ce(t){if(!t||t.length<=0)return function(t){return!0};let e="",n=t.filter(t=>t===t);return n.length>0&&(e=`\n    switch (x) {${n.map(t=>`\n        case ${ue(t)}:`).join("")}\n            return false;\n    }`),t.length!==n.length&&(e="if (x !== x) return false;\n"+e),new Function("x",e+"\nreturn true;")}function ue(t){return"bigint"!==typeof t?oe(t):ae["c"]?oe(t)+"n":`"${oe(t)}"`}const le=(t,e)=>(t*e+63&-64||64)/e,he=(t,e=0)=>t.length>=e?t.subarray(0,e):Object(ne["memcpy"])(new t.constructor(e),t,0);class fe{constructor(t,e=1){this.buffer=t,this.stride=e,this.BYTES_PER_ELEMENT=t.BYTES_PER_ELEMENT,this.ArrayType=t.constructor,this._resize(this.length=t.length/e|0)}get byteLength(){return this.length*this.stride*this.BYTES_PER_ELEMENT|0}get reservedLength(){return this.buffer.length/this.stride}get reservedByteLength(){return this.buffer.byteLength}set(t,e){return this}append(t){return this.set(this.length,t)}reserve(t){if(t>0){this.length+=t;const e=this.stride,n=this.length*e,r=this.buffer.length;n>=r&&this._resize(le(0===r?1*n:2*n,this.BYTES_PER_ELEMENT))}return this}flush(t=this.length){t=le(t*this.stride,this.BYTES_PER_ELEMENT);const e=he(this.buffer,t);return this.clear(),e}clear(){return this.length=0,this._resize(0),this}_resize(t){return this.buffer=Object(ne["memcpy"])(new this.ArrayType(t),this.buffer)}}fe.prototype.offset=0;class de extends fe{last(){return this.get(this.length-1)}get(t){return this.buffer[t]}set(t,e){return this.reserve(t-this.length+1),this.buffer[t*this.stride]=e,this}}class pe extends de{constructor(t=new Uint8Array(0)){super(t,1/8),this.numValid=0}get numInvalid(){return this.length-this.numValid}get(t){return this.buffer[t>>3]>>t%8&1}set(t,e){const{buffer:n}=this.reserve(t-this.length+1),r=t>>3,i=t%8,s=n[r]>>i&1;return e?0===s&&(n[r]|=1<<i,++this.numValid):1===s&&(n[r]&=~(1<<i),--this.numValid),this}clear(){return this.numValid=0,super.clear()}}class ye extends de{constructor(t=new Int32Array(1)){super(t,1)}append(t){return this.set(this.length-1,t)}set(t,e){const n=this.length-1,r=this.reserve(t-n+1).buffer;return n<t++&&r.fill(r[n],n,t),r[t]=r[t-1]+e,this}flush(t=this.length-1){return t>this.length&&this.set(t-1,0),super.flush(t+1)}}class be extends fe{get ArrayType64(){return this._ArrayType64||(this._ArrayType64=this.buffer instanceof Int32Array?ae["b"]:ae["d"])}set(t,e){switch(this.reserve(t-this.length+1),typeof e){case"bigint":this.buffer64[t]=e;break;case"number":this.buffer[t*this.stride]=e;break;default:this.buffer.set(e,t*this.stride)}return this}_resize(t){const e=super._resize(t),n=e.byteLength/(this.BYTES_PER_ELEMENT*this.stride);return ae["c"]&&(this.buffer64=new this.ArrayType64(e.buffer,e.byteOffset,n)),e}}class ge{constructor({type:t,nullValues:e}){this.length=0,this.finished=!1,this.type=t,this.children=[],this.nullValues=e,this.stride=ee(t),this._nulls=new pe,e&&e.length>0&&(this._isValid=ce(e))}static new(t){}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t){throw new Error('"throughDOM" not available in this environment')}static throughIterable(t){return ve(t)}static throughAsyncIterable(t){return we(t)}toVector(){return N.new(this.flush())}get ArrayType(){return this.type.ArrayType}get nullCount(){return this._nulls.numInvalid}get numChildren(){return this.children.length}get byteLength(){let t=0;return this._offsets&&(t+=this._offsets.byteLength),this._values&&(t+=this._values.byteLength),this._nulls&&(t+=this._nulls.byteLength),this._typeIds&&(t+=this._typeIds.byteLength),this.children.reduce((t,e)=>t+e.byteLength,t)}get reservedLength(){return this._nulls.reservedLength}get reservedByteLength(){let t=0;return this._offsets&&(t+=this._offsets.reservedByteLength),this._values&&(t+=this._values.reservedByteLength),this._nulls&&(t+=this._nulls.reservedByteLength),this._typeIds&&(t+=this._typeIds.reservedByteLength),this.children.reduce((t,e)=>t+e.reservedByteLength,t)}get valueOffsets(){return this._offsets?this._offsets.buffer:null}get values(){return this._values?this._values.buffer:null}get nullBitmap(){return this._nulls?this._nulls.buffer:null}get typeIds(){return this._typeIds?this._typeIds.buffer:null}append(t){return this.set(this.length,t)}isValid(t){return this._isValid(t)}set(t,e){return this.setValid(t,this.isValid(e))&&this.setValue(t,e),this}setValue(t,e){this._setValue(this,t,e)}setValid(t,e){return this.length=this._nulls.set(t,+e).length,e}addChild(t,e=""+this.numChildren){throw new Error(`Cannot append children to non-nested type "${this.type}"`)}getChildAt(t){return this.children[t]||null}flush(){const t=[],e=this._values,n=this._offsets,r=this._typeIds,{length:i,nullCount:s}=this;r?(t[R.TYPE]=r.flush(i),n&&(t[R.OFFSET]=n.flush(i))):n?(e&&(t[R.DATA]=e.flush(n.last())),t[R.OFFSET]=n.flush(i)):e&&(t[R.DATA]=e.flush(i)),s>0&&(t[R.VALIDITY]=this._nulls.flush(i));const o=ie.new(this.type,0,i,s,t,this.children.map(t=>t.flush()));return this.clear(),o}finish(){return this.finished=!0,this.children.forEach(t=>t.finish()),this}clear(){return this.length=0,this._offsets&&this._offsets.clear(),this._values&&this._values.clear(),this._nulls&&this._nulls.clear(),this._typeIds&&this._typeIds.clear(),this.children.forEach(t=>t.clear()),this}}ge.prototype.length=1,ge.prototype.stride=1,ge.prototype.children=null,ge.prototype.finished=!1,ge.prototype.nullValues=null,ge.prototype._isValid=()=>!0;class me extends ge{constructor(t){super(t),this._values=new de(new this.ArrayType(0),this.stride)}setValue(t,e){const n=this._values;return n.reserve(t-n.length+1),super.setValue(t,e)}}class _e extends ge{constructor(t){super(t),this._pendingLength=0,this._offsets=new ye}setValue(t,e){const n=this._pending||(this._pending=new Map),r=n.get(t);r&&(this._pendingLength-=r.length),this._pendingLength+=e.length,n.set(t,e)}setValid(t,e){return!!super.setValid(t,e)||((this._pending||(this._pending=new Map)).set(t,void 0),!1)}clear(){return this._pendingLength=0,this._pending=void 0,super.clear()}flush(){return this._flush(),super.flush()}finish(){return this._flush(),super.finish()}_flush(){const t=this._pending,e=this._pendingLength;return this._pendingLength=0,this._pending=void 0,t&&t.size>0&&this._flushPending(t,e),this}}function ve(t){const{["queueingStrategy"]:e="count"}=t,{["highWaterMark"]:n=("bytes"!==e?1e3:16384)}=t,r="bytes"!==e?"length":"byteLength";return function*(e){let i=0,s=ge.new(t);for(const t of e)s.append(t)[r]>=n&&++i&&(yield s.toVector());(s.finish().length>0||0===i)&&(yield s.toVector())}}function we(t){const{["queueingStrategy"]:e="count"}=t,{["highWaterMark"]:n=("bytes"!==e?1e3:16384)}=t,r="bytes"!==e?"length":"byteLength";return async function*(e){let i=0,s=ge.new(t);for await(const t of e)s.append(t)[r]>=n&&++i&&(yield s.toVector());(s.finish().length>0||0===i)&&(yield s.toVector())}}class Oe extends ge{constructor(t){super(t),this._values=new pe}setValue(t,e){this._values.set(t,+e)}}class Ie extends ge{setValue(t,e){}setValid(t,e){return this.length=Math.max(t+1,this.length),e}}class Se extends me{}class Ae extends Se{}class Te extends Se{}class Be extends me{}class xe extends ge{constructor({type:t,nullValues:e,dictionaryHashFunction:n}){super({type:new te(t.dictionary,t.indices,t.id,t.isOrdered)}),this._nulls=null,this._dictionaryOffset=0,this._keysToIndices=Object.create(null),this.indices=ge.new({type:this.type.indices,nullValues:e}),this.dictionary=ge.new({type:this.type.dictionary,nullValues:null}),"function"===typeof n&&(this.valueToKey=n)}get values(){return this.indices.values}get nullCount(){return this.indices.nullCount}get nullBitmap(){return this.indices.nullBitmap}get byteLength(){return this.indices.byteLength+this.dictionary.byteLength}get reservedLength(){return this.indices.reservedLength+this.dictionary.reservedLength}get reservedByteLength(){return this.indices.reservedByteLength+this.dictionary.reservedByteLength}isValid(t){return this.indices.isValid(t)}setValid(t,e){const n=this.indices;return e=n.setValid(t,e),this.length=n.length,e}setValue(t,e){let n=this._keysToIndices,r=this.valueToKey(e),i=n[r];return void 0===i&&(n[r]=i=this._dictionaryOffset+this.dictionary.append(e).length-1),this.indices.setValue(t,i)}flush(){const t=this.type,e=this._dictionary,n=this.dictionary.toVector(),r=this.indices.flush().clone(t);return r.dictionary=e?e.concat(n):n,this.finished||(this._dictionaryOffset+=n.length),this._dictionary=r.dictionary,this.clear(),r}finish(){return this.indices.finish(),this.dictionary.finish(),this._dictionaryOffset=0,this._keysToIndices=Object.create(null),super.finish()}clear(){return this.indices.clear(),this.dictionary.clear(),super.clear()}valueToKey(t){return"string"===typeof t?t:""+t}}class je extends me{}const Ee=new Float64Array(1),De=new Uint32Array(Ee.buffer);function Le(t){let e=(31744&t)>>10,n=(1023&t)/1024,r=(-1)**((32768&t)>>15);switch(e){case 31:return r*(n?NaN:1/0);case 0:return r*(n?6103515625e-14*n:0)}return r*2**(e-15)*(1+n)}function Fe(t){if(t!==t)return 32256;Ee[0]=t;let e=(2147483648&De[1])>>16&65535,n=2146435072&De[1],r=0;return n>=1089470464?De[0]>0?n=31744:(n=(2080374784&n)>>16,r=(1048575&De[1])>>10):n<=1056964608?(r=1048576+(1048575&De[1]),r=1048576+(r<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,r=512+(1048575&De[1])>>10),e|n|65535&r}class Ue extends me{}class Me extends Ue{setValue(t,e){this._values.set(t,Fe(e))}}class Ce extends Ue{setValue(t,e){this._values.set(t,e)}}class Ne extends Ue{setValue(t,e){this._values.set(t,e)}}const ke=Symbol.for("isArrowBigNum");function Re(t,...e){return 0===e.length?Object.setPrototypeOf(Object(ne["toArrayBufferView"])(this["TypedArray"],t),this.constructor.prototype):Object.setPrototypeOf(new this["TypedArray"](t,...e),this.constructor.prototype)}function Ve(...t){return Re.apply(this,t)}function Pe(...t){return Re.apply(this,t)}function ze(...t){return Re.apply(this,t)}function $e(t){let e,n,{buffer:r,byteOffset:i,length:s,signed:o}=t,a=new Int32Array(r,i,s),c=0,u=0,l=a.length;while(u<l)n=a[u++],e=a[u++],o||(e>>>=0),c+=(n>>>0)+e*u**32;return c}let Ye,We;function He(t){let e="",n=new Uint32Array(2),r=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2),i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer),s=-1,o=r.length-1;do{for(n[0]=r[s=0];s<o;)r[s++]=n[1]=n[0]/10,n[0]=(n[0]-10*n[1]<<16)+r[s];r[s]=n[1]=n[0]/10,n[0]=n[0]-10*n[1],e=`${n[0]}${e}`}while(i[0]||i[1]||i[2]||i[3]);return e||"0"}Re.prototype[ke]=!0,Re.prototype.toJSON=function(){return`"${Ye(this)}"`},Re.prototype.valueOf=function(){return $e(this)},Re.prototype.toString=function(){return Ye(this)},Re.prototype[Symbol.toPrimitive]=function(t="default"){switch(t){case"number":return $e(this);case"string":return Ye(this);case"default":return We(this)}return Ye(this)},Object.setPrototypeOf(Ve.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf(Pe.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(ze.prototype,Object.create(Uint32Array.prototype)),Object.assign(Ve.prototype,Re.prototype,{constructor:Ve,signed:!0,TypedArray:Int32Array,BigIntArray:ae["b"]}),Object.assign(Pe.prototype,Re.prototype,{constructor:Pe,signed:!1,TypedArray:Uint32Array,BigIntArray:ae["d"]}),Object.assign(ze.prototype,Re.prototype,{constructor:ze,signed:!0,TypedArray:Uint32Array,BigIntArray:ae["d"]}),ae["c"]?(We=t=>8===t.byteLength?new t["BigIntArray"](t.buffer,t.byteOffset,1)[0]:He(t),Ye=t=>8===t.byteLength?""+new t["BigIntArray"](t.buffer,t.byteOffset,1)[0]:He(t)):(Ye=He,We=Ye);class qe{constructor(t,e){return qe.new(t,e)}static new(t,e){switch(e){case!0:return new Ve(t);case!1:return new Pe(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case ae["b"]:return new Ve(t)}return 16===t.byteLength?new ze(t):new Pe(t)}static signed(t){return new Ve(t)}static unsigned(t){return new Pe(t)}static decimal(t){return new ze(t)}}class Ke extends me{setValue(t,e){this._values.set(t,e)}}class Ge extends Ke{}class Je extends Ke{}class Ze extends Ke{}class Xe extends Ke{constructor(t){t["nullValues"]&&(t["nullValues"]=t["nullValues"].map(rn)),super(t),this._values=new be(new Int32Array(0),2)}get values64(){return this._values.buffer64}isValid(t){return super.isValid(rn(t))}}class Qe extends Ke{}class tn extends Ke{}class en extends Ke{}class nn extends Ke{constructor(t){t["nullValues"]&&(t["nullValues"]=t["nullValues"].map(rn)),super(t),this._values=new be(new Uint32Array(0),2)}get values64(){return this._values.buffer64}isValid(t){return super.isValid(rn(t))}}const rn=(t=>e=>(ArrayBuffer.isView(e)&&(t.buffer=e.buffer,t.byteOffset=e.byteOffset,t.byteLength=e.byteLength,e=We(t),t.buffer=null),e))({BigIntArray:ae["b"]});class sn extends me{}class on extends sn{}class an extends sn{}class cn extends sn{}class un extends sn{}class ln extends me{}class hn extends ln{}class fn extends ln{}class dn extends ln{}class pn extends ln{}class yn extends me{}class bn extends yn{}class gn extends yn{}var mn=n("5217");class _n extends _e{constructor(t){super(t),this._values=new fe(new Uint8Array(0))}get byteLength(){let t=this._pendingLength+4*this.length;return this._offsets&&(t+=this._offsets.byteLength),this._values&&(t+=this._values.byteLength),this._nulls&&(t+=this._nulls.byteLength),t}setValue(t,e){return super.setValue(t,Object(ne["toUint8Array"])(e))}_flushPending(t,e){const n=this._offsets,r=this._values.reserve(e).buffer;let i,s=0,o=0,a=0;for([s,i]of t)void 0===i?n.set(s,0):(o=i.length,r.set(i,a),n.set(s,o),a+=o)}}class vn extends _e{constructor(t){super(t),this._values=new fe(new Uint8Array(0))}get byteLength(){let t=this._pendingLength+4*this.length;return this._offsets&&(t+=this._offsets.byteLength),this._values&&(t+=this._values.byteLength),this._nulls&&(t+=this._nulls.byteLength),t}setValue(t,e){return super.setValue(t,Object(mn["b"])(e))}_flushPending(t,e){}}vn.prototype._flushPending=_n.prototype._flushPending;class wn{get length(){return this._values.length}get(t){return this._values[t]}clear(){return this._values=null,this}bind(t){return t instanceof N?t:(this._values=t,this)}}const On=Symbol.for("parent"),In=Symbol.for("rowIndex"),Sn=Symbol.for("keyToIdx"),An=Symbol.for("idxToVal"),Tn=Symbol.for("nodejs.util.inspect.custom");class Bn{constructor(t,e){this[On]=t,this.size=e}entries(){return this[Symbol.iterator]()}has(t){return void 0!==this.get(t)}get(t){let e=void 0;if(null!==t&&void 0!==t){const n=this[Sn]||(this[Sn]=new Map);let r=n.get(t);if(void 0!==r){const t=this[An]||(this[An]=new Array(this.size));void 0!==(e=t[r])||(t[r]=e=this.getValue(r))}else if((r=this.getIndex(t))>-1){n.set(t,r);const i=this[An]||(this[An]=new Array(this.size));void 0!==(e=i[r])||(i[r]=e=this.getValue(r))}}return e}set(t,e){if(null!==t&&void 0!==t){const n=this[Sn]||(this[Sn]=new Map);let r=n.get(t);if(void 0===r&&n.set(t,r=this.getIndex(t)),r>-1){const t=this[An]||(this[An]=new Array(this.size));t[r]=this.setValue(r,e)}}return this}clear(){throw new Error(`Clearing ${this[Symbol.toStringTag]} not supported.`)}delete(t){throw new Error(`Deleting ${this[Symbol.toStringTag]} values not supported.`)}*[Symbol.iterator](){const t=this.keys(),e=this.values(),n=this[Sn]||(this[Sn]=new Map),r=this[An]||(this[An]=new Array(this.size));for(let i,s,o,a,c=0;!(o=t.next()).done&&!(a=e.next()).done;++c)i=o.value,s=a.value,r[c]=s,n.has(i)||n.set(i,c),yield[i,s]}forEach(t,e){const n=this.keys(),r=this.values(),i=void 0===e?t:(n,r,i)=>t.call(e,n,r,i),s=this[Sn]||(this[Sn]=new Map),o=this[An]||(this[An]=new Array(this.size));for(let a,c,u,l,h=0;!(u=n.next()).done&&!(l=r.next()).done;++h)a=u.value,c=l.value,o[h]=c,s.has(a)||s.set(a,h),i(c,a,this)}toArray(){return[...this.values()]}toJSON(){const t={};return this.forEach((e,n)=>t[n]=e),t}inspect(){return this.toString()}[Tn](){return this.toString()}toString(){const t=[];return this.forEach((e,n)=>{n=oe(n),e=oe(e),t.push(`${n}: ${e}`)}),`{ ${t.join(", ")} }`}}Bn[Symbol.toStringTag]=(t=>(Object.defineProperties(t,{size:{writable:!0,enumerable:!1,configurable:!1,value:0},[On]:{writable:!0,enumerable:!1,configurable:!1,value:null},[In]:{writable:!0,enumerable:!1,configurable:!1,value:-1}}),t[Symbol.toStringTag]="Row"))(Bn.prototype);class xn extends Bn{constructor(t){return super(t,t.length),Dn(this)}keys(){return this[On].getChildAt(0)[Symbol.iterator]()}values(){return this[On].getChildAt(1)[Symbol.iterator]()}getKey(t){return this[On].getChildAt(0).get(t)}getIndex(t){return this[On].getChildAt(0).indexOf(t)}getValue(t){return this[On].getChildAt(1).get(t)}setValue(t,e){this[On].getChildAt(1).set(t,e)}}class jn extends Bn{constructor(t){return super(t,t.type.children.length),En(this)}*keys(){for(const t of this[On].type.children)yield t.name}*values(){for(const t of this[On].type.children)yield this[t.name]}getKey(t){return this[On].type.children[t].name}getIndex(t){return this[On].type.children.findIndex(e=>e.name===t)}getValue(t){return this[On].getChildAt(t).get(this[In])}setValue(t,e){return this[On].getChildAt(t).set(this[In],e)}}Object.setPrototypeOf(Bn.prototype,Map.prototype);const En=(()=>{const t={enumerable:!0,configurable:!1,get:null,set:null};return e=>{let n=-1,r=e[Sn]||(e[Sn]=new Map);const i=t=>function(){return this.get(t)},s=t=>function(e){return this.set(t,e)};for(const o of e.keys())r.set(o,++n),t.get=i(o),t.set=s(o),e.hasOwnProperty(o)||(t.enumerable=!0,Object.defineProperty(e,o,t)),e.hasOwnProperty(n)||(t.enumerable=!1,Object.defineProperty(e,n,t));return t.get=t.set=null,e}})(),Dn=(()=>{if("undefined"===typeof Proxy)return En;const t=Bn.prototype.has,e=Bn.prototype.get,n=Bn.prototype.set,r=Bn.prototype.getKey,i={isExtensible(){return!1},deleteProperty(){return!1},preventExtensions(){return!0},ownKeys(t){return[...t.keys()].map(t=>""+t)},has(t,e){switch(e){case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:case On:case In:case An:case Sn:case Tn:return!0}return"number"!==typeof e||t.has(e)||(e=t.getKey(e)),t.has(e)},get(n,i,s){switch(i){case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:case On:case In:case An:case Sn:case Tn:return Reflect.get(n,i,s)}return"number"!==typeof i||t.call(s,i)||(i=r.call(s,i)),e.call(s,i)},set(e,i,s,o){switch(i){case On:case In:case An:case Sn:return Reflect.set(e,i,s,o);case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:return!1}return"number"!==typeof i||t.call(o,i)||(i=r.call(o,i)),!!t.call(o,i)&&!!n.call(o,i,s)}};return t=>new Proxy(t,i)})();function Ln(t,e,n){const r=t.length,i=e>-1?e:r+e%r;return n?n(t,i):i}let Fn;function Un(t,e,n,r){let{length:i=0}=t,s="number"!==typeof e?0:e,o="number"!==typeof n?i:n;return s<0&&(s=(s%i+i)%i),o<0&&(o=(o%i+i)%i),o<s&&(Fn=s,s=o,o=Fn),o>i&&(o=i),r?r(t,s,o):[s,o]}const Mn=ae["c"]?Object(ae["a"])(0):0,Cn=t=>t!==t;function Nn(t){let e=typeof t;if("object"!==e||null===t)return Cn(t)?Cn:"bigint"!==e?e=>e===t:e=>Mn+e===t;if(t instanceof Date){const e=t.valueOf();return t=>t instanceof Date&&t.valueOf()===e}return ArrayBuffer.isView(t)?e=>!!e&&Object(ne["compareArrayLike"])(t,e):t instanceof Map?Rn(t):Array.isArray(t)?kn(t):t instanceof N?Vn(t):Pn(t)}function kn(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=Nn(t[n]);return zn(e)}function Rn(t){let e=-1;const n=[];return t.forEach(t=>n[++e]=Nn(t)),zn(n)}function Vn(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=Nn(t.get(n));return zn(e)}function Pn(t){const e=Object.keys(t);if(0===e.length)return()=>!1;const n=[];for(let r=-1,i=e.length;++r<i;)n[r]=Nn(t[e[r]]);return zn(n,e)}function zn(t,e){return n=>{if(!n||"object"!==typeof n)return!1;switch(n.constructor){case Array:return $n(t,n);case Map:case xn:case jn:return Wn(t,n,n.keys());case Object:case void 0:return Wn(t,n,e||Object.keys(n))}return n instanceof N&&Yn(t,n)}}function $n(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e[r]))return!1;return!0}function Yn(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e.get(r)))return!1;return!0}function Wn(t,e,n){const r=n[Symbol.iterator](),i=e instanceof Map?e.keys():Object.keys(e)[Symbol.iterator](),s=e instanceof Map?e.values():Object.values(e)[Symbol.iterator]();let o=0,a=t.length,c=s.next(),u=r.next(),l=i.next();for(;o<a&&!u.done&&!l.done&&!c.done;++o,u=r.next(),l=i.next(),c=s.next())if(u.value!==l.value||!t[o](c.value))break;return!!(o===a&&u.done&&l.done&&c.done)||(r.return&&r.return(),i.return&&i.return(),s.return&&s.return(),!1)}class Hn extends N{constructor(t,e=[],n=qn(e)){super(),this._nullCount=-1,this._type=t,this._chunks=e,this._chunkOffsets=n,this._length=n[n.length-1],this._numChildren=(this._type.children||[]).length}static flatten(...t){return nr(N,t)}static concat(...t){const e=Hn.flatten(...t);return new Hn(e[0].type,e)}get type(){return this._type}get length(){return this._length}get chunks(){return this._chunks}get typeId(){return this._type.typeId}get VectorName(){return`Chunked<${this._type}>`}get data(){return this._chunks[0]?this._chunks[0].data:null}get ArrayType(){return this._type.ArrayType}get numChildren(){return this._numChildren}get stride(){return this._chunks[0]?this._chunks[0].stride:1}get byteLength(){return this._chunks.reduce((t,e)=>t+e.byteLength,0)}get nullCount(){let t=this._nullCount;return t<0&&(this._nullCount=t=this._chunks.reduce((t,{nullCount:e})=>t+e,0)),t}get indices(){if(Ot.isDictionary(this._type)){if(!this._indices){const t=this._chunks;this._indices=1===t.length?t[0].indices:Hn.concat(...t.map(t=>t.indices))}return this._indices}return null}get dictionary(){return Ot.isDictionary(this._type)?this._chunks[this._chunks.length-1].data.dictionary:null}*[Symbol.iterator](){for(const t of this._chunks)yield*t}clone(t=this._chunks){return new Hn(this._type,t)}concat(...t){return this.clone(Hn.flatten(this,...t))}slice(t,e){return Un(this,t,e,this._sliceInternal)}getChildAt(t){if(t<0||t>=this._numChildren)return null;let e,n,r,i=this._children||(this._children=[]);return(e=i[t])?e:(n=(this._type.children||[])[t])&&(r=this._chunks.map(e=>e.getChildAt(t)).filter(t=>null!=t),r.length>0)?i[t]=new Hn(n.type,r):null}search(t,e){let n=t,r=this._chunkOffsets,i=r.length-1;if(n<0)return null;if(n>=r[i])return null;if(i<=1)return e?e(this,0,n):[0,n];let s=0,o=0,a=0;do{if(s+1===i)return e?e(this,s,n-o):[s,n-o];a=s+(i-s)/2|0,n>=r[a]?s=a:i=a}while(n<r[i]&&n>=(o=r[s]));return null}isValid(t){return!!this.search(t,this.isValidInternal)}get(t){return this.search(t,this.getInternal)}set(t,e){this.search(t,({chunks:t},n,r)=>t[n].set(r,e))}indexOf(t,e){return e&&"number"===typeof e?this.search(e,(e,n,r)=>this.indexOfInternal(e,n,r,t)):this.indexOfInternal(this,0,Math.max(0,e||0),t)}toArray(){const{chunks:t}=this,e=t.length;let n=this._type.ArrayType;if(e<=0)return new n(0);if(e<=1)return t[0].toArray();let r=0,i=new Array(e);for(let a=-1;++a<e;)r+=(i[a]=t[a].toArray()).length;n!==i[0].constructor&&(n=i[0].constructor);let s=new n(r),o=n===Array?Gn:Kn;for(let a=-1,c=0;++a<e;)c=o(i[a],s,c);return s}getInternal({_chunks:t},e,n){return t[e].get(n)}isValidInternal({_chunks:t},e,n){return t[e].isValid(n)}indexOfInternal({_chunks:t},e,n,r){let i=e-1,s=t.length,o=n,a=0,c=-1;while(++i<s){if(~(c=t[i].indexOf(r,o)))return a+c;o=0,a+=t[i].length}return-1}_sliceInternal(t,e,n){const r=[],{chunks:i,_chunkOffsets:s}=t;for(let o=-1,a=i.length;++o<a;){const t=i[o],a=t.length,c=s[o];if(c>=n)break;if(e>=c+a)continue;if(c>=e&&c+a<=n){r.push(t);continue}const u=Math.max(0,e-c),l=Math.min(n-c,a);r.push(t.slice(u,l))}return t.clone(r)}}function qn(t){let e=new Uint32Array((t||[]).length+1),n=e[0]=0,r=e.length;for(let i=0;++i<r;)e[i]=n+=t[i-1].length;return e}const Kn=(t,e,n)=>(e.set(t,n),n+t.length),Gn=(t,e,n)=>{let r=n;for(let i=-1,s=t.length;++i<s;)e[r++]=t[i];return r};class Jn extends Hn{constructor(t,e=[],n){if(e=Hn.flatten(...e),super(t.type,e,n),this._field=t,1===e.length&&!(this instanceof Zn))return new Zn(t,e[0],this._chunkOffsets)}static new(t,e,...n){const r=Hn.flatten(Array.isArray(e)?[...e,...n]:e instanceof N?[e,...n]:[N.new(e,...n)]);if("string"===typeof t){const e=r[0].data.type;t=new lr(t,e,!0)}else!t.nullable&&r.some(({nullCount:t})=>t>0)&&(t=t.clone({nullable:!0}));return new Jn(t,r)}get field(){return this._field}get name(){return this._field.name}get nullable(){return this._field.nullable}get metadata(){return this._field.metadata}clone(t=this._chunks){return new Jn(this._field,t)}getChildAt(t){if(t<0||t>=this.numChildren)return null;let e,n,r,i=this._children||(this._children=[]);return(e=i[t])?e:(n=(this.type.children||[])[t])&&(r=this._chunks.map(e=>e.getChildAt(t)).filter(t=>null!=t),r.length>0)?i[t]=new Jn(n,r):null}}class Zn extends Jn{constructor(t,e,n){super(t,[e],n),this._chunk=e}search(t,e){return e?e(this,0,t):[0,t]}isValid(t){return this._chunk.isValid(t)}get(t){return this._chunk.get(t)}set(t,e){this._chunk.set(t,e)}indexOf(t,e){return this._chunk.indexOf(t,e)}}const Xn=Array.isArray,Qn=(t,e)=>ir(t,e,[],0),tr=t=>{const[e,n]=cr(t,[[],[]]);return n.map((t,n)=>t instanceof Jn?Jn.new(t.field.clone(e[n]),t):t instanceof N?Jn.new(e[n],t):Jn.new(e[n],[]))},er=t=>cr(t,[[],[]]),nr=(t,e)=>sr(t,e,[],0),rr=(t,e)=>or(t,e,[],0);function ir(t,e,n,r){let i,s=r,o=-1,a=e.length;while(++o<a)Xn(i=e[o])?s=ir(t,i,n,s).length:i instanceof t&&(n[s++]=i);return n}function sr(t,e,n,r){let i,s=r,o=-1,a=e.length;while(++o<a)Xn(i=e[o])?s=sr(t,i,n,s).length:i instanceof Hn?s=sr(t,i.chunks,n,s).length:i instanceof t&&(n[s++]=i);return n}function or(t,e,n,r){let i,s=r,o=-1,a=e.length;while(++o<a)Xn(i=e[o])?s=or(t,i,n,s).length:i instanceof t?s=ir(N,i.schema.fields.map((t,e)=>i.getChildAt(e)),n,s).length:i instanceof N&&(n[s++]=i);return n}const ar=(t,[e,n],r)=>(t[0][r]=e,t[1][r]=n,t);function cr(t,e){let n,r;switch(r=t.length){case 0:return e;case 1:if(n=e[0],!t[0])return e;if(Xn(t[0]))return cr(t[0],e);t[0]instanceof ie||t[0]instanceof N||t[0]instanceof Ot||([n,t]=Object.entries(t[0]).reduce(ar,e));break;default:Xn(n=t[r-1])?t=Xn(t[0])?t[0]:t.slice(0,r-1):(t=Xn(t[0])?t[0]:t,n=[])}let i,s,o=-1,a=-1,c=-1,u=t.length,[l,h]=e;while(++c<u)s=t[c],s instanceof Jn&&(h[++a]=s)?l[++o]=s.field.clone(n[c],s.type,!0):(({[c]:i=c}=n),s instanceof Ot&&(h[++a]=s)?l[++o]=lr.new(i,s,!0):s&&s.type&&(h[++a]=s)&&(s instanceof ie&&(h[a]=s=N.new(s)),l[++o]=lr.new(i,s.type,!0)));return e}class ur{constructor(t=[],e,n){this.fields=t||[],this.metadata=e||new Map,n||(n=fr(t)),this.dictionaries=n}static from(...t){return ur.new(t[0],t[1])}static new(...t){return new ur(er(t)[0])}get[Symbol.toStringTag](){return"Schema"}toString(){return`Schema<{ ${this.fields.map((t,e)=>`${e}: ${t}`).join(", ")} }>`}compareTo(t){return wt.compareSchemas(this,t)}select(...t){const e=t.reduce((t,e)=>(t[e]=!0)&&t,Object.create(null));return new ur(this.fields.filter(t=>e[t.name]),this.metadata)}selectAt(...t){return new ur(t.map(t=>this.fields[t]).filter(Boolean),this.metadata)}assign(...t){const e=t[0]instanceof ur?t[0]:new ur(Qn(lr,t)),n=[...this.fields],r=hr(hr(new Map,this.metadata),e.metadata),i=e.fields.filter(t=>{const e=n.findIndex(e=>e.name===t.name);return!~e||(n[e]=t.clone({metadata:hr(hr(new Map,n[e].metadata),t.metadata)}))&&!1}),s=fr(i,new Map);return new ur([...n,...i],r,new Map([...this.dictionaries,...s]))}}class lr{constructor(t,e,n=!1,r){this.name=t,this.type=e,this.nullable=n,this.metadata=r||new Map}static new(...t){let[e,n,r,i]=t;return t[0]&&"object"===typeof t[0]&&(({name:e}=t[0]),void 0===n&&(n=t[0].type),void 0===r&&(r=t[0].nullable),void 0===i&&(i=t[0].metadata)),new lr(""+e,n,r,i)}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}compareTo(t){return wt.compareField(this,t)}clone(...t){let[e,n,r,i]=t;return t[0]&&"object"===typeof t[0]?({name:e=this.name,type:n=this.type,nullable:r=this.nullable,metadata:i=this.metadata}=t[0]):[e=this.name,n=this.type,r=this.nullable,i=this.metadata]=t,lr.new(e,n,r,i)}}function hr(t,e){return new Map([...t||new Map,...e||new Map])}function fr(t,e=new Map){for(let n=-1,r=t.length;++n<r;){const r=t[n],i=r.type;if(Ot.isDictionary(i))if(e.has(i.id)){if(e.get(i.id)!==i.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else e.set(i.id,i.dictionary);i.children&&i.children.length>0&&fr(i.children,e)}return e}ur.prototype.fields=null,ur.prototype.metadata=null,ur.prototype.dictionaries=null,lr.prototype.type=null,lr.prototype.name=null,lr.prototype.nullable=null,lr.prototype.metadata=null;class dr extends _e{constructor(t){super(t),this._run=new wn,this._offsets=new ye}addChild(t,e="0"){if(this.numChildren>0)throw new Error("ListBuilder can only have one child.");return this.children[this.numChildren]=t,this.type=new qt(new lr(e,t.type,!0)),this.numChildren-1}clear(){return this._run.clear(),super.clear()}_flushPending(t){const e=this._run,n=this._offsets,r=this._setValue;let i,s=0;for([s,i]of t)void 0===i?n.set(s,0):(n.set(s,i.length),r(this,s,e.bind(i)))}}class pr extends ge{constructor(){super(...arguments),this._run=new wn}setValue(t,e){super.setValue(t,this._run.bind(e))}addChild(t,e="0"){if(this.numChildren>0)throw new Error("FixedSizeListBuilder can only have one child.");const n=this.children.push(t);return this.type=new Zt(this.type.listSize,new lr(e,t.type,!0)),n}clear(){return this._run.clear(),super.clear()}}class yr extends _e{set(t,e){return super.set(t,e)}setValue(t,e){e=e instanceof Map?e:new Map(Object.entries(e));const n=this._pending||(this._pending=new Map),r=n.get(t);r&&(this._pendingLength-=r.size),this._pendingLength+=e.size,n.set(t,e)}addChild(t,e=""+this.numChildren){if(this.numChildren>0)throw new Error("ListBuilder can only have one child.");return this.children[this.numChildren]=t,this.type=new Xt(new lr(e,t.type,!0),this.type.keysSorted),this.numChildren-1}_flushPending(t){const e=this._offsets,n=this._setValue;t.forEach((t,r)=>{void 0===t?e.set(r,0):(e.set(r,t.size),n(this,r,t))})}}class br extends ge{addChild(t,e=""+this.numChildren){const n=this.children.push(t);return this.type=new Kt([...this.type.children,new lr(e,t.type,!0)]),n}}class gr extends ge{constructor(t){super(t),this._typeIds=new de(new Int8Array(0),1),"function"===typeof t["valueToChildTypeId"]&&(this._valueToChildTypeId=t["valueToChildTypeId"])}get typeIdToChildIndex(){return this.type.typeIdToChildIndex}append(t,e){return this.set(this.length,t,e)}set(t,e,n){return void 0===n&&(n=this._valueToChildTypeId(this,e,t)),this.setValid(t,this.isValid(e))&&this.setValue(t,e,n),this}setValue(t,e,n){this._typeIds.set(t,n),super.setValue(t,e)}addChild(t,e=""+this.children.length){const n=this.children.push(t),{type:{children:r,mode:i,typeIds:s}}=this,o=[...r,new lr(e,t.type)];return this.type=new Gt(i,[...s,n],o),n}_valueToChildTypeId(t,e,n){throw new Error("Cannot map UnionBuilder value to child typeId. Pass the `childTypeId` as the second argument to unionBuilder.append(), or supply a `valueToChildTypeId` function as part of the UnionBuilder constructor options.")}}class mr extends gr{}class _r extends gr{constructor(t){super(t),this._offsets=new de(new Int32Array(0))}setValue(t,e,n){const r=this.type.typeIdToChildIndex[n];return this._offsets.set(t,this.getChildAt(r).length),super.setValue(t,e,n)}}class vr extends nt{}const wr=(t,e,n)=>{t[e]=n/864e5|0},Or=(t,e,n)=>{t[e]=n%4294967296|0,t[e+1]=n/4294967296|0},Ir=(t,e,n)=>{t[e]=1e3*n%4294967296|0,t[e+1]=1e3*n/4294967296|0},Sr=(t,e,n)=>{t[e]=1e6*n%4294967296|0,t[e+1]=1e6*n/4294967296|0},Ar=(t,e,n,r)=>{const{[n]:i,[n+1]:s}=e;null!=i&&null!=s&&t.set(r.subarray(0,s-i),i)},Tr=({offset:t,values:e},n,r)=>{const i=t+n;r?e[i>>3]|=1<<i%8:e[i>>3]&=~(1<<i%8)},Br=({values:t},e,n)=>{wr(t,e,n.valueOf())},xr=({values:t},e,n)=>{Or(t,2*e,n.valueOf())},jr=({stride:t,values:e},n,r)=>{e[t*n]=r},Er=({stride:t,values:e},n,r)=>{e[t*n]=Fe(r)},Dr=(t,e,n)=>{switch(typeof n){case"bigint":t.values64[e]=n;break;case"number":t.values[e*t.stride]=n;break;default:const r=n,{stride:i,ArrayType:s}=t,o=Object(ne["toArrayBufferView"])(s,r);t.values.set(o.subarray(0,i),i*e)}},Lr=({stride:t,values:e},n,r)=>{e.set(r.subarray(0,t),t*n)},Fr=({values:t,valueOffsets:e},n,r)=>Ar(t,e,n,r),Ur=({values:t,valueOffsets:e},n,r)=>{Ar(t,e,n,Object(mn["b"])(r))},Mr=(t,e,n)=>{t.type.bitWidth<64?jr(t,e,n):Dr(t,e,n)},Cr=(t,e,n)=>{t.type.precision!==z.HALF?jr(t,e,n):Er(t,e,n)},Nr=(t,e,n)=>{t.type.unit===V.DAY?Br(t,e,n):xr(t,e,n)},kr=({values:t},e,n)=>Or(t,2*e,n/1e3),Rr=({values:t},e,n)=>Or(t,2*e,n),Vr=({values:t},e,n)=>Ir(t,2*e,n),Pr=({values:t},e,n)=>Sr(t,2*e,n),zr=(t,e,n)=>{switch(t.type.unit){case P.SECOND:return kr(t,e,n);case P.MILLISECOND:return Rr(t,e,n);case P.MICROSECOND:return Vr(t,e,n);case P.NANOSECOND:return Pr(t,e,n)}},$r=({values:t,stride:e},n,r)=>{t[e*n]=r},Yr=({values:t,stride:e},n,r)=>{t[e*n]=r},Wr=({values:t},e,n)=>{t.set(n.subarray(0,2),2*e)},Hr=({values:t},e,n)=>{t.set(n.subarray(0,2),2*e)},qr=(t,e,n)=>{switch(t.type.unit){case P.SECOND:return $r(t,e,n);case P.MILLISECOND:return Yr(t,e,n);case P.MICROSECOND:return Wr(t,e,n);case P.NANOSECOND:return Hr(t,e,n)}},Kr=({values:t},e,n)=>{t.set(n.subarray(0,4),4*e)},Gr=(t,e,n)=>{const r=t.getChildAt(0),i=t.valueOffsets;for(let s=-1,o=i[e],a=i[e+1];o<a;)r.set(o++,n.get(++s))},Jr=(t,e,n)=>{const r=t.getChildAt(0),i=t.valueOffsets,s=n instanceof Map?[...n]:Object.entries(n);for(let o=-1,a=i[e],c=i[e+1];a<c;)r.set(a++,s[++o])},Zr=(t,e)=>(n,r,i)=>n&&n.set(t,e[i]),Xr=(t,e)=>(n,r,i)=>n&&n.set(t,e.get(i)),Qr=(t,e)=>(n,r,i)=>n&&n.set(t,e.get(r.name)),ti=(t,e)=>(n,r,i)=>n&&n.set(t,e[r.name]),ei=(t,e,n)=>{const r=n instanceof Map?Qr(e,n):n instanceof N?Xr(e,n):Array.isArray(n)?Zr(e,n):ti(e,n);t.type.children.forEach((e,n)=>r(t.getChildAt(n),e,n))},ni=(t,e,n)=>{t.type.mode===$.Dense?ri(t,e,n):ii(t,e,n)},ri=(t,e,n)=>{const r=t.typeIdToChildIndex[t.typeIds[e]],i=t.getChildAt(r);i&&i.set(t.valueOffsets[e],n)},ii=(t,e,n)=>{const r=t.typeIdToChildIndex[t.typeIds[e]],i=t.getChildAt(r);i&&i.set(e,n)},si=(t,e,n)=>{const r=t.getKey(e);null!==r&&t.setValue(r,n)},oi=(t,e,n)=>{t.type.unit===Y.DAY_TIME?ai(t,e,n):ci(t,e,n)},ai=({values:t},e,n)=>{t.set(n.subarray(0,2),2*e)},ci=({values:t},e,n)=>{t[e]=12*n[0]+n[1]%12},ui=(t,e,n)=>{const r=t.getChildAt(0),{stride:i}=t;for(let s=-1,o=e*i;++s<i;)r.set(o+s,n.get(s))};vr.prototype.visitBool=Tr,vr.prototype.visitInt=Mr,vr.prototype.visitInt8=jr,vr.prototype.visitInt16=jr,vr.prototype.visitInt32=jr,vr.prototype.visitInt64=Dr,vr.prototype.visitUint8=jr,vr.prototype.visitUint16=jr,vr.prototype.visitUint32=jr,vr.prototype.visitUint64=Dr,vr.prototype.visitFloat=Cr,vr.prototype.visitFloat16=Er,vr.prototype.visitFloat32=jr,vr.prototype.visitFloat64=jr,vr.prototype.visitUtf8=Ur,vr.prototype.visitBinary=Fr,vr.prototype.visitFixedSizeBinary=Lr,vr.prototype.visitDate=Nr,vr.prototype.visitDateDay=Br,vr.prototype.visitDateMillisecond=xr,vr.prototype.visitTimestamp=zr,vr.prototype.visitTimestampSecond=kr,vr.prototype.visitTimestampMillisecond=Rr,vr.prototype.visitTimestampMicrosecond=Vr,vr.prototype.visitTimestampNanosecond=Pr,vr.prototype.visitTime=qr,vr.prototype.visitTimeSecond=$r,vr.prototype.visitTimeMillisecond=Yr,vr.prototype.visitTimeMicrosecond=Wr,vr.prototype.visitTimeNanosecond=Hr,vr.prototype.visitDecimal=Kr,vr.prototype.visitList=Gr,vr.prototype.visitStruct=ei,vr.prototype.visitUnion=ni,vr.prototype.visitDenseUnion=ri,vr.prototype.visitSparseUnion=ii,vr.prototype.visitDictionary=si,vr.prototype.visitInterval=oi,vr.prototype.visitIntervalDayTime=ai,vr.prototype.visitIntervalYearMonth=ci,vr.prototype.visitFixedSizeList=ui,vr.prototype.visitMap=Jr;const li=new vr;class hi extends nt{visitNull(){return Ie}visitBool(){return Oe}visitInt(){return Ke}visitInt8(){return Ge}visitInt16(){return Je}visitInt32(){return Ze}visitInt64(){return Xe}visitUint8(){return Qe}visitUint16(){return tn}visitUint32(){return en}visitUint64(){return nn}visitFloat(){return Ue}visitFloat16(){return Me}visitFloat32(){return Ce}visitFloat64(){return Ne}visitUtf8(){return vn}visitBinary(){return _n}visitFixedSizeBinary(){return je}visitDate(){return Se}visitDateDay(){return Ae}visitDateMillisecond(){return Te}visitTimestamp(){return ln}visitTimestampSecond(){return hn}visitTimestampMillisecond(){return fn}visitTimestampMicrosecond(){return dn}visitTimestampNanosecond(){return pn}visitTime(){return sn}visitTimeSecond(){return on}visitTimeMillisecond(){return an}visitTimeMicrosecond(){return cn}visitTimeNanosecond(){return un}visitDecimal(){return Be}visitList(){return dr}visitStruct(){return br}visitUnion(){return gr}visitDenseUnion(){return _r}visitSparseUnion(){return mr}visitDictionary(){return xe}visitInterval(){return yn}visitIntervalDayTime(){return bn}visitIntervalYearMonth(){return gn}visitFixedSizeList(){return pr}visitMap(){return yr}}const fi=new hi;function di(t){const e=t.type,n=new(fi.getVisitFn(e)())(t);if(e.children&&e.children.length>0){const r=t["children"]||[],i={nullValues:t["nullValues"]},s=Array.isArray(r)?(t,e)=>r[e]||i:({name:t})=>r[t]||i;e.children.forEach((t,e)=>{const{type:r}=t,i=s(t,e);n.children.push(di({...i,type:r}))})}return n}var pi;ge.new=di,Object.keys(k).map(t=>k[t]).filter(t=>"number"===typeof t&&t!==k.NONE).forEach(t=>{const e=fi.visit(t);e.prototype._setValue=li.getVisitFn(t)}),vn.prototype._setValue=li.visitBinary,function(t){(function(e){(function(e){(function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}version(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):U.apache.arrow.flatbuf.MetadataVersion.V1}schema(t){let e=this.bb.__offset(this.bb_pos,6);return e?(t||new U.apache.arrow.flatbuf.Schema).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(e,n){let r=this.bb.__offset(this.bb_pos,8);return r?(n||new t.apache.arrow.flatbuf.Block).__init(this.bb.__vector(this.bb_pos+r)+24*e,this.bb):null}dictionariesLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(e,n){let r=this.bb.__offset(this.bb_pos,10);return r?(n||new t.apache.arrow.flatbuf.Block).__init(this.bb.__vector(this.bb_pos+r)+24*e,this.bb):null}recordBatchesLength(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(4)}static addVersion(t,e){t.addFieldInt16(0,e,U.apache.arrow.flatbuf.MetadataVersion.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static endFooter(t){let e=t.endObject();return e}static finishFooterBuffer(t,e){t.finish(e)}static createFooter(t,e,r,i,s){return n.startFooter(t),n.addVersion(t,e),n.addSchema(t,r),n.addDictionaries(t,i),n.addRecordBatches(t,s),n.endFooter(t)}}e.Footer=n})(e.flatbuf||(e.flatbuf={}))})(e.arrow||(e.arrow={}))})(t.apache||(t.apache={}))}(pi||(pi={})),function(t){(function(t){(function(t){(function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static createBlock(t,e,n,r){return t.prep(8,24),t.writeInt64(r),t.pad(4),t.writeInt32(n),t.writeInt64(e),t.offset()}}t.Block=e})(t.flatbuf||(t.flatbuf={}))})(t.arrow||(t.arrow={}))})(t.apache||(t.apache={}))}(pi||(pi={}));var yi=n("a6b2"),bi=yi["a"].Long,gi=yi["a"].Builder,mi=yi["a"].ByteBuffer,_i=pi.apache.arrow.flatbuf.Block,vi=pi.apache.arrow.flatbuf.Footer;class wi{constructor(t,e=H.V4,n,r){this.schema=t,this.version=e,n&&(this._recordBatches=n),r&&(this._dictionaryBatches=r)}static decode(t){t=new mi(Object(ne["toUint8Array"])(t));const e=vi.getRootAsFooter(t),n=ur.decode(e.schema());return new Oi(n,e)}static encode(t){const e=new gi,n=ur.encode(e,t.schema);vi.startRecordBatchesVector(e,t.numRecordBatches),[...t.recordBatches()].slice().reverse().forEach(t=>Ii.encode(e,t));const r=e.endVector();vi.startDictionariesVector(e,t.numDictionaries),[...t.dictionaryBatches()].slice().reverse().forEach(t=>Ii.encode(e,t));const i=e.endVector();return vi.startFooter(e),vi.addSchema(e,n),vi.addVersion(e,H.V4),vi.addRecordBatches(e,r),vi.addDictionaries(e,i),vi.finishFooterBuffer(e,vi.endFooter(e)),e.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}*recordBatches(){for(let t,e=-1,n=this.numRecordBatches;++e<n;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,n=this.numDictionaries;++e<n;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class Oi extends wi{constructor(t,e){super(t,e.version()),this._footer=e}get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return Ii.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return Ii.decode(e)}return null}}class Ii{static decode(t){return new Ii(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:n}=e,r=new bi(e.offset,0),i=new bi(e.bodyLength,0);return _i.createBlock(t,r,n,i)}constructor(t,e,n){this.metaDataLength=t,this.offset="number"===typeof n?n:n.low,this.bodyLength="number"===typeof e?e:e.low}}var Si=n("f673");class Ai extends Si["b"]{write(t){if((t=Object(ne["toUint8Array"])(t)).byteLength>0)return super.write(t)}toString(t=!1){return t?Object(mn["a"])(this.toUint8Array(!0)):this.toUint8Array(!1).then(mn["a"])}toUint8Array(t=!1){return t?Object(ne["joinUint8Arrays"])(this._values)[0]:(async()=>{let t=[],e=0;for await(const n of this)t.push(n),e+=n.byteLength;return Object(ne["joinUint8Arrays"])(t,e)[0]})()}}class Ti{constructor(t){t&&(this.source=new xi(C["a"].fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class Bi{constructor(t){t instanceof Bi?this.source=t.source:t instanceof Ai?this.source=new ji(C["a"].fromAsyncIterable(t)):Object(ae["n"])(t)?this.source=new ji(C["a"].fromNodeStream(t)):Object(ae["m"])(t)?this.source=new ji(C["a"].fromDOMStream(t)):Object(ae["g"])(t)?this.source=new ji(C["a"].fromDOMStream(t.body)):Object(ae["i"])(t)?this.source=new ji(C["a"].fromIterable(t)):(Object(ae["l"])(t)||Object(ae["f"])(t))&&(this.source=new ji(C["a"].fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class xi{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t,e="read"){return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Si["c"])}return(t){return Object.create(this.source.return&&this.source.return(t)||Si["c"])}}class ji{constructor(t){this.source=t,this._closedPromise=new Promise(t=>this._closedPromiseResolve=t)}async cancel(t){await this.return(t)}get closed(){return this._closedPromise}async read(t){return(await this.next(t,"read")).value}async peek(t){return(await this.next(t,"peek")).value}async next(t,e="read"){return await this.source.next({cmd:e,size:t})}async throw(t){const e=this.source.throw&&await this.source.throw(t)||Si["c"];return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}async return(t){const e=this.source.return&&await this.source.return(t)||Si["c"];return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}}class Ei extends Ti{constructor(t,e){super(),this.position=0,this.buffer=Object(ne["toUint8Array"])(t),this.size="undefined"===typeof e?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:n}=this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:n,position:r}=this;return e&&r<n?("number"!==typeof t&&(t=1/0),this.position=Math.min(n,r+Math.min(n-r,t)),e.subarray(r,this.position)):null}readAt(t,e){const n=this.buffer,r=Math.min(this.size,t+e);return n?n.subarray(t,r):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class Di extends Bi{constructor(t,e){super(),this.position=0,this._handle=t,"number"===typeof e?this.size=e:this._pending=(async()=>{this.size=(await t.stat()).size,delete this._pending})()}async readInt32(t){const{buffer:e,byteOffset:n}=await this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}async seek(t){return this._pending&&await this._pending,this.position=Math.min(t,this.size),t<this.size}async read(t){this._pending&&await this._pending;const{_handle:e,size:n,position:r}=this;if(e&&r<n){"number"!==typeof t&&(t=1/0);let i=r,s=0,o=0,a=Math.min(n,i+Math.min(n-i,t)),c=new Uint8Array(Math.max(0,(this.position=a)-i));while((i+=o)<a&&(s+=o)<c.byteLength)({bytesRead:o}=await e.read(c,s,c.byteLength-s,i));return c}return null}async readAt(t,e){this._pending&&await this._pending;const{_handle:n,size:r}=this;if(n&&t+e<r){const i=Math.min(r,t+e),s=new Uint8Array(i-t);return(await n.read(s,0,e,t)).buffer}return new Uint8Array(e)}async close(){const t=this._handle;this._handle=null,t&&await t.close()}async throw(t){return await this.close(),{done:!0,value:t}}async return(t){return await this.close(),{done:!0,value:t}}}const Li=65536;function Fi(t){return t<0&&(t=4294967295+t+1),"0x"+t.toString(16)}const Ui=8,Mi=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class Ci{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),n=new Uint32Array([t.buffer[1]>>>16,65535&t.buffer[1],t.buffer[0]>>>16,65535&t.buffer[0]]);let r=e[3]*n[3];this.buffer[0]=65535&r;let i=r>>>16;return r=e[2]*n[3],i+=r,r=e[3]*n[2]>>>0,i+=r,this.buffer[0]+=i<<16,this.buffer[1]=i>>>0<r?Li:0,this.buffer[1]+=i>>>16,this.buffer[1]+=e[1]*n[3]+e[2]*n[2]+e[3]*n[1],this.buffer[1]+=e[0]*n[3]+e[1]*n[2]+e[2]*n[1]+e[3]*n[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${Fi(this.buffer[1])} ${Fi(this.buffer[0])}`}}class Ni extends Ci{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t,e=new Uint32Array(2)){return Ni.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return Ni.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const n=t.length;let r=new Ni(e);for(let i=0;i<n;){const e=Ui<n-i?Ui:n-i,s=new Ni(new Uint32Array([parseInt(t.substr(i,e),10),0])),o=new Ni(new Uint32Array([Mi[e],0]));r.times(o),r.plus(s),i+=e}return r}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)Ni.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){let n=new Ni(new Uint32Array(t.buffer));return n.times(e)}static add(t,e){let n=new Ni(new Uint32Array(t.buffer));return n.plus(e)}}class ki extends Ci{negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=this.buffer[1]<<0,n=t.buffer[1]<<0;return e<n||e===n&&this.buffer[0]<t.buffer[0]}static from(t,e=new Uint32Array(2)){return ki.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return ki.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const n=t.startsWith("-"),r=t.length;let i=new ki(e);for(let s=n?1:0;s<r;){const e=Ui<r-s?Ui:r-s,n=new ki(new Uint32Array([parseInt(t.substr(s,e),10),0])),o=new ki(new Uint32Array([Mi[e],0]));i.times(o),i.plus(n),s+=e}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)ki.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){let n=new ki(new Uint32Array(t.buffer));return n.times(e)}static add(t,e){let n=new ki(new Uint32Array(t.buffer));return n.plus(e)}}class Ri{constructor(t){this.buffer=t}high(){return new ki(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new ki(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}times(t){const e=new Ni(new Uint32Array([this.buffer[3],0])),n=new Ni(new Uint32Array([this.buffer[2],0])),r=new Ni(new Uint32Array([this.buffer[1],0])),i=new Ni(new Uint32Array([this.buffer[0],0])),s=new Ni(new Uint32Array([t.buffer[3],0])),o=new Ni(new Uint32Array([t.buffer[2],0])),a=new Ni(new Uint32Array([t.buffer[1],0])),c=new Ni(new Uint32Array([t.buffer[0],0]));let u=Ni.multiply(i,c);this.buffer[0]=u.low();let l=new Ni(new Uint32Array([u.high(),0]));u=Ni.multiply(r,c),l.plus(u),u=Ni.multiply(i,a),l.plus(u),this.buffer[1]=l.low(),this.buffer[3]=l.lessThan(u)?1:0,this.buffer[2]=l.high();let h=new Ni(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2));return h.plus(Ni.multiply(n,c)).plus(Ni.multiply(r,a)).plus(Ni.multiply(i,o)),this.buffer[3]+=Ni.multiply(e,c).plus(Ni.multiply(n,a)).plus(Ni.multiply(r,o)).plus(Ni.multiply(i,s)).low(),this}plus(t){let e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return`${Fi(this.buffer[3])} ${Fi(this.buffer[2])} ${Fi(this.buffer[1])} ${Fi(this.buffer[0])}`}static multiply(t,e){let n=new Ri(new Uint32Array(t.buffer));return n.times(e)}static add(t,e){let n=new Ri(new Uint32Array(t.buffer));return n.plus(e)}static from(t,e=new Uint32Array(4)){return Ri.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(4)){return Ri.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(4)){const n=t.startsWith("-"),r=t.length;let i=new Ri(e);for(let s=n?1:0;s<r;){const e=Ui<r-s?Ui:r-s,n=new Ri(new Uint32Array([parseInt(t.substr(s,e),10),0,0,0])),o=new Ri(new Uint32Array([Mi[e],0,0,0]));i.times(o),i.plus(n),s+=e}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(4*t.length);for(let n=-1,r=t.length;++n<r;)Ri.from(t[n],new Uint32Array(e.buffer,e.byteOffset+16*n,4));return e}}class Vi extends nt{constructor(t,e,n,r){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=n,this.dictionaries=r}visit(t){return super.visit(t instanceof lr?t.type:t)}visitNull(t,{length:e}=this.nextFieldNode()){return ie.Null(t,0,e)}visitBool(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Bool(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitInt(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Int(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitFloat(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Float(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitUtf8(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Utf8(t,0,e,n,this.readNullBitmap(t,n),this.readOffsets(t),this.readData(t))}visitBinary(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Binary(t,0,e,n,this.readNullBitmap(t,n),this.readOffsets(t),this.readData(t))}visitFixedSizeBinary(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.FixedSizeBinary(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitDate(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Date(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitTimestamp(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Timestamp(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitTime(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Time(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitDecimal(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Decimal(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitList(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.List(t,0,e,n,this.readNullBitmap(t,n),this.readOffsets(t),this.visit(t.children[0]))}visitStruct(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Struct(t,0,e,n,this.readNullBitmap(t,n),this.visitMany(t.children))}visitUnion(t){return t.mode===$.Sparse?this.visitSparseUnion(t):this.visitDenseUnion(t)}visitDenseUnion(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Union(t,0,e,n,this.readNullBitmap(t,n),this.readTypeIds(t),this.readOffsets(t),this.visitMany(t.children))}visitSparseUnion(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Union(t,0,e,n,this.readNullBitmap(t,n),this.readTypeIds(t),this.visitMany(t.children))}visitDictionary(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Dictionary(t,0,e,n,this.readNullBitmap(t,n),this.readData(t.indices),this.readDictionary(t))}visitInterval(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Interval(t,0,e,n,this.readNullBitmap(t,n),this.readData(t))}visitFixedSizeList(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.FixedSizeList(t,0,e,n,this.readNullBitmap(t,n),this.visit(t.children[0]))}visitMap(t,{length:e,nullCount:n}=this.nextFieldNode()){return ie.Map(t,0,e,n,this.readNullBitmap(t,n),this.readOffsets(t),this.visit(t.children[0]))}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e,n=this.nextBufferRange()){return e>0&&this.readData(t,n)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t,{length:e,offset:n}=this.nextBufferRange()){return this.bytes.subarray(n,n+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class Pi extends Vi{constructor(t,e,n,r){super(new Uint8Array(0),e,n,r),this.sources=t}readNullBitmap(t,e,{offset:n}=this.nextBufferRange()){return e<=0?new Uint8Array(0):Z(this.sources[n])}readOffsets(t,{offset:e}=this.nextBufferRange()){return Object(ne["toArrayBufferView"])(Uint8Array,Object(ne["toArrayBufferView"])(Int32Array,this.sources[e]))}readTypeIds(t,{offset:e}=this.nextBufferRange()){return Object(ne["toArrayBufferView"])(Uint8Array,Object(ne["toArrayBufferView"])(t.ArrayType,this.sources[e]))}readData(t,{offset:e}=this.nextBufferRange()){const{sources:n}=this;return Ot.isTimestamp(t)||(Ot.isInt(t)||Ot.isTime(t))&&64===t.bitWidth||Ot.isDate(t)&&t.unit===V.MILLISECOND?Object(ne["toArrayBufferView"])(Uint8Array,ki.convertArray(n[e])):Ot.isDecimal(t)?Object(ne["toArrayBufferView"])(Uint8Array,Ri.convertArray(n[e])):Ot.isBinary(t)||Ot.isFixedSizeBinary(t)?zi(n[e]):Ot.isBool(t)?Z(n[e]):Ot.isUtf8(t)?Object(mn["b"])(n[e].join("")):Object(ne["toArrayBufferView"])(Uint8Array,Object(ne["toArrayBufferView"])(t.ArrayType,n[e].map(t=>+t)))}}function zi(t){const e=t.join(""),n=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)n[r>>1]=parseInt(e.substr(r,2),16);return n}var $i=yi["a"].Long,Yi=U.apache.arrow.flatbuf.Null,Wi=U.apache.arrow.flatbuf.Int,Hi=U.apache.arrow.flatbuf.FloatingPoint,qi=U.apache.arrow.flatbuf.Binary,Ki=U.apache.arrow.flatbuf.Bool,Gi=U.apache.arrow.flatbuf.Utf8,Ji=U.apache.arrow.flatbuf.Decimal,Zi=U.apache.arrow.flatbuf.Date,Xi=U.apache.arrow.flatbuf.Time,Qi=U.apache.arrow.flatbuf.Timestamp,ts=U.apache.arrow.flatbuf.Interval,es=U.apache.arrow.flatbuf.List,ns=U.apache.arrow.flatbuf.Struct_,rs=U.apache.arrow.flatbuf.Union,is=U.apache.arrow.flatbuf.DictionaryEncoding,ss=U.apache.arrow.flatbuf.FixedSizeBinary,os=U.apache.arrow.flatbuf.FixedSizeList,as=U.apache.arrow.flatbuf.Map;class cs extends nt{visit(t,e){return null==t||null==e?void 0:super.visit(t,e)}visitNull(t,e){return Yi.startNull(e),Yi.endNull(e)}visitInt(t,e){return Wi.startInt(e),Wi.addBitWidth(e,t.bitWidth),Wi.addIsSigned(e,t.isSigned),Wi.endInt(e)}visitFloat(t,e){return Hi.startFloatingPoint(e),Hi.addPrecision(e,t.precision),Hi.endFloatingPoint(e)}visitBinary(t,e){return qi.startBinary(e),qi.endBinary(e)}visitBool(t,e){return Ki.startBool(e),Ki.endBool(e)}visitUtf8(t,e){return Gi.startUtf8(e),Gi.endUtf8(e)}visitDecimal(t,e){return Ji.startDecimal(e),Ji.addScale(e,t.scale),Ji.addPrecision(e,t.precision),Ji.endDecimal(e)}visitDate(t,e){return Zi.startDate(e),Zi.addUnit(e,t.unit),Zi.endDate(e)}visitTime(t,e){return Xi.startTime(e),Xi.addUnit(e,t.unit),Xi.addBitWidth(e,t.bitWidth),Xi.endTime(e)}visitTimestamp(t,e){const n=t.timezone&&e.createString(t.timezone)||void 0;return Qi.startTimestamp(e),Qi.addUnit(e,t.unit),void 0!==n&&Qi.addTimezone(e,n),Qi.endTimestamp(e)}visitInterval(t,e){return ts.startInterval(e),ts.addUnit(e,t.unit),ts.endInterval(e)}visitList(t,e){return es.startList(e),es.endList(e)}visitStruct(t,e){return ns.startStruct_(e),ns.endStruct_(e)}visitUnion(t,e){rs.startTypeIdsVector(e,t.typeIds.length);const n=rs.createTypeIdsVector(e,t.typeIds);return rs.startUnion(e),rs.addMode(e,t.mode),rs.addTypeIds(e,n),rs.endUnion(e)}visitDictionary(t,e){const n=this.visit(t.indices,e);return is.startDictionaryEncoding(e),is.addId(e,new $i(t.id,0)),is.addIsOrdered(e,t.isOrdered),void 0!==n&&is.addIndexType(e,n),is.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return ss.startFixedSizeBinary(e),ss.addByteWidth(e,t.byteWidth),ss.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return os.startFixedSizeList(e),os.addListSize(e,t.listSize),os.endFixedSizeList(e)}visitMap(t,e){return as.startMap(e),as.addKeysSorted(e,t.keysSorted),as.endMap(e)}}const us=new cs;function ls(t,e=new Map){return new ur(ds(t,e),_s(t["customMetadata"]),e)}function hs(t){return new Cs(t["count"],ys(t["columns"]),bs(t["columns"]))}function fs(t){return new Ns(hs(t["data"]),t["id"],t["isDelta"])}function ds(t,e){return(t["fields"]||[]).filter(Boolean).map(t=>lr.fromJSON(t,e))}function ps(t,e){return(t["children"]||[]).filter(Boolean).map(t=>lr.fromJSON(t,e))}function ys(t){return(t||[]).reduce((t,e)=>[...t,new Rs(e["count"],gs(e["VALIDITY"])),...ys(e["children"])],[])}function bs(t,e=[]){for(let n=-1,r=(t||[]).length;++n<r;){const r=t[n];r["VALIDITY"]&&e.push(new ks(e.length,r["VALIDITY"].length)),r["TYPE"]&&e.push(new ks(e.length,r["TYPE"].length)),r["OFFSET"]&&e.push(new ks(e.length,r["OFFSET"].length)),r["DATA"]&&e.push(new ks(e.length,r["DATA"].length)),e=bs(r["children"],e)}return e}function gs(t){return(t||[]).reduce((t,e)=>t+ +(0===e),0)}function ms(t,e){let n,r,i,s,o,a;return e&&(s=t["dictionary"])?e.has(n=s["id"])?(r=(r=s["indexType"])?vs(r):new Bt,a=new te(e.get(n),r,n,s["isOrdered"]),i=new lr(t["name"],a,t["nullable"],_s(t["customMetadata"]))):(r=(r=s["indexType"])?vs(r):new Bt,e.set(n,o=ws(t,ps(t,e))),a=new te(o,r,n,s["isOrdered"]),i=new lr(t["name"],a,t["nullable"],_s(t["customMetadata"]))):(o=ws(t,ps(t,e)),i=new lr(t["name"],o,t["nullable"],_s(t["customMetadata"]))),i||null}function _s(t){return new Map(Object.entries(t||{}))}function vs(t){return new St(t["isSigned"],t["bitWidth"])}function ws(t,e){const n=t["type"]["name"];switch(n){case"NONE":return new It;case"null":return new It;case"binary":return new Nt;case"utf8":return new kt;case"bool":return new Rt;case"list":return new qt((e||[])[0]);case"struct":return new Kt(e||[]);case"struct_":return new Kt(e||[])}switch(n){case"int":{const e=t["type"];return new St(e["isSigned"],e["bitWidth"])}case"floatingpoint":{const e=t["type"];return new Ft(z[e["precision"]])}case"decimal":{const e=t["type"];return new Vt(e["scale"],e["precision"])}case"date":{const e=t["type"];return new Pt(V[e["unit"]])}case"time":{const e=t["type"];return new Yt(P[e["unit"]],e["bitWidth"])}case"timestamp":{const e=t["type"];return new Wt(P[e["unit"]],e["timezone"])}case"interval":{const e=t["type"];return new Ht(Y[e["unit"]])}case"union":{const n=t["type"];return new Gt($[n["mode"]],n["typeIds"]||[],e||[])}case"fixedsizebinary":{const e=t["type"];return new Jt(e["byteWidth"])}case"fixedsizelist":{const n=t["type"];return new Zt(n["listSize"],(e||[])[0])}case"map":{const n=t["type"];return new Xt((e||[])[0],n["keysSorted"])}}throw new Error(`Unrecognized type: "${n}"`)}var Os=yi["a"].Long,Is=yi["a"].Builder,Ss=yi["a"].ByteBuffer,As=U.apache.arrow.flatbuf.Type,Ts=U.apache.arrow.flatbuf.Field,Bs=U.apache.arrow.flatbuf.Schema,xs=U.apache.arrow.flatbuf.Buffer,js=M.apache.arrow.flatbuf.Message,Es=U.apache.arrow.flatbuf.KeyValue,Ds=M.apache.arrow.flatbuf.FieldNode,Ls=U.apache.arrow.flatbuf.Endianness,Fs=M.apache.arrow.flatbuf.RecordBatch,Us=M.apache.arrow.flatbuf.DictionaryBatch;class Ms{constructor(t,e,n,r){this._version=e,this._headerType=n,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength="number"===typeof t?t:t.low}static fromJSON(t,e){const n=new Ms(0,H.V4,e);return n._createHeader=Vs(t,e),n}static decode(t){t=new Ss(Object(ne["toUint8Array"])(t));const e=js.getRootAsMessage(t),n=e.bodyLength(),r=e.version(),i=e.headerType(),s=new Ms(n,r,i);return s._createHeader=Ps(e,i),s}static encode(t){let e=new Is,n=-1;return t.isSchema()?n=ur.encode(e,t.header()):t.isRecordBatch()?n=Cs.encode(e,t.header()):t.isDictionaryBatch()&&(n=Ns.encode(e,t.header())),js.startMessage(e),js.addVersion(e,H.V4),js.addHeader(e,n),js.addHeaderType(e,t.headerType),js.addBodyLength(e,new Os(t.bodyLength,0)),js.finishMessageBuffer(e,js.endMessage(e)),e.asUint8Array()}static from(t,e=0){if(t instanceof ur)return new Ms(0,H.V4,W.Schema,t);if(t instanceof Cs)return new Ms(e,H.V4,W.RecordBatch,t);if(t instanceof Ns)return new Ms(e,H.V4,W.DictionaryBatch,t);throw new Error("Unrecognized Message header: "+t)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===W.Schema}isRecordBatch(){return this.headerType===W.RecordBatch}isDictionaryBatch(){return this.headerType===W.DictionaryBatch}}class Cs{get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}constructor(t,e,n){this._nodes=e,this._buffers=n,this._length="number"===typeof t?t:t.low}}class Ns{get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}constructor(t,e,n=!1){this._data=t,this._isDelta=n,this._id="number"===typeof e?e:e.low}}class ks{constructor(t,e){this.offset="number"===typeof t?t:t.low,this.length="number"===typeof e?e:e.low}}class Rs{constructor(t,e){this.length="number"===typeof t?t:t.low,this.nullCount="number"===typeof e?e:e.low}}function Vs(t,e){return()=>{switch(e){case W.Schema:return ur.fromJSON(t);case W.RecordBatch:return Cs.fromJSON(t);case W.DictionaryBatch:return Ns.fromJSON(t)}throw new Error(`Unrecognized Message type: { name: ${W[e]}, type: ${e} }`)}}function Ps(t,e){return()=>{switch(e){case W.Schema:return ur.decode(t.header(new Bs));case W.RecordBatch:return Cs.decode(t.header(new Fs),t.version());case W.DictionaryBatch:return Ns.decode(t.header(new Us),t.version())}throw new Error(`Unrecognized Message type: { name: ${W[e]}, type: ${e} }`)}}function zs(t,e=new Map){const n=Gs(t,e);return new ur(n,Xs(t),e)}function $s(t,e=H.V4){return new Cs(t.length(),qs(t),Ks(t,e))}function Ys(t,e=H.V4){return new Ns(Cs.decode(t.data(),e),t.id(),t.isDelta())}function Ws(t){return new ks(t.offset(),t.length())}function Hs(t){return new Rs(t.length(),t.nullCount())}function qs(t){const e=[];for(let n,r=-1,i=-1,s=t.nodesLength();++r<s;)(n=t.nodes(r))&&(e[++i]=Rs.decode(n));return e}function Ks(t,e){const n=[];for(let r,i=-1,s=-1,o=t.buffersLength();++i<o;)(r=t.buffers(i))&&(e<H.V4&&(r.bb_pos+=8*(i+1)),n[++s]=ks.decode(r));return n}function Gs(t,e){const n=[];for(let r,i=-1,s=-1,o=t.fieldsLength();++i<o;)(r=t.fields(i))&&(n[++s]=lr.decode(r,e));return n}function Js(t,e){const n=[];for(let r,i=-1,s=-1,o=t.childrenLength();++i<o;)(r=t.children(i))&&(n[++s]=lr.decode(r,e));return n}function Zs(t,e){let n,r,i,s,o,a;return e&&(a=t.dictionary())?e.has(n=a.id().low)?(s=(s=a.indexType())?Qs(s):new Bt,o=new te(e.get(n),s,n,a.isOrdered()),r=new lr(t.name(),o,t.nullable(),Xs(t))):(s=(s=a.indexType())?Qs(s):new Bt,e.set(n,i=to(t,Js(t,e))),o=new te(i,s,n,a.isOrdered()),r=new lr(t.name(),o,t.nullable(),Xs(t))):(i=to(t,Js(t,e)),r=new lr(t.name(),i,t.nullable(),Xs(t))),r||null}function Xs(t){const e=new Map;if(t)for(let n,r,i=-1,s=0|t.customMetadataLength();++i<s;)(n=t.customMetadata(i))&&null!=(r=n.key())&&e.set(r,n.value());return e}function Qs(t){return new St(t.isSigned(),t.bitWidth())}function to(t,e){const n=t.typeType();switch(n){case As.NONE:return new It;case As.Null:return new It;case As.Binary:return new Nt;case As.Utf8:return new kt;case As.Bool:return new Rt;case As.List:return new qt((e||[])[0]);case As.Struct_:return new Kt(e||[])}switch(n){case As.Int:{const e=t.type(new U.apache.arrow.flatbuf.Int);return new St(e.isSigned(),e.bitWidth())}case As.FloatingPoint:{const e=t.type(new U.apache.arrow.flatbuf.FloatingPoint);return new Ft(e.precision())}case As.Decimal:{const e=t.type(new U.apache.arrow.flatbuf.Decimal);return new Vt(e.scale(),e.precision())}case As.Date:{const e=t.type(new U.apache.arrow.flatbuf.Date);return new Pt(e.unit())}case As.Time:{const e=t.type(new U.apache.arrow.flatbuf.Time);return new Yt(e.unit(),e.bitWidth())}case As.Timestamp:{const e=t.type(new U.apache.arrow.flatbuf.Timestamp);return new Wt(e.unit(),e.timezone())}case As.Interval:{const e=t.type(new U.apache.arrow.flatbuf.Interval);return new Ht(e.unit())}case As.Union:{const n=t.type(new U.apache.arrow.flatbuf.Union);return new Gt(n.mode(),n.typeIdsArray()||[],e||[])}case As.FixedSizeBinary:{const e=t.type(new U.apache.arrow.flatbuf.FixedSizeBinary);return new Jt(e.byteWidth())}case As.FixedSizeList:{const n=t.type(new U.apache.arrow.flatbuf.FixedSizeList);return new Zt(n.listSize(),(e||[])[0])}case As.Map:{const n=t.type(new U.apache.arrow.flatbuf.Map);return new Xt((e||[])[0],n.keysSorted())}}throw new Error(`Unrecognized type: "${As[n]}" (${n})`)}function eo(t,e){const n=e.fields.map(e=>lr.encode(t,e));Bs.startFieldsVector(t,n.length);const r=Bs.createFieldsVector(t,n),i=e.metadata&&e.metadata.size>0?Bs.createCustomMetadataVector(t,[...e.metadata].map(([e,n])=>{const r=t.createString(""+e),i=t.createString(""+n);return Es.startKeyValue(t),Es.addKey(t,r),Es.addValue(t,i),Es.endKeyValue(t)})):-1;return Bs.startSchema(t),Bs.addFields(t,r),Bs.addEndianness(t,ao?Ls.Little:Ls.Big),-1!==i&&Bs.addCustomMetadata(t,i),Bs.endSchema(t)}function no(t,e){let n=-1,r=-1,i=-1,s=e.type,o=e.typeId;Ot.isDictionary(s)?(o=s.dictionary.typeId,i=us.visit(s,t),r=us.visit(s.dictionary,t)):r=us.visit(s,t);const a=(s.children||[]).map(e=>lr.encode(t,e)),c=Ts.createChildrenVector(t,a),u=e.metadata&&e.metadata.size>0?Ts.createCustomMetadataVector(t,[...e.metadata].map(([e,n])=>{const r=t.createString(""+e),i=t.createString(""+n);return Es.startKeyValue(t),Es.addKey(t,r),Es.addValue(t,i),Es.endKeyValue(t)})):-1;return e.name&&(n=t.createString(e.name)),Ts.startField(t),Ts.addType(t,r),Ts.addTypeType(t,o),Ts.addChildren(t,c),Ts.addNullable(t,!!e.nullable),-1!==n&&Ts.addName(t,n),-1!==i&&Ts.addDictionary(t,i),-1!==u&&Ts.addCustomMetadata(t,u),Ts.endField(t)}function ro(t,e){const n=e.nodes||[],r=e.buffers||[];Fs.startNodesVector(t,n.length),n.slice().reverse().forEach(e=>Rs.encode(t,e));const i=t.endVector();Fs.startBuffersVector(t,r.length),r.slice().reverse().forEach(e=>ks.encode(t,e));const s=t.endVector();return Fs.startRecordBatch(t),Fs.addLength(t,new Os(e.length,0)),Fs.addNodes(t,i),Fs.addBuffers(t,s),Fs.endRecordBatch(t)}function io(t,e){const n=Cs.encode(t,e.data);return Us.startDictionaryBatch(t),Us.addId(t,new Os(e.id,0)),Us.addIsDelta(t,e.isDelta),Us.addData(t,n),Us.endDictionaryBatch(t)}function so(t,e){return Ds.createFieldNode(t,new Os(e.length,0),new Os(e.nullCount,0))}function oo(t,e){return xs.createBuffer(t,new Os(e.offset,0),new Os(e.length,0))}lr["encode"]=no,lr["decode"]=Zs,lr["fromJSON"]=ms,ur["encode"]=eo,ur["decode"]=zs,ur["fromJSON"]=ls,Cs["encode"]=ro,Cs["decode"]=$s,Cs["fromJSON"]=hs,Ns["encode"]=io,Ns["decode"]=Ys,Ns["fromJSON"]=fs,Rs["encode"]=so,Rs["decode"]=Hs,ks["encode"]=oo,ks["decode"]=Ws;const ao=function(){const t=new ArrayBuffer(2);return new DataView(t).setInt16(0,256,!0),256===new Int16Array(t)[0]}();var co=yi["a"].ByteBuffer;const uo=t=>`Expected ${W[t]} Message in stream, but was null or length 0.`,lo=t=>`Header pointer of flatbuffer-encoded ${W[t]} Message is null or length 0.`,ho=(t,e)=>`Expected to read ${t} metadata bytes, but only read ${e}.`,fo=(t,e)=>`Expected to read ${t} bytes for message body, but only read ${e}.`;class po{constructor(t){this.source=t instanceof Ti?t:new Ti(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||-1===t.value&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Si["c"]:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(uo(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=Object(ne["toUint8Array"])(this.source.read(t));if(e.byteLength<t)throw new Error(fo(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(t=!1){const e=W.Schema,n=this.readMessage(e),r=n&&n.header();if(t&&!r)throw new Error(lo(e));return r}readMetadataLength(){const t=this.source.read(go),e=t&&new co(t),n=e&&e.readInt32(0)||0;return{done:0===n,value:n}}readMetadata(t){const e=this.source.read(t);if(!e)return Si["c"];if(e.byteLength<t)throw new Error(ho(t,e.byteLength));return{done:!1,value:Ms.decode(e)}}}class yo{constructor(t,e){this.source=t instanceof Bi?t:Object(ae["h"])(t)?new Di(t,e):new Bi(t)}[Symbol.asyncIterator](){return this}async next(){let t;return(t=await this.readMetadataLength()).done||-1===t.value&&(t=await this.readMetadataLength()).done||(t=await this.readMetadata(t.value)).done?Si["c"]:t}async throw(t){return await this.source.throw(t)}async return(t){return await this.source.return(t)}async readMessage(t){let e;if((e=await this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(uo(t));return e.value}async readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=Object(ne["toUint8Array"])(await this.source.read(t));if(e.byteLength<t)throw new Error(fo(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}async readSchema(t=!1){const e=W.Schema,n=await this.readMessage(e),r=n&&n.header();if(t&&!r)throw new Error(lo(e));return r}async readMetadataLength(){const t=await this.source.read(go),e=t&&new co(t),n=e&&e.readInt32(0)||0;return{done:0===n,value:n}}async readMetadata(t){const e=await this.source.read(t);if(!e)return Si["c"];if(e.byteLength<t)throw new Error(ho(t,e.byteLength));return{done:!1,value:Ms.decode(e)}}}class bo extends po{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof Si["a"]?t:new Si["a"](t)}next(){const{_json:t}=this;if(!this._schema){this._schema=!0;const e=Ms.fromJSON(t.schema,W.Schema);return{done:!1,value:e}}if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];this._body=e["data"]["columns"];const n=Ms.fromJSON(e,W.DictionaryBatch);return{done:!1,value:n}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];this._body=e["columns"];const n=Ms.fromJSON(e,W.RecordBatch);return{done:!1,value:n}}return this._body=[],Si["c"]}readMessageBody(t){return e(this._body);function e(t){return(t||[]).reduce((t,n)=>[...t,...n["VALIDITY"]&&[n["VALIDITY"]]||[],...n["TYPE"]&&[n["TYPE"]]||[],...n["OFFSET"]&&[n["OFFSET"]]||[],...n["DATA"]&&[n["DATA"]]||[],...e(n["children"])],[])}}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(uo(t));return e.value}readSchema(){const t=W.Schema,e=this.readMessage(t),n=e&&e.header();if(!e||!n)throw new Error(lo(t));return n}}const go=4,mo="ARROW1",_o=new Uint8Array(mo.length);for(let cl=0;cl<mo.length;cl+=1)_o[cl]=mo.charCodeAt(cl);function vo(t,e=0){for(let n=-1,r=_o.length;++n<r;)if(_o[n]!==t[e+n])return!1;return!0}const wo=_o.length,Oo=wo+go,Io=2*wo+go;class So extends nt{constructor(){super(),this._byteLength=0,this._nodes=[],this._buffers=[],this._bufferRegions=[]}static assemble(...t){const e=new So,n=rr(lu,t),[r=e]=e.visitMany(n);return r}visit(t){if(!Ot.isDictionary(t.type)){const{data:e,length:n,nullCount:r}=t;if(n>**********)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");Ot.isNull(t.type)||Ao.call(this,r<=0?new Uint8Array(0):J(e.offset,n,e.nullBitmap)),this.nodes.push(new Rs(n,r))}return super.visit(t)}visitNull(t){return this}visitDictionary(t){return this.visit(t.indices)}get nodes(){return this._nodes}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get bufferRegions(){return this._bufferRegions}}function Ao(t){const e=t.byteLength+7&-8;return this.buffers.push(t),this.bufferRegions.push(new ks(this._byteLength,e)),this._byteLength+=e,this}function To(t){const{type:e,length:n,typeIds:r,valueOffsets:i}=t;if(Ao.call(this,r),e.mode===$.Sparse)return Do.call(this,t);if(e.mode===$.Dense){if(t.offset<=0)return Ao.call(this,i),Do.call(this,t);{const s=r.reduce((t,e)=>Math.max(t,e),r[0]),o=new Int32Array(s+1),a=new Int32Array(s+1).fill(-1),c=new Int32Array(n),u=Object(ne["rebaseValueOffsets"])(-i[0],n,i);for(let t,e,i=-1;++i<n;)-1===(e=a[t=r[i]])&&(e=a[t]=u[t]),c[i]=u[i]-e,++o[t];Ao.call(this,c);for(let r,i=-1,l=e.children.length;++i<l;)if(r=t.getChildAt(i)){const t=e.typeIds[i],s=Math.min(n,o[t]);this.visit(r.slice(a[t],s))}}}return this}function Bo(t){let e;return t.nullCount>=t.length?Ao.call(this,new Uint8Array(0)):(e=t.values)instanceof Uint8Array?Ao.call(this,J(t.offset,t.length,e)):Ao.call(this,Z(t))}function xo(t){return Ao.call(this,t.values.subarray(0,t.length*t.stride))}function jo(t){const{length:e,values:n,valueOffsets:r}=t,i=r[0],s=r[e],o=Math.min(s-i,n.byteLength-i);return Ao.call(this,Object(ne["rebaseValueOffsets"])(-r[0],e,r)),Ao.call(this,n.subarray(i,i+o)),this}function Eo(t){const{length:e,valueOffsets:n}=t;return n&&Ao.call(this,Object(ne["rebaseValueOffsets"])(n[0],e,n)),this.visit(t.getChildAt(0))}function Do(t){return this.visitMany(t.type.children.map((e,n)=>t.getChildAt(n)).filter(Boolean))[0]}So.prototype.visitBool=Bo,So.prototype.visitInt=xo,So.prototype.visitFloat=xo,So.prototype.visitUtf8=jo,So.prototype.visitBinary=jo,So.prototype.visitFixedSizeBinary=xo,So.prototype.visitDate=xo,So.prototype.visitTimestamp=xo,So.prototype.visitTime=xo,So.prototype.visitDecimal=xo,So.prototype.visitList=Eo,So.prototype.visitStruct=Do,So.prototype.visitUnion=To,So.prototype.visitInterval=xo,So.prototype.visitFixedSizeList=Eo,So.prototype.visitMap=Eo;class Lo extends Si["d"]{constructor(t){super(),this._position=0,this._started=!1,this._sink=new Ai,this._schema=null,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,Object(ae["k"])(t)||(t={autoDestroy:!0,writeLegacyIpcFormat:!1}),this._autoDestroy="boolean"!==typeof t.autoDestroy||t.autoDestroy,this._writeLegacyIpcFormat="boolean"===typeof t.writeLegacyIpcFormat&&t.writeLegacyIpcFormat}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}toString(t=!1){return this._sink.toString(t)}toUint8Array(t=!1){return this._sink.toUint8Array(t)}writeAll(t){return Object(ae["l"])(t)?t.then(t=>this.writeAll(t)):Object(ae["f"])(t)?Co(this,t):Mo(this,t)}get closed(){return this._sink.closed}[Symbol.asyncIterator](){return this._sink[Symbol.asyncIterator]()}toDOMStream(t){return this._sink.toDOMStream(t)}toNodeStream(t){return this._sink.toNodeStream(t)}close(){return this.reset()._sink.close()}abort(t){return this.reset()._sink.abort(t)}finish(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}reset(t=this._sink,e=null){return t===this._sink||t instanceof Ai?this._sink=t:(this._sink=new Ai,t&&Object(ae["o"])(t)?this.toDOMStream({type:"bytes"}).pipeTo(t):t&&Object(ae["p"])(t)&&this.toNodeStream({objectMode:!1}).pipe(t)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,e&&e.compareTo(this._schema)||(null===e?(this._position=0,this._schema=null):(this._started=!0,this._schema=e,this._writeSchema(e))),this}write(t){let e=null;if(!this._sink)throw new Error("RecordBatchWriter is closed");if(null===t||void 0===t)return this.finish()&&void 0;if(t instanceof au&&!(e=t.schema))return this.finish()&&void 0;if(t instanceof lu&&!(e=t.schema))return this.finish()&&void 0;if(e&&!e.compareTo(this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,e)}t instanceof lu?t instanceof hu||this._writeRecordBatch(t):t instanceof au?this.writeAll(t.chunks):Object(ae["i"])(t)&&this.writeAll(t)}_writeMessage(t,e=8){const n=e-1,r=Ms.encode(t),i=r.byteLength,s=this._writeLegacyIpcFormat?4:8,o=i+s+n&~n,a=o-i-s;return t.headerType===W.RecordBatch?this._recordBatchBlocks.push(new Ii(o,t.bodyLength,this._position)):t.headerType===W.DictionaryBatch&&this._dictionaryBlocks.push(new Ii(o,t.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(o-s)),i>0&&this._write(r),this._writePadding(a)}_write(t){if(this._started){const e=Object(ne["toUint8Array"])(t);e&&e.byteLength>0&&(this._sink.write(e),this._position+=e.byteLength)}return this}_writeSchema(t){return this._writeMessage(Ms.from(t))}_writeFooter(t){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}_writeMagic(){return this._write(_o)}_writePadding(t){return t>0?this._write(new Uint8Array(t)):this}_writeRecordBatch(t){const{byteLength:e,nodes:n,bufferRegions:r,buffers:i}=So.assemble(t),s=new Cs(t.length,n,r),o=Ms.from(s,e);return this._writeDictionaries(t)._writeMessage(o)._writeBodyBuffers(i)}_writeDictionaryBatch(t,e,n=!1){this._dictionaryDeltaOffsets.set(e,t.length+(this._dictionaryDeltaOffsets.get(e)||0));const{byteLength:r,nodes:i,bufferRegions:s,buffers:o}=So.assemble(t),a=new Cs(t.length,i,s),c=new Ns(a,e,n),u=Ms.from(c,r);return this._writeMessage(u)._writeBodyBuffers(o)}_writeBodyBuffers(t){let e,n,r;for(let i=-1,s=t.length;++i<s;)(e=t[i])&&(n=e.byteLength)>0&&(this._write(e),(r=(n+7&-8)-n)>0&&this._writePadding(r));return this}_writeDictionaries(t){for(let[e,n]of t.dictionaries){let t=this._dictionaryDeltaOffsets.get(e)||0;if(0===t||(n=n.slice(t)).length>0){const r="chunks"in n?n.chunks:[n];for(const n of r)this._writeDictionaryBatch(n,e,t>0),t+=n.length}}return this}}class Fo extends Lo{static writeAll(t,e){const n=new Fo(e);return Object(ae["l"])(t)?t.then(t=>n.writeAll(t)):Object(ae["f"])(t)?Co(n,t):Mo(n,t)}}class Uo extends Lo{constructor(){super(),this._autoDestroy=!0}static writeAll(t){const e=new Uo;return Object(ae["l"])(t)?t.then(t=>e.writeAll(t)):Object(ae["f"])(t)?Co(e,t):Mo(e,t)}_writeSchema(t){return this._writeMagic()._writePadding(2)}_writeFooter(t){const e=wi.encode(new wi(t,H.V4,this._recordBatchBlocks,this._dictionaryBlocks));return super._writeFooter(t)._write(e)._write(Int32Array.of(e.byteLength))._writeMagic()}}function Mo(t,e){let n=e;e instanceof au&&(n=e.chunks,t.reset(void 0,e.schema));for(const r of n)t.write(r);return t.finish()}async function Co(t,e){for await(const n of e)t.write(n);return t.finish()}const No=new Uint8Array(0),ko=t=>[No,No,new Uint8Array(t),No];function Ro(t,e,n=e.reduce((t,e)=>Math.max(t,e.length),0)){let r,i,s=-1,o=e.length;const a=[...t.fields],c=[],u=(n+63&-64)>>3;while(++s<o)(r=e[s])&&r.length===n?c[s]=r:((i=a[s]).nullable||(a[s]=a[s].clone({nullable:!0})),c[s]=r?r._changeLengthAndBackfillNullBitmap(n):ie.new(i.type,0,n,n,ko(u)));return[new ur(a),n,c]}function Vo(t){return Po(new ur(t.map(({field:t})=>t)),t)}function Po(t,e){return zo(t,e.map(t=>t instanceof Hn?t.chunks.map(t=>t.data):[t.data]))}function zo(t,e){const n=[...t.fields],r=[],i={numBatches:e.reduce((t,e)=>Math.max(t,e.length),0)};let s,o=0,a=0,c=-1,u=e.length,l=[];while(i.numBatches-- >0){for(a=Number.POSITIVE_INFINITY,c=-1;++c<u;)l[c]=s=e[c].shift(),a=Math.min(a,s?s.length:a);isFinite(a)&&(l=$o(n,a,l,e,i),a>0&&(r[o++]=[a,l.slice()]))}return[t=new ur(n,t.metadata),r.map(e=>new lu(t,...e))]}function $o(t,e,n,r,i){let s,o,a=0,c=-1,u=r.length;const l=(e+63&-64)>>3;while(++c<u)(s=n[c])&&(a=s.length)>=e?a===e?n[c]=s:(n[c]=s.slice(0,e),s=s.slice(e,a-e),i.numBatches=Math.max(i.numBatches,r[c].unshift(s))):((o=t[c]).nullable||(t[c]=o.clone({nullable:!0})),n[c]=s?s._changeLengthAndBackfillNullBitmap(e):ie.new(o.type,0,e,e,ko(l)));return n}class Yo extends N{constructor(t,e){super(),this._children=e,this.numChildren=t.childData.length,this._bindDataAccessors(this.data=t)}get type(){return this.data.type}get typeId(){return this.data.typeId}get length(){return this.data.length}get offset(){return this.data.offset}get stride(){return this.data.stride}get nullCount(){return this.data.nullCount}get byteLength(){return this.data.byteLength}get VectorName(){return k[this.typeId]+"Vector"}get ArrayType(){return this.type.ArrayType}get values(){return this.data.values}get typeIds(){return this.data.typeIds}get nullBitmap(){return this.data.nullBitmap}get valueOffsets(){return this.data.valueOffsets}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}clone(t,e=this._children){return N.new(t,e)}concat(...t){return Hn.concat(this,...t)}slice(t,e){return Un(this,t,e,this._sliceInternal)}isValid(t){if(this.nullCount>0){const e=this.offset+t,n=this.nullBitmap[e>>3],r=n&1<<e%8;return 0!==r}return!0}getChildAt(t){return t<0||t>=this.numChildren?null:(this._children||(this._children=[]))[t]||(this._children[t]=N.new(this.data.childData[t]))}toJSON(){return[...this]}_sliceInternal(t,e,n){return t.clone(t.data.slice(e,n-e),null)}_bindDataAccessors(t){}}Yo.prototype[Symbol.isConcatSpreadable]=!0;class Wo extends Yo{asUtf8(){return N.new(this.data.clone(new kt))}}class Ho extends Yo{static from(t){return eu(()=>new Rt,t)}}class qo extends Yo{static from(...t){return 2===t.length?eu(()=>t[1]===V.DAY?new zt:new $t,t[0]):eu(()=>new $t,t[0])}}class Ko extends qo{}class Go extends qo{}class Jo extends Yo{}class Zo extends Yo{constructor(t){super(t),this.indices=N.new(t.clone(this.type.indices))}static from(...t){if(3===t.length){const[e,n,r]=t,i=new te(e.type,n,null,null);return N.new(ie.Dictionary(i,0,r.length,0,null,r,e))}return eu(()=>t[0].type,t[0])}get dictionary(){return this.data.dictionary}reverseLookup(t){return this.dictionary.indexOf(t)}getKey(t){return this.indices.get(t)}getValue(t){return this.dictionary.get(t)}setKey(t,e){return this.indices.set(t,e)}setValue(t,e){return this.dictionary.set(t,e)}}Zo.prototype.indices=null;class Xo extends Yo{}class Qo extends Yo{}class ta extends Yo{static from(t){let e=oa(this);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)){let n=sa(t.constructor)||e;if(null===e&&(e=n),e&&e===n){let n=new e,r=t.byteLength/n.ArrayType.BYTES_PER_ELEMENT;if(!ia(e,t.constructor))return N.new(ie.Float(n,0,r,0,null,t))}}if(e)return eu(()=>new e,t);if(t instanceof DataView||t instanceof ArrayBuffer)throw new TypeError("Cannot infer float type from instance of "+t.constructor.name);throw new TypeError("Unrecognized FloatVector input")}}class ea extends ta{toFloat32Array(){return new Float32Array(this)}toFloat64Array(){return new Float64Array(this)}}class na extends ta{}class ra extends ta{}const ia=(t,e)=>t===Ut&&e!==Uint16Array,sa=t=>{switch(t){case Uint16Array:return Ut;case Float32Array:return Mt;case Float64Array:return Ct;default:return null}},oa=t=>{switch(t){case ea:return Ut;case na:return Mt;case ra:return Ct;default:return null}};class aa extends Yo{}class ca extends aa{}class ua extends aa{}class la extends Yo{static from(...t){let[e,n=!1]=t,r=wa(this,n);if(e instanceof ArrayBuffer||ArrayBuffer.isView(e)){let t=va(e.constructor,n)||r;if(null===r&&(r=t),r&&r===t){let t=new r,n=e.byteLength/t.ArrayType.BYTES_PER_ELEMENT;return _a(r,e.constructor)&&(n*=.5),N.new(ie.Int(t,0,n,0,null,e))}}if(r)return eu(()=>new r,e);if(e instanceof DataView||e instanceof ArrayBuffer)throw new TypeError("Cannot infer integer type from instance of "+e.constructor.name);throw new TypeError("Unrecognized IntVector input")}}class ha extends la{}class fa extends la{}class da extends la{}class pa extends la{toBigInt64Array(){return Object(ne["toBigInt64Array"])(this.values)}get values64(){return this._values64||(this._values64=this.toBigInt64Array())}}class ya extends la{}class ba extends la{}class ga extends la{}class ma extends la{toBigUint64Array(){return Object(ne["toBigUint64Array"])(this.values)}get values64(){return this._values64||(this._values64=this.toBigUint64Array())}}const _a=(t,e)=>(t===xt||t===Lt)&&(e===Int32Array||e===Uint32Array),va=(t,e)=>{switch(t){case Int8Array:return At;case Int16Array:return Tt;case Int32Array:return e?xt:Bt;case ae["b"]:return xt;case Uint8Array:return jt;case Uint16Array:return Et;case Uint32Array:return e?Lt:Dt;case ae["d"]:return Lt;default:return null}},wa=(t,e)=>{switch(t){case ha:return At;case fa:return Tt;case da:return e?xt:Bt;case pa:return xt;case ya:return jt;case ba:return Et;case ga:return e?Lt:Dt;case ma:return Lt;default:return null}};class Oa extends Yo{}class Ia extends Yo{asList(){const t=this.type.children[0];return N.new(this.data.clone(new qt(t)))}bind(t){const e=this.getChildAt(0),{[t]:n,[t+1]:r}=this.valueOffsets;return new xn(e.slice(n,r))}}class Sa extends Yo{}const Aa=Symbol.for("rowIndex");class Ta extends Yo{bind(t){const e=this._row||(this._row=new jn(this)),n=Object.create(e);return n[Aa]=t,n}}class Ba extends Yo{}class xa extends Ba{}class ja extends Ba{}class Ea extends Ba{}class Da extends Ba{}class La extends Yo{}class Fa extends La{}class Ua extends La{}class Ma extends La{}class Ca extends La{}class Na extends Yo{get typeIdToChildIndex(){return this.data.type.typeIdToChildIndex}}class ka extends Na{get valueOffsets(){return this.data.valueOffsets}}class Ra extends Na{}class Va extends Yo{static from(t){return eu(()=>new kt,t)}asBinary(){return N.new(this.data.clone(new Nt))}}function Pa(t){return function(){return t(this)}}function za(t){return function(e){return t(this,e)}}function $a(t){return function(e,n){return t(this,e,n)}}class Ya extends nt{}const Wa=(t,e)=>864e5*t[e],Ha=(t,e)=>4294967296*t[e+1]+(t[e]>>>0),qa=(t,e)=>t[e+1]/1e3*4294967296+(t[e]>>>0)/1e3,Ka=(t,e)=>t[e+1]/1e6*4294967296+(t[e]>>>0)/1e6,Ga=t=>new Date(t),Ja=(t,e)=>Ga(Wa(t,e)),Za=(t,e)=>Ga(Ha(t,e)),Xa=(t,e)=>null,Qa=(t,e,n)=>{const{[n]:r,[n+1]:i}=e;return null!=r&&null!=i?t.subarray(r,i):null},tc=({offset:t,values:e},n)=>{const r=t+n,i=e[r>>3];return 0!==(i&1<<r%8)},ec=({values:t},e)=>Ja(t,e),nc=({values:t},e)=>Za(t,2*e),rc=({stride:t,values:e},n)=>e[t*n],ic=({stride:t,values:e},n)=>Le(e[t*n]),sc=({stride:t,values:e,type:n},r)=>qe.new(e.subarray(t*r,t*(r+1)),n.isSigned),oc=({stride:t,values:e},n)=>e.subarray(t*n,t*(n+1)),ac=({values:t,valueOffsets:e},n)=>Qa(t,e,n),cc=({values:t,valueOffsets:e},n)=>{const r=Qa(t,e,n);return null!==r?Object(mn["a"])(r):null},uc=(t,e)=>t.type.bitWidth<64?rc(t,e):sc(t,e),lc=(t,e)=>t.type.precision!==z.HALF?rc(t,e):ic(t,e),hc=(t,e)=>t.type.unit===V.DAY?ec(t,e):nc(t,e),fc=({values:t},e)=>1e3*Ha(t,2*e),dc=({values:t},e)=>Ha(t,2*e),pc=({values:t},e)=>qa(t,2*e),yc=({values:t},e)=>Ka(t,2*e),bc=(t,e)=>{switch(t.type.unit){case P.SECOND:return fc(t,e);case P.MILLISECOND:return dc(t,e);case P.MICROSECOND:return pc(t,e);case P.NANOSECOND:return yc(t,e)}},gc=({values:t,stride:e},n)=>t[e*n],mc=({values:t,stride:e},n)=>t[e*n],_c=({values:t},e)=>qe.signed(t.subarray(2*e,2*(e+1))),vc=({values:t},e)=>qe.signed(t.subarray(2*e,2*(e+1))),wc=(t,e)=>{switch(t.type.unit){case P.SECOND:return gc(t,e);case P.MILLISECOND:return mc(t,e);case P.MICROSECOND:return _c(t,e);case P.NANOSECOND:return vc(t,e)}},Oc=({values:t},e)=>qe.decimal(t.subarray(4*e,4*(e+1))),Ic=(t,e)=>{const n=t.getChildAt(0),{valueOffsets:r,stride:i}=t;return n.slice(r[e*i],r[e*i+1])},Sc=(t,e)=>t.bind(e),Ac=(t,e)=>t.bind(e),Tc=(t,e)=>t.type.mode===$.Dense?Bc(t,e):xc(t,e),Bc=(t,e)=>{const n=t.typeIdToChildIndex[t.typeIds[e]],r=t.getChildAt(n);return r?r.get(t.valueOffsets[e]):null},xc=(t,e)=>{const n=t.typeIdToChildIndex[t.typeIds[e]],r=t.getChildAt(n);return r?r.get(e):null},jc=(t,e)=>t.getValue(t.getKey(e)),Ec=(t,e)=>t.type.unit===Y.DAY_TIME?Dc(t,e):Lc(t,e),Dc=({values:t},e)=>t.subarray(2*e,2*(e+1)),Lc=({values:t},e)=>{const n=t[e],r=new Int32Array(2);return r[0]=n/12|0,r[1]=n%12|0,r},Fc=(t,e)=>{const n=t.getChildAt(0),{stride:r}=t;return n.slice(e*r,(e+1)*r)};Ya.prototype.visitNull=Xa,Ya.prototype.visitBool=tc,Ya.prototype.visitInt=uc,Ya.prototype.visitInt8=rc,Ya.prototype.visitInt16=rc,Ya.prototype.visitInt32=rc,Ya.prototype.visitInt64=sc,Ya.prototype.visitUint8=rc,Ya.prototype.visitUint16=rc,Ya.prototype.visitUint32=rc,Ya.prototype.visitUint64=sc,Ya.prototype.visitFloat=lc,Ya.prototype.visitFloat16=ic,Ya.prototype.visitFloat32=rc,Ya.prototype.visitFloat64=rc,Ya.prototype.visitUtf8=cc,Ya.prototype.visitBinary=ac,Ya.prototype.visitFixedSizeBinary=oc,Ya.prototype.visitDate=hc,Ya.prototype.visitDateDay=ec,Ya.prototype.visitDateMillisecond=nc,Ya.prototype.visitTimestamp=bc,Ya.prototype.visitTimestampSecond=fc,Ya.prototype.visitTimestampMillisecond=dc,Ya.prototype.visitTimestampMicrosecond=pc,Ya.prototype.visitTimestampNanosecond=yc,Ya.prototype.visitTime=wc,Ya.prototype.visitTimeSecond=gc,Ya.prototype.visitTimeMillisecond=mc,Ya.prototype.visitTimeMicrosecond=_c,Ya.prototype.visitTimeNanosecond=vc,Ya.prototype.visitDecimal=Oc,Ya.prototype.visitList=Ic,Ya.prototype.visitStruct=Ac,Ya.prototype.visitUnion=Tc,Ya.prototype.visitDenseUnion=Bc,Ya.prototype.visitSparseUnion=xc,Ya.prototype.visitDictionary=jc,Ya.prototype.visitInterval=Ec,Ya.prototype.visitIntervalDayTime=Dc,Ya.prototype.visitIntervalYearMonth=Lc,Ya.prototype.visitFixedSizeList=Fc,Ya.prototype.visitMap=Sc;const Uc=new Ya;class Mc extends nt{}function Cc(t,e){return null===e&&t.length>0?0:-1}function Nc(t,e){const{nullBitmap:n}=t;if(!n||t.nullCount<=0)return-1;let r=0;for(const i of X(n,t.data.offset+(e||0),t.length,n,q)){if(!i)return r;++r}return-1}function kc(t,e,n){if(void 0===e)return-1;if(null===e)return Nc(t,n);const r=Nn(e);for(let i=(n||0)-1,s=t.length;++i<s;)if(r(t.get(i)))return i;return-1}function Rc(t,e,n){const r=Nn(e);for(let i=(n||0)-1,s=t.length;++i<s;)if(r(t.get(i)))return i;return-1}Mc.prototype.visitNull=Cc,Mc.prototype.visitBool=kc,Mc.prototype.visitInt=kc,Mc.prototype.visitInt8=kc,Mc.prototype.visitInt16=kc,Mc.prototype.visitInt32=kc,Mc.prototype.visitInt64=kc,Mc.prototype.visitUint8=kc,Mc.prototype.visitUint16=kc,Mc.prototype.visitUint32=kc,Mc.prototype.visitUint64=kc,Mc.prototype.visitFloat=kc,Mc.prototype.visitFloat16=kc,Mc.prototype.visitFloat32=kc,Mc.prototype.visitFloat64=kc,Mc.prototype.visitUtf8=kc,Mc.prototype.visitBinary=kc,Mc.prototype.visitFixedSizeBinary=kc,Mc.prototype.visitDate=kc,Mc.prototype.visitDateDay=kc,Mc.prototype.visitDateMillisecond=kc,Mc.prototype.visitTimestamp=kc,Mc.prototype.visitTimestampSecond=kc,Mc.prototype.visitTimestampMillisecond=kc,Mc.prototype.visitTimestampMicrosecond=kc,Mc.prototype.visitTimestampNanosecond=kc,Mc.prototype.visitTime=kc,Mc.prototype.visitTimeSecond=kc,Mc.prototype.visitTimeMillisecond=kc,Mc.prototype.visitTimeMicrosecond=kc,Mc.prototype.visitTimeNanosecond=kc,Mc.prototype.visitDecimal=kc,Mc.prototype.visitList=kc,Mc.prototype.visitStruct=kc,Mc.prototype.visitUnion=kc,Mc.prototype.visitDenseUnion=Rc,Mc.prototype.visitSparseUnion=Rc,Mc.prototype.visitDictionary=kc,Mc.prototype.visitInterval=kc,Mc.prototype.visitIntervalDayTime=kc,Mc.prototype.visitIntervalYearMonth=kc,Mc.prototype.visitFixedSizeList=kc,Mc.prototype.visitMap=kc;const Vc=new Mc;class Pc extends nt{}function zc(t){const e=Uc.getVisitFn(t);return X(t.nullBitmap,t.offset,t.length,t,(t,n,r,i)=>0!==(r&1<<i)?e(t,n):null)}function $c(t){if(t.nullCount>0)return zc(t);const{type:e,typeId:n,length:r}=t;return 1===t.stride&&(n===k.Timestamp||n===k.Int&&64!==e.bitWidth||n===k.Time&&64!==e.bitWidth||n===k.Float&&e.precision>0)?t.values.subarray(0,r)[Symbol.iterator]():function*(e){for(let n=-1;++n<r;)yield e(t,n)}(Uc.getVisitFn(t))}Pc.prototype.visitNull=$c,Pc.prototype.visitBool=$c,Pc.prototype.visitInt=$c,Pc.prototype.visitInt8=$c,Pc.prototype.visitInt16=$c,Pc.prototype.visitInt32=$c,Pc.prototype.visitInt64=$c,Pc.prototype.visitUint8=$c,Pc.prototype.visitUint16=$c,Pc.prototype.visitUint32=$c,Pc.prototype.visitUint64=$c,Pc.prototype.visitFloat=$c,Pc.prototype.visitFloat16=$c,Pc.prototype.visitFloat32=$c,Pc.prototype.visitFloat64=$c,Pc.prototype.visitUtf8=$c,Pc.prototype.visitBinary=$c,Pc.prototype.visitFixedSizeBinary=$c,Pc.prototype.visitDate=$c,Pc.prototype.visitDateDay=$c,Pc.prototype.visitDateMillisecond=$c,Pc.prototype.visitTimestamp=$c,Pc.prototype.visitTimestampSecond=$c,Pc.prototype.visitTimestampMillisecond=$c,Pc.prototype.visitTimestampMicrosecond=$c,Pc.prototype.visitTimestampNanosecond=$c,Pc.prototype.visitTime=$c,Pc.prototype.visitTimeSecond=$c,Pc.prototype.visitTimeMillisecond=$c,Pc.prototype.visitTimeMicrosecond=$c,Pc.prototype.visitTimeNanosecond=$c,Pc.prototype.visitDecimal=$c,Pc.prototype.visitList=$c,Pc.prototype.visitStruct=$c,Pc.prototype.visitUnion=$c,Pc.prototype.visitDenseUnion=$c,Pc.prototype.visitSparseUnion=$c,Pc.prototype.visitDictionary=$c,Pc.prototype.visitInterval=$c,Pc.prototype.visitIntervalDayTime=$c,Pc.prototype.visitIntervalYearMonth=$c,Pc.prototype.visitFixedSizeList=$c,Pc.prototype.visitMap=$c;const Yc=new Pc;class Wc extends nt{}function Hc(t){const{type:e,length:n,stride:r}=t;switch(e.typeId){case k.Int:case k.Float:case k.Decimal:case k.Time:case k.Timestamp:return t.values.subarray(0,n*r)}return[...Yc.visit(t)]}Wc.prototype.visitNull=Hc,Wc.prototype.visitBool=Hc,Wc.prototype.visitInt=Hc,Wc.prototype.visitInt8=Hc,Wc.prototype.visitInt16=Hc,Wc.prototype.visitInt32=Hc,Wc.prototype.visitInt64=Hc,Wc.prototype.visitUint8=Hc,Wc.prototype.visitUint16=Hc,Wc.prototype.visitUint32=Hc,Wc.prototype.visitUint64=Hc,Wc.prototype.visitFloat=Hc,Wc.prototype.visitFloat16=Hc,Wc.prototype.visitFloat32=Hc,Wc.prototype.visitFloat64=Hc,Wc.prototype.visitUtf8=Hc,Wc.prototype.visitBinary=Hc,Wc.prototype.visitFixedSizeBinary=Hc,Wc.prototype.visitDate=Hc,Wc.prototype.visitDateDay=Hc,Wc.prototype.visitDateMillisecond=Hc,Wc.prototype.visitTimestamp=Hc,Wc.prototype.visitTimestampSecond=Hc,Wc.prototype.visitTimestampMillisecond=Hc,Wc.prototype.visitTimestampMicrosecond=Hc,Wc.prototype.visitTimestampNanosecond=Hc,Wc.prototype.visitTime=Hc,Wc.prototype.visitTimeSecond=Hc,Wc.prototype.visitTimeMillisecond=Hc,Wc.prototype.visitTimeMicrosecond=Hc,Wc.prototype.visitTimeNanosecond=Hc,Wc.prototype.visitDecimal=Hc,Wc.prototype.visitList=Hc,Wc.prototype.visitStruct=Hc,Wc.prototype.visitUnion=Hc,Wc.prototype.visitDenseUnion=Hc,Wc.prototype.visitSparseUnion=Hc,Wc.prototype.visitDictionary=Hc,Wc.prototype.visitInterval=Hc,Wc.prototype.visitIntervalDayTime=Hc,Wc.prototype.visitIntervalYearMonth=Hc,Wc.prototype.visitFixedSizeList=Hc,Wc.prototype.visitMap=Hc;const qc=new Wc,Kc=(t,e)=>t+e,Gc=t=>"Cannot compute the byte width of variable-width column "+t;class Jc extends nt{visitNull(t){return 0}visitInt(t){return t.bitWidth/8}visitFloat(t){return t.ArrayType.BYTES_PER_ELEMENT}visitBinary(t){throw new Error(Gc(t))}visitUtf8(t){throw new Error(Gc(t))}visitBool(t){return 1/8}visitDecimal(t){return 16}visitDate(t){return 4*(t.unit+1)}visitTime(t){return t.bitWidth/8}visitTimestamp(t){return t.unit===P.SECOND?4:8}visitInterval(t){return 4*(t.unit+1)}visitList(t){throw new Error(Gc(t))}visitStruct(t){return this.visitFields(t.children).reduce(Kc,0)}visitUnion(t){return this.visitFields(t.children).reduce(Kc,0)}visitFixedSizeBinary(t){return t.byteWidth}visitFixedSizeList(t){return t.listSize*this.visitFields(t.children).reduce(Kc,0)}visitMap(t){return this.visitFields(t.children).reduce(Kc,0)}visitDictionary(t){return this.visit(t.indices)}visitFields(t){return(t||[]).map(t=>this.visit(t.type))}visitSchema(t){return this.visitFields(t.fields).reduce(Kc,0)}}const Zc=new Jc;class Xc extends nt{visitNull(){return Sa}visitBool(){return Ho}visitInt(){return la}visitInt8(){return ha}visitInt16(){return fa}visitInt32(){return da}visitInt64(){return pa}visitUint8(){return ya}visitUint16(){return ba}visitUint32(){return ga}visitUint64(){return ma}visitFloat(){return ta}visitFloat16(){return ea}visitFloat32(){return na}visitFloat64(){return ra}visitUtf8(){return Va}visitBinary(){return Wo}visitFixedSizeBinary(){return Xo}visitDate(){return qo}visitDateDay(){return Ko}visitDateMillisecond(){return Go}visitTimestamp(){return Ba}visitTimestampSecond(){return xa}visitTimestampMillisecond(){return ja}visitTimestampMicrosecond(){return Ea}visitTimestampNanosecond(){return Da}visitTime(){return La}visitTimeSecond(){return Fa}visitTimeMillisecond(){return Ua}visitTimeMicrosecond(){return Ma}visitTimeNanosecond(){return Ca}visitDecimal(){return Jo}visitList(){return Oa}visitStruct(){return Ta}visitUnion(){return Na}visitDenseUnion(){return ka}visitSparseUnion(){return Ra}visitDictionary(){return Zo}visitInterval(){return aa}visitIntervalDayTime(){return ca}visitIntervalYearMonth(){return ua}visitFixedSizeList(){return Qo}visitMap(){return Ia}}const Qc=new Xc;function tu(t,...e){return new(Qc.getVisitFn(t)())(t,...e)}function eu(t,e){if(Object(ae["i"])(e))return N.from({nullValues:[null,void 0],type:t(),values:e});if(Object(ae["f"])(e))return N.from({nullValues:[null,void 0],type:t(),values:e});const{values:n=[],type:r=t(),nullValues:i=[null,void 0]}={...e};return Object(ae["i"])(n),N.from({nullValues:i,...e,type:r})}function nu(t){const{values:e=[],...n}={nullValues:[null,void 0],...t};if(Object(ae["i"])(e)){const t=[...ge.throughIterable(n)(e)];return 1===t.length?t[0]:Hn.concat(t)}return(async t=>{const r=ge.throughAsyncIterable(n);for await(const n of r(e))t.push(n);return 1===t.length?t[0]:Hn.concat(t)})([])}function ru(t){return function(){return t(this.type)}}function iu(t){return function(e){return this.isValid(e)?t.call(this,e):null}}function su(t){return function(e,n){G(this.nullBitmap,this.offset+e,!(null===n||void 0===n))&&t.call(this,e,n)}}function ou(){const t=this.nullBitmap;t&&t.byteLength>0&&(this.get=iu(this.get),this.set=su(this.set))}N.new=tu,N.from=nu,Yo.prototype.get=function(t){return Uc.visit(this,t)},Yo.prototype.set=function(t,e){return li.visit(this,t,e)},Yo.prototype.indexOf=function(t,e){return Vc.visit(this,t,e)},Yo.prototype.toArray=function(){return qc.visit(this)},Yo.prototype.getByteWidth=function(){return Zc.visit(this.type)},Yo.prototype[Symbol.iterator]=function(){return Yc.visit(this)},Yo.prototype._bindDataAccessors=ou,Object.keys(k).map(t=>k[t]).filter(t=>"number"===typeof t).filter(t=>t!==k.NONE).forEach(t=>{const e=Qc.visit(t);e.prototype["get"]=za(Uc.getVisitFn(t)),e.prototype["set"]=$a(li.getVisitFn(t)),e.prototype["indexOf"]=$a(Vc.getVisitFn(t)),e.prototype["toArray"]=Pa(qc.getVisitFn(t)),e.prototype["getByteWidth"]=ru(Zc.getVisitFn(t)),e.prototype[Symbol.iterator]=Pa(Yc.getVisitFn(t))});class au extends Hn{constructor(...t){let e=null;t[0]instanceof ur&&(e=t.shift());let n=Qn(lu,t);if(!e&&!(e=n[0]&&n[0].schema))throw new TypeError("Table must be initialized with a Schema or at least one RecordBatch");n[0]||(n[0]=new hu(e)),super(new Kt(e.fields),n),this._schema=e,this._chunks=n}static empty(t=new ur([])){return new au(t,[])}static from(t){if(!t)return au.empty();if("object"===typeof t){let e=Object(ae["i"])(t["values"])?cu(t):Object(ae["f"])(t["values"])?uu(t):null;if(null!==e)return e}let e=du.from(t);return Object(ae["l"])(e)?(async()=>await au.from(await e))():e.isSync()&&(e=e.open())?e.schema?new au(e.schema,[...e]):au.empty():(async t=>{const e=await t,n=e.schema,r=[];if(n){for await(let t of e)r.push(t);return new au(n,r)}return au.empty()})(e.open())}static async fromAsync(t){return await au.from(t)}static fromStruct(t){return au.new(t.data.childData,t.type.children)}static new(...t){return new au(...Vo(tr(t)))}get schema(){return this._schema}get length(){return this._length}get chunks(){return this._chunks}get numCols(){return this._numChildren}clone(t=this._chunks){return new au(this._schema,t)}getColumn(t){return this.getColumnAt(this.getColumnIndex(t))}getColumnAt(t){return this.getChildAt(t)}getColumnIndex(t){return this._schema.fields.findIndex(e=>e.name===t)}getChildAt(t){if(t<0||t>=this.numChildren)return null;let e,n;const r=this._schema.fields,i=this._children||(this._children=[]);if(n=i[t])return n;if(e=r[t]){const n=this._chunks.map(e=>e.getChildAt(t)).filter(t=>null!=t);if(n.length>0)return i[t]=new Jn(e,n)}return null}serialize(t="binary",e=!0){const n=e?Fo:Uo;return n.writeAll(this).toUint8Array(!0)}count(){return this._length}select(...t){const e=this._schema.fields.reduce((t,e,n)=>t.set(e.name,n),new Map);return this.selectAt(...t.map(t=>e.get(t)).filter(t=>t>-1))}selectAt(...t){const e=this._schema.selectAt(...t);return new au(e,this._chunks.map(({length:n,data:{childData:r}})=>new lu(e,n,t.map(t=>r[t]).filter(Boolean))))}assign(t){const e=this._schema.fields,[n,r]=t.schema.fields.reduce((t,n,r)=>{const[i,s]=t,o=e.findIndex(t=>t.name===n.name);return~o?s[o]=r:i.push(r),t},[[],[]]),i=this._schema.assign(t.schema),s=[...e.map((e,n,i,s=r[n])=>void 0===s?this.getColumnAt(n):t.getColumnAt(s)),...n.map(e=>t.getColumnAt(e))].filter(Boolean);return new au(...Po(i,s))}}function cu(t){const{type:e}=t;return e instanceof Kt?au.fromStruct(Ta.from(t)):null}function uu(t){const{type:e}=t;return e instanceof Kt?Ta.from(t).then(t=>au.fromStruct(t)):null}class lu extends Ta{constructor(...t){let e,n,r=t[0];if(t[1]instanceof ie)[,e,n]=t;else{const n=r.fields,[,i,s]=t;e=ie.Struct(new Kt(n),0,i,0,null,s)}super(e,n),this._schema=r}static from(t){return Object(ae["i"])(t["values"]),au.from(t)}static new(...t){const[e,n]=er(t),r=n.filter(t=>t instanceof N);return new lu(...Ro(new ur(e),r.map(t=>t.data)))}clone(t,e=this._children){return new lu(this._schema,t,e)}concat(...t){const e=this._schema,n=Hn.flatten(this,...t);return new au(e,n.map(({data:t})=>new lu(e,t)))}get schema(){return this._schema}get numCols(){return this._schema.fields.length}get dictionaries(){return this._dictionaries||(this._dictionaries=fu.collect(this))}select(...t){const e=this._schema.fields.reduce((t,e,n)=>t.set(e.name,n),new Map);return this.selectAt(...t.map(t=>e.get(t)).filter(t=>t>-1))}selectAt(...t){const e=this._schema.selectAt(...t),n=t.map(t=>this.data.childData[t]).filter(Boolean);return new lu(e,this.length,n)}}class hu extends lu{constructor(t){super(t,0,t.fields.map(t=>ie.new(t.type,0,0,0)))}}class fu extends nt{constructor(){super(...arguments),this.dictionaries=new Map}static collect(t){return(new fu).visit(t.data,new Kt(t.schema.fields)).dictionaries}visit(t,e){return Ot.isDictionary(e)?this.visitDictionary(t,e):(t.childData.forEach((t,n)=>this.visit(t,e.children[n].type)),this)}visitDictionary(t,e){const n=t.dictionary;return n&&n.length>0&&this.dictionaries.set(e.id,n),this}}class du extends Si["d"]{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return Object(ae["l"])(e)?e.then(()=>this):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return C["a"].toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return C["a"].toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof du?t:Object(ae["e"])(t)?Bu(t):Object(ae["h"])(t)?Eu(t):Object(ae["l"])(t)?(async()=>await du.from(await t))():Object(ae["g"])(t)||Object(ae["m"])(t)||Object(ae["n"])(t)||Object(ae["f"])(t)?ju(new Bi(t)):xu(new Ti(t))}static readAll(t){return t instanceof du?t.isSync()?Au(t):Tu(t):Object(ae["e"])(t)||ArrayBuffer.isView(t)||Object(ae["i"])(t)||Object(ae["j"])(t)?Au(t):Tu(t)}}class pu extends du{constructor(t){super(t),this._impl=t}[Symbol.iterator](){return this._impl[Symbol.iterator]()}async*[Symbol.asyncIterator](){yield*this[Symbol.iterator]()}}class yu extends du{constructor(t){super(t),this._impl=t}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class bu extends pu{constructor(t){super(t),this._impl=t}}class gu extends yu{constructor(t){super(t),this._impl=t}}class mu{constructor(t=new Map){this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){return new lu(this.schema,t.length,this._loadVectors(t,e,this.schema.fields))}_loadDictionaryBatch(t,e){const{id:n,isDelta:r,data:i}=t,{dictionaries:s,schema:o}=this,a=s.get(n);if(r||!a){const t=o.dictionaries.get(n);return a&&r?a.concat(N.new(this._loadVectors(i,e,[t])[0])):N.new(this._loadVectors(i,e,[t])[0])}return a}_loadVectors(t,e,n){return new Vi(e,t.nodes,t.buffers,this.dictionaries).visitMany(n)}}class _u extends mu{constructor(t,e){super(e),this._reader=Object(ae["e"])(t)?new bo(this._handle=t):new po(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=Su(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Si["c"]}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Si["c"]}next(){if(this.closed)return Si["c"];let t,{_reader:e}=this;while(t=this._readNextMessageAndValidate())if(t.isSchema())this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength),i=this._loadRecordBatch(n,r);return{done:!1,value:i}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new hu(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class vu extends mu{constructor(t,e){super(e),this._reader=new yo(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}async cancel(){!this.closed&&(this.closed=!0)&&(await this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}async open(t){return this.closed||(this.autoDestroy=Su(this,t),this.schema||(this.schema=await this._reader.readSchema())||await this.cancel()),this}async throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?await this.reset()._reader.throw(t):Si["c"]}async return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?await this.reset()._reader.return(t):Si["c"]}async next(){if(this.closed)return Si["c"];let t,{_reader:e}=this;while(t=await this._readNextMessageAndValidate())if(t.isSchema())await this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=await e.readMessageBody(t.bodyLength),i=this._loadRecordBatch(n,r);return{done:!1,value:i}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=await e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new hu(this.schema)}):await this.return()}async _readNextMessageAndValidate(t){return await this._reader.readMessage(t)}}class wu extends _u{constructor(t,e){super(t instanceof Ei?t:new Ei(t),e)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){if(this.closed)return null;this._footer||this.open();const e=this._footer&&this._footer.getRecordBatch(t);if(e&&this._handle.seek(e.offset)){const t=this._reader.readMessage(W.RecordBatch);if(t&&t.isRecordBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength),r=this._loadRecordBatch(e,n);return r}}return null}_readDictionaryBatch(t){const e=this._footer&&this._footer.getDictionaryBatch(t);if(e&&this._handle.seek(e.offset)){const t=this._reader.readMessage(W.DictionaryBatch);if(t&&t.isDictionaryBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}_readFooter(){const{_handle:t}=this,e=t.size-Oo,n=t.readInt32(e),r=t.readAt(e-n,n);return wi.decode(r)}_readNextMessageAndValidate(t){if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer&&this._footer.getRecordBatch(this._recordBatchIndex);if(e&&this._handle.seek(e.offset))return this._reader.readMessage(t)}return null}}class Ou extends vu{constructor(t,...e){const n="number"!==typeof e[0]?e.shift():void 0,r=e[0]instanceof Map?e.shift():void 0;super(t instanceof Di?t:new Di(t,n),r)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isFile(){return!0}isAsync(){return!0}async open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=await this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&await this._readDictionaryBatch(this._dictionaryIndex++)}return await super.open(t)}async readRecordBatch(t){if(this.closed)return null;this._footer||await this.open();const e=this._footer&&this._footer.getRecordBatch(t);if(e&&await this._handle.seek(e.offset)){const t=await this._reader.readMessage(W.RecordBatch);if(t&&t.isRecordBatch()){const e=t.header(),n=await this._reader.readMessageBody(t.bodyLength),r=this._loadRecordBatch(e,n);return r}}return null}async _readDictionaryBatch(t){const e=this._footer&&this._footer.getDictionaryBatch(t);if(e&&await this._handle.seek(e.offset)){const t=await this._reader.readMessage(W.DictionaryBatch);if(t&&t.isDictionaryBatch()){const e=t.header(),n=await this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}async _readFooter(){const{_handle:t}=this;t._pending&&await t._pending;const e=t.size-Oo,n=await t.readInt32(e),r=await t.readAt(e-n,n);return wi.decode(r)}async _readNextMessageAndValidate(t){if(this._footer||await this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&await this._handle.seek(e.offset))return await this._reader.readMessage(t)}return null}}class Iu extends _u{constructor(t,e){super(t,e)}_loadVectors(t,e,n){return new Pi(e,t.nodes,t.buffers,this.dictionaries).visitMany(n)}}function Su(t,e){return e&&"boolean"===typeof e["autoDestroy"]?e["autoDestroy"]:t["autoDestroy"]}function*Au(t){const e=du.from(t);try{if(!e.open({autoDestroy:!1}).closed)do{yield e}while(!e.reset().open().closed)}finally{e.cancel()}}async function*Tu(t){const e=await du.from(t);try{if(!(await e.open({autoDestroy:!1})).closed)do{yield e}while(!(await e.reset().open()).closed)}finally{await e.cancel()}}function Bu(t){return new pu(new Iu(t))}function xu(t){const e=t.peek(wo+7&-8);return e&&e.byteLength>=4?vo(e)?new bu(new wu(t.read())):new pu(new _u(t)):new pu(new _u(function*(){}()))}async function ju(t){const e=await t.peek(wo+7&-8);return e&&e.byteLength>=4?vo(e)?new bu(new wu(await t.read())):new yu(new vu(t)):new yu(new vu(async function*(){}()))}async function Eu(t){const{size:e}=await t.stat(),n=new Di(t,e);return e>=Io&&vo(await n.readAt(0,wo+7&-8))?new gu(new Ou(n)):new yu(new vu(n))}function Du(t,e){if(Object(ae["f"])(t))return Fu(t,e);if(Object(ae["i"])(t))return Lu(t,e);throw new Error("toDOMStream() must be called with an Iterable or AsyncIterable")}function Lu(t,e){let n=null;const r=e&&"bytes"===e.type||!1,i=e&&e.highWaterMark||2**24;return new ReadableStream({...e,start(e){s(e,n||(n=t[Symbol.iterator]()))},pull(t){n?s(t,n):t.close()},cancel(){n&&n.return&&n.return(),n=null}},{highWaterMark:r?i:void 0,...e});function s(t,e){let n,i=null,s=t.desiredSize||null;while(!(i=e.next(r?s:null)).done)if(ArrayBuffer.isView(i.value)&&(n=Object(ne["toUint8Array"])(i.value))&&(null!=s&&r&&(s=s-n.byteLength+1),i.value=n),t.enqueue(i.value),null!=s&&--s<=0)return;t.close()}}function Fu(t,e){let n=null;const r=e&&"bytes"===e.type||!1,i=e&&e.highWaterMark||2**24;return new ReadableStream({...e,async start(e){await s(e,n||(n=t[Symbol.asyncIterator]()))},async pull(t){n?await s(t,n):t.close()},async cancel(){n&&n.return&&await n.return(),n=null}},{highWaterMark:r?i:void 0,...e});async function s(t,e){let n,i=null,s=t.desiredSize||null;while(!(i=await e.next(r?s:null)).done)if(ArrayBuffer.isView(i.value)&&(n=Object(ne["toUint8Array"])(i.value))&&(null!=s&&r&&(s=s-n.byteLength+1),i.value=n),t.enqueue(i.value),null!=s&&--s<=0)return;t.close()}}function Uu(t){return new Mu(t)}class Mu{constructor(t){this._numChunks=0,this._finished=!1,this._bufferedSize=0;const{["readableStrategy"]:e,["writableStrategy"]:n,["queueingStrategy"]:r="count",...i}=t;this._controller=null,this._builder=ge.new(i),this._getSize="bytes"!==r?Cu:Nu;const{["highWaterMark"]:s=("bytes"===r?16384:1e3)}={...e},{["highWaterMark"]:o=("bytes"===r?16384:1e3)}={...n};this["readable"]=new ReadableStream({["cancel"]:()=>{this._builder.clear()},["pull"]:t=>{this._maybeFlush(this._builder,this._controller=t)},["start"]:t=>{this._maybeFlush(this._builder,this._controller=t)}},{highWaterMark:s,size:"bytes"!==r?Cu:Nu}),this["writable"]=new WritableStream({["abort"]:()=>{this._builder.clear()},["write"]:()=>{this._maybeFlush(this._builder,this._controller)},["close"]:()=>{this._maybeFlush(this._builder.finish(),this._controller)}},{highWaterMark:o,size:t=>this._writeValueAndReturnChunkSize(t)})}_writeValueAndReturnChunkSize(t){const e=this._bufferedSize;return this._bufferedSize=this._getSize(this._builder.append(t)),this._bufferedSize-e}_maybeFlush(t,e){null!==e&&(this._bufferedSize>=e.desiredSize&&++this._numChunks&&this._enqueue(e,t.toVector()),t.finished&&((t.length>0||0===this._numChunks)&&++this._numChunks&&this._enqueue(e,t.toVector()),!this._finished&&(this._finished=!0)&&this._enqueue(e,null)))}_enqueue(t,e){this._bufferedSize=0,this._controller=null,null===e?t.close():t.enqueue(e)}}const Cu=t=>t.length,Nu=t=>t.byteLength;function ku(t,e){const n=new Ai;let r=null;const i=new ReadableStream({async cancel(){await n.close()},async start(t){await o(t,r||(r=await s()))},async pull(t){r?await o(t,r):t.close()}});return{writable:new WritableStream(n,{highWaterMark:16384,...t}),readable:i};async function s(){return await(await du.from(n)).open(e)}async function o(t,e){let n=t.desiredSize,r=null;while(!(r=await e.next()).done)if(t.enqueue(r.value),null!=n&&--n<=0)return;t.close()}}function Ru(t,e){const n=new this(t),r=new Bi(n),i=new ReadableStream({type:"bytes",async cancel(){await r.cancel()},async pull(t){await s(t)},async start(t){await s(t)}},{highWaterMark:16384,...e});return{writable:new WritableStream(n,t),readable:i};async function s(t){let e=null,n=t.desiredSize;while(e=await r.read(n||null))if(t.enqueue(e),null!=n&&(n-=e.byteLength)<=0)return;t.close()}}class Vu{eq(t){return t instanceof Vu||(t=new Pu(t)),new Ku(this,t)}le(t){return t instanceof Vu||(t=new Pu(t)),new Gu(this,t)}ge(t){return t instanceof Vu||(t=new Pu(t)),new Ju(this,t)}lt(t){return new Zu(this.ge(t))}gt(t){return new Zu(this.le(t))}ne(t){return new Zu(this.eq(t))}}class Pu extends Vu{constructor(t){super(),this.v=t}}class zu extends Vu{constructor(t){super(),this.name=t}bind(t){if(!this.colidx){this.colidx=-1;const e=t.schema.fields;for(let t=-1;++t<e.length;)if(e[t].name===this.name){this.colidx=t;break}if(this.colidx<0)throw new Error(`Failed to bind Col "${this.name}"`)}const e=this.vector=t.getChildAt(this.colidx);return t=>e.get(t)}}class $u{and(...t){return new Hu(this,...t)}or(...t){return new qu(this,...t)}not(){return new Zu(this)}}class Yu extends $u{constructor(t,e){super(),this.left=t,this.right=e}bind(t){return this.left instanceof Pu?this.right instanceof Pu?this._bindLitLit(t,this.left,this.right):this._bindLitCol(t,this.left,this.right):this.right instanceof Pu?this._bindColLit(t,this.left,this.right):this._bindColCol(t,this.left,this.right)}}class Wu extends $u{constructor(...t){super(),this.children=t}}Wu.prototype.children=Object.freeze([]);class Hu extends Wu{constructor(...t){t=t.reduce((t,e)=>t.concat(e instanceof Hu?e.children:e),[]),super(...t)}bind(t){const e=this.children.map(e=>e.bind(t));return(t,n)=>e.every(e=>e(t,n))}}class qu extends Wu{constructor(...t){t=t.reduce((t,e)=>t.concat(e instanceof qu?e.children:e),[]),super(...t)}bind(t){const e=this.children.map(e=>e.bind(t));return(t,n)=>e.some(e=>e(t,n))}}class Ku extends Yu{_bindLitLit(t,e,n){const r=e.v==n.v;return()=>r}_bindColCol(t,e,n){const r=e.bind(t),i=n.bind(t);return(t,e)=>r(t,e)==i(t,e)}_bindColLit(t,e,n){const r=e.bind(t);if(e.vector instanceof Zo){let t;const r=e.vector;return r.dictionary!==this.lastDictionary?(t=r.reverseLookup(n.v),this.lastDictionary=r.dictionary,this.lastKey=t):t=this.lastKey,-1===t?()=>!1:e=>r.getKey(e)===t}return(t,e)=>r(t,e)==n.v}_bindLitCol(t,e,n){return this._bindColLit(t,n,e)}}class Gu extends Yu{_bindLitLit(t,e,n){const r=e.v<=n.v;return()=>r}_bindColCol(t,e,n){const r=e.bind(t),i=n.bind(t);return(t,e)=>r(t,e)<=i(t,e)}_bindColLit(t,e,n){const r=e.bind(t);return(t,e)=>r(t,e)<=n.v}_bindLitCol(t,e,n){const r=n.bind(t);return(t,n)=>e.v<=r(t,n)}}class Ju extends Yu{_bindLitLit(t,e,n){const r=e.v>=n.v;return()=>r}_bindColCol(t,e,n){const r=e.bind(t),i=n.bind(t);return(t,e)=>r(t,e)>=i(t,e)}_bindColLit(t,e,n){const r=e.bind(t);return(t,e)=>r(t,e)>=n.v}_bindLitCol(t,e,n){const r=n.bind(t);return(t,n)=>e.v>=r(t,n)}}class Zu extends $u{constructor(t){super(),this.child=t}bind(t){const e=this.child.bind(t);return(t,n)=>!e(t,n)}}au.prototype.countBy=function(t){return new Xu(this.chunks).countBy(t)},au.prototype.scan=function(t,e){return new Xu(this.chunks).scan(t,e)},au.prototype.scanReverse=function(t,e){return new Xu(this.chunks).scanReverse(t,e)},au.prototype.filter=function(t){return new Xu(this.chunks).filter(t)};class Xu extends au{filter(t){return new tl(this.chunks,t)}scan(t,e){const n=this.chunks,r=n.length;for(let i=-1;++i<r;){const r=n[i];e&&e(r);for(let e=-1,n=r.length;++e<n;)t(e,r)}}scanReverse(t,e){const n=this.chunks,r=n.length;for(let i=r;--i>=0;){const r=n[i];e&&e(r);for(let e=r.length;--e>=0;)t(e,r)}}countBy(t){const e=this.chunks,n=e.length,r="string"===typeof t?new zu(t):t;r.bind(e[n-1]);const i=r.vector;if(!Ot.isDictionary(i.type))throw new Error("countBy currently only supports dictionary-encoded columns");const s=Math.ceil(Math.log(i.length)/Math.log(256)),o=4==s?Uint32Array:s>=2?Uint16Array:Uint8Array,a=new o(i.dictionary.length);for(let c=-1;++c<n;){const t=e[c];r.bind(t);const n=r.vector.indices;for(let e=-1,r=t.length;++e<r;){let t=n.get(e);null!==t&&a[t]++}}return new Qu(i.dictionary,la.from(a))}}class Qu extends au{constructor(t,e){const n=new ur([new lr("values",t.type),new lr("counts",e.type)]);super(new lu(n,e.length,[t,e]))}toJSON(){const t=this.getColumnAt(0),e=this.getColumnAt(1),n={};for(let r=-1;++r<this.length;)n[t.get(r)]=e.get(r);return n}}class tl extends Xu{constructor(t,e){super(t),this._predicate=e}scan(t,e){const n=this._chunks,r=n.length;for(let i=-1;++i<r;){const r=n[i],s=this._predicate.bind(r);let o=!1;for(let n=-1,i=r.length;++n<i;)s(n,r)&&(e&&!o&&(e(r),o=!0),t(n,r))}}scanReverse(t,e){const n=this._chunks,r=n.length;for(let i=r;--i>=0;){const r=n[i],s=this._predicate.bind(r);let o=!1;for(let n=r.length;--n>=0;)s(n,r)&&(e&&!o&&(e(r),o=!0),t(n,r))}}count(){let t=0;const e=this._chunks,n=e.length;for(let r=-1;++r<n;){const n=e[r],i=this._predicate.bind(n);for(let e=-1,r=n.length;++e<r;)i(e,n)&&++t}return t}*[Symbol.iterator](){const t=this._chunks,e=t.length;for(let n=-1;++n<e;){const e=t[n],r=this._predicate.bind(e);for(let t=-1,n=e.length;++t<n;)r(t,e)&&(yield e.get(t))}}filter(t){return new tl(this._chunks,this._predicate.and(t))}countBy(t){const e=this._chunks,n=e.length,r="string"===typeof t?new zu(t):t;r.bind(e[n-1]);const i=r.vector;if(!Ot.isDictionary(i.type))throw new Error("countBy currently only supports dictionary-encoded columns");const s=Math.ceil(Math.log(i.length)/Math.log(256)),o=4==s?Uint32Array:s>=2?Uint16Array:Uint8Array,a=new o(i.dictionary.length);for(let c=-1;++c<n;){const t=e[c],n=this._predicate.bind(t);r.bind(t);const i=r.vector.indices;for(let e=-1,r=t.length;++e<r;){let r=i.get(e);null!==r&&n(e,t)&&a[r]++}}return new Qu(i.dictionary,la.from(a))}}C["a"].toDOMStream=Du,ge["throughDOM"]=Uu,du["throughDOM"]=ku,Lo["throughDOM"]=Ru;
/**
 * @license
 * Copyright 2018-2021 Streamlit Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var el,nl=function(){function t(t,e,n,r){var i=this;this.getCell=function(t,e){var n=t<i.headerRows&&e<i.headerColumns,r=t>=i.headerRows&&e<i.headerColumns,s=t<i.headerRows&&e>=i.headerColumns;if(n){var o=["blank"];return e>0&&o.push("level"+t),{type:"blank",classNames:o.join(" "),content:""}}if(s){var a=e-i.headerColumns;o=["col_heading","level"+t,"col"+a];return{type:"columns",classNames:o.join(" "),content:i.getContent(i.columnsTable,a,t)}}if(r){var c=t-i.headerRows;o=["row_heading","level"+e,"row"+c];return{type:"index",id:"T_"+i.uuid+"level"+e+"_row"+c,classNames:o.join(" "),content:i.getContent(i.indexTable,c,e)}}c=t-i.headerRows,a=e-i.headerColumns,o=["data","row"+c,"col"+a];var u=i.styler?i.getContent(i.styler.displayValuesTable,c,a):i.getContent(i.dataTable,c,a);return{type:"data",id:"T_"+i.uuid+"row"+c+"_col"+a,classNames:o.join(" "),content:u}},this.getContent=function(t,e,n){var r=t.getColumnAt(n);if(null===r)return"";var s=i.getColumnTypeId(t,n);switch(s){case k.Timestamp:return i.nanosToDate(r.get(e));default:return r.get(e)}},this.dataTable=au.from(t),this.indexTable=au.from(e),this.columnsTable=au.from(n),this.styler=r?{caption:r.caption,displayValuesTable:au.from(r.displayValues),styles:r.styles,uuid:r.uuid}:void 0}return Object.defineProperty(t.prototype,"rows",{get:function(){return this.indexTable.length+this.columnsTable.numCols},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"columns",{get:function(){return this.indexTable.numCols+this.columnsTable.length},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"headerRows",{get:function(){return this.rows-this.dataRows},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"headerColumns",{get:function(){return this.columns-this.dataColumns},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dataRows",{get:function(){return this.dataTable.length},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dataColumns",{get:function(){return this.dataTable.numCols},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"uuid",{get:function(){return this.styler&&this.styler.uuid},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"caption",{get:function(){return this.styler&&this.styler.caption},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"styles",{get:function(){return this.styler&&this.styler.styles},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"table",{get:function(){return this.dataTable},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"index",{get:function(){return this.indexTable},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"columnTable",{get:function(){return this.columnsTable},enumerable:!0,configurable:!0}),t.prototype.serialize=function(){return{data:this.dataTable.serialize(),index:this.indexTable.serialize(),columns:this.columnsTable.serialize()}},t.prototype.getColumnTypeId=function(t,e){return t.schema.fields[e].type.typeId},t.prototype.nanosToDate=function(t){return new Date(t/1e6)},t}(),rl=function(){return rl=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},rl.apply(this,arguments)};
/**
 * @license
 * Copyright 2018-2021 Streamlit Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(function(t){t["COMPONENT_READY"]="streamlit:componentReady",t["SET_COMPONENT_VALUE"]="streamlit:setComponentValue",t["SET_FRAME_HEIGHT"]="streamlit:setFrameHeight"})(el||(el={}));var il=function(){function t(){}return t.API_VERSION=1,t.RENDER_EVENT="streamlit:render",t.events=new F,t.registeredMessageListener=!1,t.setComponentReady=function(){t.registeredMessageListener||(window.addEventListener("message",t.onMessageEvent),t.registeredMessageListener=!0),t.sendBackMsg(el.COMPONENT_READY,{apiVersion:t.API_VERSION})},t.setFrameHeight=function(e){void 0===e&&(e=document.body.scrollHeight),e!==t.lastFrameHeight&&(t.lastFrameHeight=e,t.sendBackMsg(el.SET_FRAME_HEIGHT,{height:e}))},t.setComponentValue=function(e){var n;e instanceof nl?(n="dataframe",e=e.serialize()):ol(e)?(n="bytes",e=new Uint8Array(e.buffer)):e instanceof ArrayBuffer?(n="bytes",e=new Uint8Array(e)):n="json",t.sendBackMsg(el.SET_COMPONENT_VALUE,{value:e,dataType:n})},t.onMessageEvent=function(e){var n=e.data["type"];switch(n){case t.RENDER_EVENT:t.onRenderMessage(e.data);break}},t.onRenderMessage=function(e){var n=e["args"];null==n&&(console.error("Got null args in onRenderMessage. This should never happen"),n={});var r=e["dfs"]&&e["dfs"].length>0?t.argsDataframeToObject(e["dfs"]):{};n=rl(rl({},n),r);var i=Boolean(e["disabled"]),s=e["theme"];s&&sl(s);var o={disabled:i,args:n,theme:s},a=new CustomEvent(t.RENDER_EVENT,{detail:o});t.events.dispatchEvent(a)},t.argsDataframeToObject=function(e){var n=e.map((function(e){var n=e.key,r=e.value;return[n,t.toArrowTable(r)]}));return Object.fromEntries(n)},t.toArrowTable=function(t){var e=t.data,n=e.data,r=e.index,i=e.columns,s=e.styler;return new nl(n,r,i,s)},t.sendBackMsg=function(t,e){window.parent.postMessage(rl({isStreamlitMessage:!0,type:t},e),"*")},t}(),sl=function(t){var e=document.createElement("style");document.head.appendChild(e),e.innerHTML="\n    :root {\n      --primary-color: "+t.primaryColor+";\n      --background-color: "+t.backgroundColor+";\n      --secondary-background-color: "+t.secondaryBackgroundColor+";\n      --text-color: "+t.textColor+";\n      --font: "+t.font+";\n    }\n\n    body {\n      background-color: var(--background-color);\n      color: var(--text-color);\n    }\n  "};function ol(t){var e=!1;try{e=t instanceof BigInt64Array||t instanceof BigUint64Array}catch(n){}return t instanceof Int8Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array||e}
/**
 * @license
 * Copyright 2018-2021 Streamlit Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var al=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}al(e,t),e.prototype.componentDidMount=function(){il.setFrameHeight()},e.prototype.componentDidUpdate=function(){il.setFrameHeight()}})(u.a.PureComponent)},da6a:function(t,e,n){"use strict";var r=n("4cec"),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};function u(t){return r.isMemo(t)?a:c[t["$$typeof"]]||i}c[r.ForwardRef]=o,c[r.Memo]=a;var l=Object.defineProperty,h=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,y=Object.prototype;function b(t,e,n){if("string"!==typeof e){if(y){var r=p(e);r&&r!==y&&b(t,r,n)}var i=h(e);f&&(i=i.concat(f(e)));for(var o=u(t),a=u(e),c=0;c<i.length;++c){var g=i[c];if(!s[g]&&(!n||!n[g])&&(!a||!a[g])&&(!o||!o[g])){var m=d(e,g);try{l(t,g,m)}catch(_){}}}}return t}t.exports=b},e3db:function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},f673:function(t,e,n){"use strict";n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return s})),n.d(e,"d",(function(){return o})),n.d(e,"b",(function(){return a}));var r=n("6bfb");const i=Object.freeze({done:!0,value:void 0});class s{constructor(t){this._json=t}get schema(){return this._json["schema"]}get batches(){return this._json["batches"]||[]}get dictionaries(){return this._json["dictionaries"]||[]}}class o{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class a extends o{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise(t=>this._closedPromiseResolve=t)}get closed(){return this._closedPromise}async cancel(t){await this.return(t)}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;while(t.length>0)t.shift().resolve(i);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return r["a"].toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return r["a"].toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}async throw(t){return await this.abort(t),i}async return(t){return await this.close(),i}async read(t){return(await this.next(t,"read")).value}async peek(t){return(await this.next(t,"peek")).value}next(...t){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise((t,e)=>{this.resolvers.push({resolve:t,reject:e})}):Promise.resolve(i)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error(this+" is closed")}}}}]);
//# sourceMappingURL=chunk-vendors.a2e4d9cb.js.map