(function(e){function t(t){for(var r,s,i=t[0],a=t[1],l=t[2],b=0,j=[];b<i.length;b++)s=i[b],Object.prototype.hasOwnProperty.call(c,s)&&c[s]&&j.push(c[s][0]),c[s]=0;for(r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r]);u&&u(t);while(j.length)j.shift()();return o.push.apply(o,l||[]),n()}function n(){for(var e,t=0;t<o.length;t++){for(var n=o[t],r=!0,i=1;i<n.length;i++){var a=n[i];0!==c[a]&&(r=!1)}r&&(o.splice(t--,1),e=s(s.s=n[0]))}return e}var r={},c={app:0},o=[];function s(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,s),n.l=!0,n.exports}s.m=e,s.c=r,s.d=function(e,t,n){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(s.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)s.d(n,r,function(t){return e[t]}.bind(null,r));return n},s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="";var i=window["webpackJsonp"]=window["webpackJsonp"]||[],a=i.push.bind(i);i.push=t,i=i.slice();for(var l=0;l<i.length;l++)t(i[l]);var u=a;o.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("cd49")},"04d9":function(e,t,n){"use strict";n("7746")},"44dc":function(e,t,n){"use strict";n("a61a")},7746:function(e,t,n){},a61a:function(e,t,n){},cd49:function(e,t,n){"use strict";n.r(t);var r=n("7a23");const c={id:"app"};function o(e,t,n,o,s,i){const a=Object(r["u"])("MyComponent"),l=Object(r["u"])("WithStreamlitConnection");return Object(r["o"])(),Object(r["d"])("div",c,[Object(r["g"])(l,null,{default:Object(r["x"])(({args:e})=>[Object(r["g"])(a,{args:e},null,8,["args"])]),_:1})])}const s=e=>(Object(r["q"])("data-v-448f46e8"),e=e(),Object(r["p"])(),e),i={class:"menu"},a=s(()=>Object(r["e"])("hr",null,null,-1)),l=["onClick"];function u(e,t,n,c,o,s){return Object(r["o"])(),Object(r["d"])("div",i,[Object(r["e"])("div",{class:Object(r["i"])(["container-xxl d-flex flex-column flex-shrink-0",{"p-3":!c.isHorizontal,"p-h":c.isHorizontal,"nav-justified":c.isHorizontal}]),style:Object(r["j"])(c.styleObjectToString(c.styles["container"]))},[c.menuTitle?(Object(r["o"])(),Object(r["d"])(r["a"],{key:0},[Object(r["e"])("a",{href:"#",class:"menu-title align-items-center mb-md-0 me-md-auto text-decoration-none",style:Object(r["j"])(c.styleObjectToString(c.styles["menu-title"]))},[Object(r["e"])("i",{class:Object(r["i"])(["icon",c.menuIcon]),style:Object(r["j"])(c.styleObjectToString(c.styles["menu-icon"]))},null,6),Object(r["f"])(" "+Object(r["v"])(c.menuTitle),1)],4),a],64)):Object(r["c"])("",!0),Object(r["e"])("ul",{class:Object(r["i"])(["nav nav-pills mb-auto",{"flex-column":!c.isHorizontal,"nav-justified":c.isHorizontal}]),style:Object(r["j"])(c.styleObjectToString(c.styles["nav"]))},[(Object(r["o"])(!0),Object(r["d"])(r["a"],null,Object(r["s"])(n.args.options,(e,t)=>(Object(r["o"])(),Object(r["d"])("li",{class:"nav-item",key:e,style:Object(r["j"])(c.styleObjectToString(c.styles["nav-item"]))},["---"===e?(Object(r["o"])(),Object(r["d"])("hr",{key:0,class:Object(r["i"])({vr:c.isHorizontal}),style:Object(r["j"])(c.styleObjectToString(c.styles["separator"]))},null,6)):(Object(r["o"])(),Object(r["d"])("a",{key:1,href:"javascript:void(0);",class:Object(r["i"])(["nav-link",{active:t==c.selectedIndex,"nav-link-horizontal":c.isHorizontal}]),onClick:n=>c.onClicked(t,e),"aria-current":"page",style:Object(r["j"])(c.styleObjectToString(c.styles["nav-link"])+c.styleObjectToString(c.styles["nav-link-selected"],t==c.selectedIndex))},[Object(r["e"])("i",{class:Object(r["i"])(["icon",c.icons[t]]),style:Object(r["j"])(c.styleObjectToString(c.styles["icon"]))},null,6),Object(r["f"])(" "+Object(r["v"])(e),1)],14,l))],4))),128))],6)],6)])}var b=n("d092");function j(){Object(r["l"])(()=>{b["a"].setFrameHeight()}),Object(r["n"])(()=>{b["a"].setFrameHeight()})}
/**
 * @license
 * Copyright 2018-2020 Streamlit Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */n("ab8b"),n("cd74");function d(e){return"bi-"!==e.slice(0,3)?"bi-"+e:e}var O={name:"MyComponent",props:["args"],setup(e){j();const t=Object(r["r"])(e.args.menuTitle),n="horizontal"==e.args.orientation,c=Object(r["r"])(e.args.menuIcon||"bi-menu-up");c.value=d(c.value);const o=Object(r["r"])(e.args.icons||[]);for(let r=0;r<e.args.options.length;r++)o.value[r]||(o.value[r]="bi-caret-right"),o.value[r]=d(o.value[r]);const s=Object(r["r"])(e.args.defaultIndex),i=(e,t)=>{s.value=e,b["a"].setComponentValue(t)},a=(e,t)=>{if("undefined"===typeof t&&(t=!0),!t)return"";let n="";for(const r in e)n+=`${r}:${e[r]};`;return n},l=Object(r["r"])(e.args.styles||{}),u=t=>{console.log("chosen index is: ",t),t>=0&&t<e.args.options.length?i(t,e.args.options[t]):console.warn("Invalid index for triggerMenuClick")};return Object(r["w"])(()=>e.args.manualSelect,(t,n)=>{void 0!==t&&null!==t&&t!==n&&i(t,e.args.options[t])}),{triggerMenuClick:u,selectedIndex:s,menuTitle:t,menuIcon:c,icons:o,styles:l,onClicked:i,styleObjectToString:a,isHorizontal:n}}},p=(n("fce6"),n("6b0d")),f=n.n(p);const v=f()(O,[["render",u],["__scopeId","data-v-448f46e8"]]);var g=v;const y=e=>(Object(r["q"])("data-v-bef81972"),e=e(),Object(r["p"])(),e),m={key:0},h=y(()=>Object(r["e"])("h1",{class:"err__title"},"Component Error",-1)),k={class:"err__msg"};function x(e,t,n,c,o,s){return Object(r["o"])(),Object(r["d"])("div",null,[""!=e.componentError?(Object(r["o"])(),Object(r["d"])("div",m,[h,Object(r["e"])("div",k,"Message: "+Object(r["v"])(e.componentError),1)])):null!=e.renderData?Object(r["t"])(e.$slots,"default",{key:1,args:e.renderData.args,disabled:e.renderData.disabled},void 0,!0):Object(r["c"])("",!0)])}var S=Object(r["h"])({name:"WithStreamlitConnection",setup(){const e=Object(r["r"])(void 0),t=Object(r["r"])(""),n=n=>{const r=n;e.value=r.detail,t.value=""};return Object(r["l"])(()=>{b["a"].events.addEventListener(b["a"].RENDER_EVENT,n),b["a"].setComponentReady()}),Object(r["n"])(()=>{""!=t.value&&b["a"].setFrameHeight()}),Object(r["m"])(()=>{b["a"].events.removeEventListener(b["a"].RENDER_EVENT,n)}),Object(r["k"])(e=>{t.value=String(e)}),{renderData:e,componentError:t}}});n("44dc");const T=f()(S,[["render",x],["__scopeId","data-v-bef81972"]]);var _=T,C=Object(r["h"])({name:"App",components:{MyComponent:g,WithStreamlitConnection:_}});n("04d9");const E=f()(C,[["render",o]]);var w=E;Object(r["b"])(w).mount("#app")},d4cd:function(e,t,n){},fce6:function(e,t,n){"use strict";n("d4cd")}});
//# sourceMappingURL=app.3d75d26b.js.map