{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue?6a6e", "webpack:///./src/streamlit/WithStreamlitConnection.vue?df18", "webpack:///./src/App.vue?5a4f", "webpack:///./src/MyComponent.vue", "webpack:///./src/streamlit/StreamlitVue.ts", "webpack:///./src/MyComponent.vue?82ad", "webpack:///./src/streamlit/WithStreamlitConnection.vue?82f3", "webpack:///./src/streamlit/WithStreamlitConnection.vue", "webpack:///./src/streamlit/WithStreamlitConnection.vue?22ba", "webpack:///./src/App.vue?47b3", "webpack:///./src/App.vue?8ecf", "webpack:///./src/main.ts", "webpack:///./src/MyComponent.vue?79a3"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "_hoisted_1", "id", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_MyComponent", "_resolveComponent", "_component_WithStreamlitConnection", "_openBlock", "_createElementBlock", "_createVNode", "default", "_withCtx", "args", "_", "class", "_createElementVNode", "_normalizeClass", "isHorizontal", "style", "_normalizeStyle", "styleObjectToString", "styles", "menuTitle", "_Fragment", "href", "menuIcon", "_toDisplayString", "_hoisted_2", "_renderList", "options", "option", "vr", "active", "selectedIndex", "onClick", "$event", "onClicked", "aria-current", "icons", "useStreamlit", "onMounted", "Streamlit", "setFrameHeight", "onUpdated", "getFullIconName", "props", "setup", "ref", "orientation", "defaultIndex", "index", "setComponentValue", "obj", "condition", "styleString", "triggerMenuClick", "console", "log", "warn", "watch", "manualSelect", "newClickPos", "oldClickPos", "undefined", "__exports__", "_withScopeId", "_pushScopeId", "_popScopeId", "_hoisted_3", "componentError", "renderData", "_renderSlot", "$slots", "disabled", "_createCommentVNode", "defineComponent", "onRenderEvent", "event", "renderEvent", "detail", "events", "addEventListener", "RENDER_EVENT", "setComponentReady", "onUnmounted", "removeEventListener", "onErrorCaptured", "err", "String", "components", "MyComponent", "WithStreamlitConnection", "createApp", "App", "mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,6ECvJT,W,oCCAA,W,uGCEA,MAAMyC,EAAa,CAAEC,GAAI,OAEnB,SAAUC,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,EAAyBC,eAAkB,eAC3CC,EAAqCD,eAAkB,2BAE7D,OAAQE,iBAAcC,eAAoB,MAAOb,EAAY,CAC3Dc,eAAaH,EAAoC,KAAM,CACrDI,QAASC,eAAS,EAAGC,UAAW,CAC9BH,eAAaL,EAAwB,CAAEQ,KAAMA,GAAQ,KAAM,EAAG,CAAC,WAEjEC,EAAG,M,2ECZAC,MAAM,Q,QASHC,eAAI,oB,8DATZP,eA2BM,MA3BNb,EA2BM,CA1BFoB,eAyBM,OAzBDD,MAAKE,eAAA,CAAC,iDAAgD,QAAkBf,EAAAgB,aAAY,MAAQhB,EAAAgB,aAAY,gBAAmBhB,EAAAgB,gBAAgBC,MAAKC,eAAElB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,gB,CAC9JpB,EAAAqB,W,iBAAhBd,eAQWe,OAAA,CAAAtC,IAAA,IAPP8B,eAKI,KALDS,KAAK,IAAIV,MAAM,wEACjBI,MAAKC,eAAElB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,iB,CAE/BN,eAAwF,KAArFD,MAAKE,eAAA,CAAC,OAAef,EAAAwB,WAAWP,MAAKC,eAAElB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,gB,uBAAmB,IACxFK,eAAEzB,EAAAqB,WAAS,I,GAEnBK,G,2BAEAZ,eAcK,MAdDD,MAAKE,eAAA,CAAC,wBAAuB,gBAA0Bf,EAAAgB,aAAY,gBAAmBhB,EAAAgB,gBACzFC,MAAKC,eAAElB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,U,qBAE/Bb,eAUKe,OAAA,KAAAK,eAVqC5B,EAAAY,KAAKiB,QAAO,CAAzBC,EAAO1F,K,iBAApCoE,eAUK,MAVDM,MAAM,WAA+C7B,IAAK6C,EAC7DZ,MAAKC,eAAElB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,e,CAEkB,QAAXS,G,iBAAtCtB,eAA0G,M,MAArGM,MAAKE,eAAA,CAAAe,GAAO9B,EAAAgB,eAAwCC,MAAKC,eAAElB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,gB,2BAC3Fb,eAKI,K,MALMgB,KAAK,sBAAsBV,MAAKE,eAAA,CAAC,WAAU,CAAAgB,OAAkB5F,GAAK6D,EAAAgC,cAAa,sBAAwBhC,EAAAgB,gBAChHiB,QAAKC,GAAElC,EAAAmC,UAAUhG,EAAG0F,GAASO,eAAa,OAC1CnB,MAAKC,eAAElB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,aAAepB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,qBAAsBjF,GAAK6D,EAAAgC,iB,CACpGlB,eAAmF,KAAhFD,MAAKE,eAAA,CAAC,OAAef,EAAAqC,MAAMlG,KAAK8E,MAAKC,eAAElB,EAAAmB,oBAAoBnB,EAAAoB,OAAO,W,uBAAc,IACnFK,eAAEI,GAAM,I,6CCjB1B,SAAUS,IAUdC,eAAU,KAGRC,OAAUC,mBAGZC,eAAU,KAERF,OAAUC;;;;;;;;;;;;;;;;uBDcd,SAASE,EAAgB1E,GACrB,MAA2B,QAApBA,EAAKwB,MAAM,EAAE,GAAc,MAAQxB,EAAOA,EAGtC,OACXA,KAAM,cACN2E,MAAO,CAAC,QACRC,MAAMD,GACFN,IAEA,MAAMjB,EAAYyB,eAAIF,EAAMjC,KAAKU,WAC3BL,EAAyC,cAA1B4B,EAAMjC,KAAKoC,YAC1BvB,EAAWsB,eAAIF,EAAMjC,KAAKa,UAAY,cAC5CA,EAAS9C,MAAQiE,EAAgBnB,EAAS9C,OAC1C,MAAM2D,EAAQS,eAAIF,EAAMjC,KAAK0B,OAAS,IACtC,IAAK,IAAIlG,EAAI,EAAGA,EAAIyG,EAAMjC,KAAKiB,QAAQvF,OAAQF,IACtCkG,EAAM3D,MAAMvC,KACbkG,EAAM3D,MAAMvC,GAAK,kBAErBkG,EAAM3D,MAAMvC,GAAKwG,EAAgBN,EAAM3D,MAAMvC,IAEjD,MAAM6F,EAAgBc,eAAIF,EAAMjC,KAAKqC,cAC/Bb,EAAYA,CAACc,EAAOpB,KACtBG,EAActD,MAAQuE,EACtBT,OAAUU,kBAAkBrB,IAE1BV,EAAsBA,CAACgC,EAAKC,KAI9B,GAHyB,qBAAdA,IACPA,GAAY,IAEXA,EACD,MAAO,GAEX,IAAIC,EAAc,GAClB,IAAK,MAAMrE,KAAOmE,EACdE,GAAgB,GAAErE,KAAOmE,EAAInE,MAEjC,OAAOqE,GAELjC,EAAS0B,eAAIF,EAAMjC,KAAKS,QAAU,IAGlCkC,EAAoBL,IACtBM,QAAQC,IAAI,oBAAqBP,GAC7BA,GAAS,GAAKA,EAAQL,EAAMjC,KAAKiB,QAAQvF,OACzC8F,EAAUc,EAAOL,EAAMjC,KAAKiB,QAAQqB,IAEpCM,QAAQE,KAAK,uCAcrB,OATAC,eACI,IAAMd,EAAMjC,KAAKgD,aACjB,CAACC,EAAaC,UACUC,IAAhBF,GAA6C,OAAhBA,GAAwBA,IAAgBC,GACzE1B,EAAUyB,EAAahB,EAAMjC,KAAKiB,QAAQgC,MAK3C,CACHN,mBACAtB,gBACAX,YACAG,WACAa,QACAjB,SACAe,YACAhB,sBACAH,kB,iCErGZ,MAAM+C,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAE1E,QCPf,MAAMC,EAAe9E,IAAM+E,eAAa,mBAAmB/E,EAAEA,IAAIgF,iBAAchF,GACzEQ,EAAa,CAAEV,IAAK,GACpB0C,EAA2BsC,EAAa,IAAmBlD,eAAoB,KAAM,CAAED,MAAO,cAAgB,mBAAoB,IAClIsD,EAAa,CAAEtD,MAAO,YAEtB,SAAUjB,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQI,iBAAcC,eAAoB,MAAO,KAAM,CAC7B,IAAvBV,EAAKuE,gBACD9D,iBAAcC,eAAoB,MAAOb,EAAY,CACpDgC,EACAZ,eAAoB,MAAOqD,EAAY,YAAc1C,eAAiB5B,EAAKuE,gBAAiB,MAE1E,MAAnBvE,EAAKwE,WACJC,eAAYzE,EAAK0E,OAAQ,UAAW,CAClCvF,IAAK,EACL2B,KAAMd,EAAKwE,WAAW1D,KACtB6D,SAAU3E,EAAKwE,WAAWG,eACzBV,GAAW,GACdW,eAAoB,IAAI,KCTnBC,qBAAgB,CAC7BzG,KAAM,0BACN4E,QACE,MAAMwB,EAAavB,oBAAiBgB,GAC9BM,EAAiBtB,eAAI,IAErB6B,EAAiBC,IACrB,MAAMC,EAAcD,EACpBP,EAAW3F,MAAQmG,EAAYC,OAC/BV,EAAe1F,MAAQ,IA4BzB,OAvBA6D,eAAU,KACRC,OAAUuC,OAAOC,iBAAiBxC,OAAUyC,aAAcN,GAC1DnC,OAAU0C,sBAEZxC,eAAU,KAKoB,IAAxB0B,EAAe1F,OACjB8D,OAAUC,mBAGd0C,eAAY,KACV3C,OAAUuC,OAAOK,oBACf5C,OAAUyC,aACVN,KAGJU,eAAgBC,IACdlB,EAAe1F,MAAQ6G,OAAOD,KAGzB,CACLjB,aACAD,qB,UC3CN,MAAM,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAE1E,QCEAM,iBAAgB,CAC7BzG,KAAM,MACNuH,WAAY,CACVC,cACAC,6B,UCRJ,MAAM,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAAS9F,KAErD,QCNf+F,eAAUC,GAAKC,MAAM,S,yDCHrB", "file": "js/app.3d75d26b.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader-v16/dist/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./App.vue?vue&type=style&index=0&id=07d8329a&lang=css\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./WithStreamlitConnection.vue?vue&type=style&index=0&id=bef81972&scoped=true&lang=css\"", "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { id: \"app\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_MyComponent = _resolveComponent(\"MyComponent\")!\n  const _component_WithStreamlitConnection = _resolveComponent(\"WithStreamlitConnection\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(_component_WithStreamlitConnection, null, {\n      default: _withCtx(({ args }) => [\n        _createVNode(_component_MyComponent, { args: args }, null, 8, [\"args\"])\n      ]),\n      _: 1\n    })\n  ]))\n}", "<template>\n    <div class=\"menu\">\n        <div class=\"container-xxl d-flex flex-column flex-shrink-0\" :class=\"{'p-3': !isHorizontal, 'p-h':isHorizontal, 'nav-justified': isHorizontal}\" :style=\"styleObjectToString(styles['container'])\">\n            <template v-if=\"menuTitle\">\n                <a href=\"#\" class=\"menu-title align-items-center mb-md-0 me-md-auto text-decoration-none\"\n                :style=\"styleObjectToString(styles['menu-title'])\"\n                >\n                    <i class=\"icon\" :class=\"menuIcon\" :style=\"styleObjectToString(styles['menu-icon'])\"></i>\n                    {{menuTitle}}\n                </a>\n            <hr>\n            </template>\n            <ul class=\"nav nav-pills mb-auto\" :class=\"{'flex-column': !isHorizontal, 'nav-justified': isHorizontal}\"\n            :style=\"styleObjectToString(styles['nav'])\"\n            >\n                <li class=\"nav-item\" v-for=\"(option,i) in args.options\" :key=\"option\"\n                :style=\"styleObjectToString(styles['nav-item'])\"\n                >\n                    <hr :class=\"{vr: isHorizontal}\" v-if=\"option === '---'\" :style=\"styleObjectToString(styles['separator'])\">\n                    <a v-else href=\"javascript:void(0);\" class=\"nav-link\" :class=\"{active: i == selectedIndex, 'nav-link-horizontal':isHorizontal}\" \n                    @click=\"onClicked(i, option)\" aria-current=\"page\" \n                    :style=\"styleObjectToString(styles['nav-link']) + styleObjectToString(styles['nav-link-selected'], i == selectedIndex)\">\n                        <i class=\"icon\" :class=\"icons[i]\" :style=\"styleObjectToString(styles['icon'])\"></i>\n                        {{option}}\n                    </a>\n                </li>\n            </ul>\n        </div>\n    </div>\n</template>\n<script>\nimport { ref, watch } from \"vue\"\nimport { Streamlit } from \"streamlit-component-lib\"\nimport { useStreamlit } from \"./streamlit\"\nimport \"bootstrap/dist/css/bootstrap.min.css\"\nimport \"bootstrap-icons/font/bootstrap-icons.css\";\n\n\nfunction getFullIconName(name) {\n    return name.slice(0,3) !== \"bi-\"? \"bi-\" + name : name\n}\n\nexport default {\n    name: \"MyComponent\",\n    props: [\"args\"], // Arguments that are passed to the plugin in Python are accessible in prop \"args\"\n    setup(props) {\n        useStreamlit() // lifecycle hooks for automatic Streamlit resize\n\n        const menuTitle = ref(props.args.menuTitle)\n        const isHorizontal = props.args.orientation == \"horizontal\"\n        const menuIcon = ref(props.args.menuIcon || \"bi-menu-up\")\n        menuIcon.value = getFullIconName(menuIcon.value)\n        const icons = ref(props.args.icons || [])\n        for (let i = 0; i < props.args.options.length; i++) {\n            if (!icons.value[i]) {\n                icons.value[i] = \"bi-caret-right\";\n            }\n            icons.value[i] = getFullIconName(icons.value[i]);\n        }\n        const selectedIndex = ref(props.args.defaultIndex)\n        const onClicked = (index, option) => {\n            selectedIndex.value = index\n            Streamlit.setComponentValue(option)\n        }\n        const styleObjectToString = (obj, condition) => {\n            if (typeof condition === \"undefined\") {\n                condition = true\n            }\n            if (!condition) {\n                return \"\"\n            }\n            let styleString = \"\"\n            for (const key in obj) {\n                styleString += `${key}:${obj[key]};`\n            }\n            return styleString\n        }\n        const styles = ref(props.args.styles || {});\n        // const manualSelect = props.args.manualSelect === undefined || props.args.manualSelect === null ? NaN : props.args.manualSelect;\n        \n        const triggerMenuClick = (index) => {\n            console.log(\"chosen index is: \", index)\n            if (index >= 0 && index < props.args.options.length) {\n                onClicked(index, props.args.options[index]);\n            } else {\n                console.warn('Invalid index for triggerMenuClick');\n            }\n        }\n\n\n        watch(\n            () => props.args.manualSelect,\n            (newClickPos, oldClickPos) => {\n                if (newClickPos !== undefined && newClickPos !== null && newClickPos !== oldClickPos) {\n                onClicked(newClickPos, props.args.options[newClickPos])\n                }\n            }\n        )\n\n        return {\n            triggerMenuClick,\n            selectedIndex,\n            menuTitle,\n            menuIcon,\n            icons,\n            styles,\n            onClicked,\n            styleObjectToString,\n            isHorizontal\n        }\n    },\n}\n</script>\n\n<style scoped>\n.icon {\n    font-size: 1rem;\n    margin-right: 0.5rem;\n}\n\n.menu hr {\n    margin-top: 0.5rem;\n    margin-bottom: 0.5rem;\n}\n\n.menu .container-xxl {\n   background-color: var(--secondary-background-color);\n   border-radius: 0.5rem;\n}\n\n.menu-title {\n    margin-left: 0.75rem;\n    margin-right: 0.75rem;\n}\n\n.menu-title, .menu-title .icon {\n    font-size: 1.5rem;\n}\n\n.menu-title .icon {\n    margin-right: 0.75rem;\n}\n\n.menu-title, .menu .nav-link, .menu .nav-item, hr {\n    color: var(--text-color);\n}\n\n.nav-link.active {\n    color: white;\n}\n\n.nav-link:hover {\n    background-color: var(--hover-color);\n}\n\n.nav-link-hover:hover {\n    background-color: inherit;\n}\n\n.menu .nav-link {\n    /* box-shadow: 0 0px 0.2rem #aaa; */\n    margin-top: 0.25rem;\n    margin-bottom: 0.25rem;\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n}\n\n.menu .nav-link-horizontal {\n    /* margin-top: 0.25rem;\n    margin-bottom: 0.25rem; */\n    padding-top: 0.25rem;\n    padding-bottom: 0.25rem;\n}\n\n.p-h {\n    padding: 0.5rem 0.75rem;\n}\n\n.container .flex-column {\n    padding-top: 0.25rem;\n}\n\n.menu .nav-item .nav-link.active{\n    background-color: var(--primary-color);\n}\n\n.nav-link.active, .nav-link.active+.icon {\n    font-weight: 900;\n}\n\n.vr {\n    width: 1px; \n    height: 80%;\n}\n</style>\n", "/**\n * Vue.js specific composables\n */\nimport { onMounted, onUpdated } from \"vue\"\nimport { Streamlit } from \"streamlit-component-lib\"\n\nexport function useStreamlit() {\n  /**\n   * Optional Streamlit Vue-based setup.\n   *\n   * You are not required call this function on your Streamlit\n   * component. If you decide not to call it, you should implement the\n   * `onMounted` and `onUpdated` functions in your own component,\n   * so that your plugin properly resizes.\n   */\n\n  onMounted((): void => {\n    // After we're rendered for the first time, tell Streamlit that our height\n    // has changed.\n    Streamlit.setFrameHeight()\n  })\n\n  onUpdated((): void => {\n    // After we're updated, tell Streamlit that our height may have changed.\n    Streamlit.setFrameHeight()\n  })\n}\n", "import { render } from \"./MyComponent.vue?vue&type=template&id=448f46e8&scoped=true\"\nimport script from \"./MyComponent.vue?vue&type=script&lang=js\"\nexport * from \"./MyComponent.vue?vue&type=script&lang=js\"\n\nimport \"./MyComponent.vue?vue&type=style&index=0&id=448f46e8&scoped=true&lang=css\"\n\nimport exportComponent from \"/root/code/streamlit-option-menu/streamlit_option_menu/frontend/node_modules/vue-loader-v16/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-448f46e8\"]])\n\nexport default __exports__", "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\"\n\nconst _withScopeId = n => (_pushScopeId(\"data-v-bef81972\"),n=n(),_popScopeId(),n)\nconst _hoisted_1 = { key: 0 }\nconst _hoisted_2 = /*#__PURE__*/ _withScopeId(() => /*#__PURE__*/_createElementVNode(\"h1\", { class: \"err__title\" }, \"Component Error\", -1))\nconst _hoisted_3 = { class: \"err__msg\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", null, [\n    (_ctx.componentError != '')\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n          _hoisted_2,\n          _createElementVNode(\"div\", _hoisted_3, \"Message: \" + _toDisplayString(_ctx.componentError), 1)\n        ]))\n      : (_ctx.renderData != null)\n        ? _renderSlot(_ctx.$slots, \"default\", {\n            key: 1,\n            args: _ctx.renderData.args,\n            disabled: _ctx.renderData.disabled\n          }, undefined, true)\n        : _createCommentVNode(\"\", true)\n  ]))\n}", "\nimport {\n  ref,\n  defineComponent,\n  onMounted,\n  onUpdated,\n  onUnmounted,\n  onErrorCaptured,\n} from \"vue\"\nimport { Streamlit, RenderData } from \"streamlit-component-lib\"\n\nexport default defineComponent({\n  name: \"WithStreamlitConnection\",\n  setup() {\n    const renderData = ref<RenderData>((undefined as unknown) as RenderData)\n    const componentError = ref(\"\")\n\n    const onRenderEvent = (event: Event): void => {\n      const renderEvent = event as CustomEvent<RenderData>\n      renderData.value = renderEvent.detail\n      componentError.value = \"\"\n    }\n\n    // Set up event listeners, and signal to Streamlit that we're ready.\n    // We won't render the component until we receive the first RENDER_EVENT.\n    onMounted(() => {\n      Streamlit.events.addEventListener(Streamlit.RENDER_EVENT, onRenderEvent)\n      Streamlit.setComponentReady()\n    })\n    onUpdated(() => {\n      // If our slot threw an error, we display it in render(). In this\n      // case, the slot won't be mounted and therefore won't call\n      // `setFrameHeight` on its own. We do it here so that the rendered\n      // error will be visible.\n      if (componentError.value != \"\") {\n        Streamlit.setFrameHeight()\n      }\n    })\n    onUnmounted(() => {\n      Streamlit.events.removeEventListener(\n        Streamlit.RENDER_EVENT,\n        onRenderEvent\n      )\n    })\n    onErrorCaptured(err => {\n      componentError.value = String(err)\n    })\n\n    return {\n      renderData,\n      componentError,\n    }\n  },\n})\n", "import { render } from \"./WithStreamlitConnection.vue?vue&type=template&id=bef81972&scoped=true&ts=true\"\nimport script from \"./WithStreamlitConnection.vue?vue&type=script&lang=ts\"\nexport * from \"./WithStreamlitConnection.vue?vue&type=script&lang=ts\"\n\nimport \"./WithStreamlitConnection.vue?vue&type=style&index=0&id=bef81972&scoped=true&lang=css\"\n\nimport exportComponent from \"/root/code/streamlit-option-menu/streamlit_option_menu/frontend/node_modules/vue-loader-v16/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-bef81972\"]])\n\nexport default __exports__", "\nimport { defineComponent } from \"vue\"\nimport MyComponent from \"./MyComponent.vue\"\n\n// \"withStreamlitConnection\" is a scoped slot. It bootstraps the\n// connection between your component and the Streamlit app, and handles\n// passing arguments from Python -> Component.\n//\n// You don't need to edit withStreamlitConnection (but you're welcome to!).\nimport WithStreamlitConnection from \"./streamlit/WithStreamlitConnection.vue\"\n\nexport default defineComponent({\n  name: \"App\",\n  components: {\n    MyComponent,\n    WithStreamlitConnection,\n  },\n})\n", "import { render } from \"./App.vue?vue&type=template&id=07d8329a&ts=true\"\nimport script from \"./App.vue?vue&type=script&lang=ts\"\nexport * from \"./App.vue?vue&type=script&lang=ts\"\n\nimport \"./App.vue?vue&type=style&index=0&id=07d8329a&lang=css\"\n\nimport exportComponent from \"/root/code/streamlit-option-menu/streamlit_option_menu/frontend/node_modules/vue-loader-v16/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { createApp } from 'vue'\nimport App from './App.vue'\n\ncreateApp(App).mount('#app')\n", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader-v16/dist/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./MyComponent.vue?vue&type=style&index=0&id=448f46e8&scoped=true&lang=css\""], "sourceRoot": ""}