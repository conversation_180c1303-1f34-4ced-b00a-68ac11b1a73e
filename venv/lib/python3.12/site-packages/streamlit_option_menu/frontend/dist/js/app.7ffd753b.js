(function(e){function t(t){for(var r,a,s=t[0],i=t[1],l=t[2],b=0,j=[];b<s.length;b++)a=s[b],Object.prototype.hasOwnProperty.call(c,a)&&c[a]&&j.push(c[a][0]),c[a]=0;for(r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r]);u&&u(t);while(j.length)j.shift()();return o.push.apply(o,l||[]),n()}function n(){for(var e,t=0;t<o.length;t++){for(var n=o[t],r=!0,s=1;s<n.length;s++){var i=n[s];0!==c[i]&&(r=!1)}r&&(o.splice(t--,1),e=a(a.s=n[0]))}return e}var r={},c={app:0},o=[];function a(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,a),n.l=!0,n.exports}a.m=e,a.c=r,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)a.d(n,r,function(t){return e[t]}.bind(null,r));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="";var s=window["webpackJsonp"]=window["webpackJsonp"]||[],i=s.push.bind(s);s.push=t,s=s.slice();for(var l=0;l<s.length;l++)t(s[l]);var u=i;o.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("cd49")},"04d9":function(e,t,n){"use strict";n("7746")},"09d0":function(e,t,n){"use strict";n("f6f8")},"44dc":function(e,t,n){"use strict";n("a61a")},7746:function(e,t,n){},a61a:function(e,t,n){},cd49:function(e,t,n){"use strict";n.r(t);var r=n("7a23");const c={id:"app"};function o(e,t,n,o,a,s){const i=Object(r["u"])("MyComponent"),l=Object(r["u"])("WithStreamlitConnection");return Object(r["o"])(),Object(r["d"])("div",c,[Object(r["g"])(l,null,{default:Object(r["x"])(({args:e})=>[Object(r["g"])(i,{args:e},null,8,["args"])]),_:1})])}const a=e=>(Object(r["q"])("data-v-5af006b8"),e=e(),Object(r["p"])(),e),s={class:"menu"},i=a(()=>Object(r["e"])("hr",null,null,-1)),l=["onClick"];function u(e,t,n,c,o,a){return Object(r["o"])(),Object(r["d"])("div",s,[Object(r["e"])("div",{class:Object(r["i"])(["container-xxl d-flex flex-column flex-shrink-0",{"p-3":!c.isHorizontal,"p-h":c.isHorizontal,"nav-justified":c.isHorizontal}]),style:Object(r["j"])(c.styleObjectToString(c.styles["container"]))},[c.menuTitle?(Object(r["o"])(),Object(r["d"])(r["a"],{key:0},[Object(r["e"])("a",{href:"#",class:"menu-title align-items-center mb-md-0 me-md-auto text-decoration-none",style:Object(r["j"])(c.styleObjectToString(c.styles["menu-title"]))},[Object(r["e"])("i",{class:Object(r["i"])(["icon",c.menuIcon]),style:Object(r["j"])(c.styleObjectToString(c.styles["menu-icon"]))},null,6),Object(r["f"])(" "+Object(r["v"])(c.menuTitle),1)],4),i],64)):Object(r["c"])("",!0),Object(r["e"])("ul",{class:Object(r["i"])(["nav nav-pills mb-auto",{"flex-column":!c.isHorizontal,"nav-justified":c.isHorizontal}]),style:Object(r["j"])(c.styleObjectToString(c.styles["nav"]))},[(Object(r["o"])(!0),Object(r["d"])(r["a"],null,Object(r["s"])(n.args.options,(e,t)=>(Object(r["o"])(),Object(r["d"])("li",{class:"nav-item",key:e,style:Object(r["j"])(c.styleObjectToString(c.styles["nav-item"]))},["---"===e?(Object(r["o"])(),Object(r["d"])("hr",{key:0,class:Object(r["i"])({vr:c.isHorizontal}),style:Object(r["j"])(c.styleObjectToString(c.styles["separator"]))},null,6)):(Object(r["o"])(),Object(r["d"])("a",{key:1,href:"javascript:void(0);",class:Object(r["i"])(["nav-link",{active:t==c.selectedIndex,"nav-link-horizontal":c.isHorizontal}]),onClick:n=>c.onClicked(t,e),"aria-current":"page",style:Object(r["j"])(c.styleObjectToString(c.styles["nav-link"])+c.styleObjectToString(c.styles["nav-link-selected"],t==c.selectedIndex))},[Object(r["e"])("i",{class:Object(r["i"])(["icon",c.icons[t]]),style:Object(r["j"])(c.styleObjectToString(c.styles["icon"]))},null,6),Object(r["f"])(" "+Object(r["v"])(e),1)],14,l))],4))),128))],6)],6)])}var b=n("d092");function j(){Object(r["l"])(()=>{b["a"].setFrameHeight()}),Object(r["n"])(()=>{b["a"].setFrameHeight()})}
/**
 * @license
 * Copyright 2018-2020 Streamlit Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */n("ab8b"),n("cd74");function O(e){return"bi-"!==e.slice(0,3)?"bi-"+e:e}var d={name:"MyComponent",props:["args"],setup(e){j();const t=Object(r["r"])(e.args.menuTitle),n="horizontal"==e.args.orientation,c=Object(r["r"])(e.args.menuIcon||"bi-menu-up");c.value=O(c.value);const o=Object(r["r"])(e.args.icons||[]);for(let r=0;r<e.args.options.length;r++)o.value[r]||(o.value[r]="bi-caret-right"),o.value[r]=O(o.value[r]);const a=Object(r["r"])(e.args.defaultIndex),s=()=>{for(let t=0;t<e.args.options.length;t++)o.value[t]||(o.value[t]="bi-caret-right"),o.value[t]=O(o.value[t])};s();const i=(e,t)=>{a.value=e,b["a"].setComponentValue(t)},l=(e,t)=>{if("undefined"===typeof t&&(t=!0),!t)return"";let n="";for(const r in e)n+=`${r}:${e[r]};`;return n},u=Object(r["r"])(e.args.styles||{}),d=t=>{console.log("chosen index is: ",t),t>=0&&t<e.args.options.length?i(t,e.args.options[t]):console.warn("Invalid index for triggerMenuClick")};return Object(r["w"])(()=>e.args.icons,()=>{o.value=e.args.icons||[],s()}),Object(r["w"])(()=>e.args.manualSelect,(t,n)=>{void 0!==t&&null!==t&&t!==n&&i(t,e.args.options[t])}),{triggerMenuClick:d,selectedIndex:a,menuTitle:t,menuIcon:c,icons:o,styles:u,onClicked:i,styleObjectToString:l,isHorizontal:n}}},v=(n("09d0"),n("6b0d")),p=n.n(v);const f=p()(d,[["render",u],["__scopeId","data-v-5af006b8"]]);var g=f;const y=e=>(Object(r["q"])("data-v-bef81972"),e=e(),Object(r["p"])(),e),m={key:0},h=y(()=>Object(r["e"])("h1",{class:"err__title"},"Component Error",-1)),k={class:"err__msg"};function x(e,t,n,c,o,a){return Object(r["o"])(),Object(r["d"])("div",null,[""!=e.componentError?(Object(r["o"])(),Object(r["d"])("div",m,[h,Object(r["e"])("div",k,"Message: "+Object(r["v"])(e.componentError),1)])):null!=e.renderData?Object(r["t"])(e.$slots,"default",{key:1,args:e.renderData.args,disabled:e.renderData.disabled},void 0,!0):Object(r["c"])("",!0)])}var S=Object(r["h"])({name:"WithStreamlitConnection",setup(){const e=Object(r["r"])(void 0),t=Object(r["r"])(""),n=n=>{const r=n;e.value=r.detail,t.value=""};return Object(r["l"])(()=>{b["a"].events.addEventListener(b["a"].RENDER_EVENT,n),b["a"].setComponentReady()}),Object(r["n"])(()=>{""!=t.value&&b["a"].setFrameHeight()}),Object(r["m"])(()=>{b["a"].events.removeEventListener(b["a"].RENDER_EVENT,n)}),Object(r["k"])(e=>{t.value=String(e)}),{renderData:e,componentError:t}}});n("44dc");const T=p()(S,[["render",x],["__scopeId","data-v-bef81972"]]);var _=T,C=Object(r["h"])({name:"App",components:{MyComponent:g,WithStreamlitConnection:_}});n("04d9");const E=p()(C,[["render",o]]);var w=E;Object(r["b"])(w).mount("#app")},f6f8:function(e,t,n){}});
//# sourceMappingURL=app.7ffd753b.js.map