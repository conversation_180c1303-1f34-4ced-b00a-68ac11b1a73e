"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[4666],{90186:(e,t,i)=>{i.d(t,{$:()=>p,Z:()=>c});var s=i(66845),n=i(25621),l=i(66694),a=i(27466),r=i(38570),o=i(80318),d=i(40864);let p;!function(e){e.EXTRASMALL="xs",e.SMALL="sm",e.MEDIUM="md",e.LARGE="lg",e.EXTRALARGE="xl"}(p||(p={}));const c=function(e){let{value:t,width:i,size:c=p.SMALL,overrides:g}=e;const h=(0,n.u)(),u={xs:h.spacing.twoXS,sm:h.spacing.sm,md:h.spacing.lg,lg:h.spacing.xl,xl:h.spacing.twoXL},{activeTheme:m}=s.useContext(l.E),f=!(0,a.MJ)(m),x={BarContainer:{style:{marginTop:h.spacing.none,marginBottom:h.spacing.none,marginRight:h.spacing.none,marginLeft:h.spacing.none}},Bar:{style:e=>{let{$theme:t}=e;return{width:i?i.toString():void 0,marginTop:h.spacing.none,marginBottom:h.spacing.none,marginRight:h.spacing.none,marginLeft:h.spacing.none,height:u[c],backgroundColor:t.colors.progressbarTrackFill,borderTopLeftRadius:h.spacing.twoXS,borderTopRightRadius:h.spacing.twoXS,borderBottomLeftRadius:h.spacing.twoXS,borderBottomRightRadius:h.spacing.twoXS}}},BarProgress:{style:()=>({backgroundColor:f?h.colors.primary:h.colors.blue70,borderTopLeftRadius:h.spacing.twoXS,borderTopRightRadius:h.spacing.twoXS,borderBottomLeftRadius:h.spacing.twoXS,borderBottomRightRadius:h.spacing.twoXS})}};return(0,d.jsx)(r.Z,{value:t,overrides:(0,o.aO)(x,g)})}},77367:(e,t,i)=>{i.d(t,{R:()=>s});class s{setStatus(e){return new s(this.name,this.size,this.id,e)}constructor(e,t,i,s){this.name=void 0,this.size=void 0,this.status=void 0,this.id=void 0,this.name=e,this.size=t,this.id=i,this.status=s}}},14666:(e,t,i)=>{i.r(t),i.d(t,{default:()=>ce});var s=i(23183),n=i(72706),l=i.n(n),a=i(66845),r=i(16295),o=i(87814),d=i(50641);let p;!function(e){e.Gigabyte="gb",e.Megabyte="mb",e.Kilobyte="kb",e.Byte="b"}(p||(p={}));const c=(0,d.rA)()?1024:1e3,g=[p.Gigabyte,p.Megabyte,p.Kilobyte,p.Byte],h=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(t||(t=p.Byte),i<0&&(i=0),e<0)throw new Error("Size must be greater than or equal to 0");const s=g.indexOf(t);return s&&e>c/2?h(e/c,g[s-1],i):"".concat(e.toFixed(i)).concat(t.toUpperCase())};var u=i(98478),m=i(86659),f=i(8879),x=i(68411),w=i(51622),y=i(9003),b=i(81354),S=i(1515);const F=(0,S.Z)("section",{target:"e1b2p2ww15"})((e=>{let{isDisabled:t,theme:i}=e;return{display:"flex",alignItems:"center",padding:i.spacing.lg,backgroundColor:i.colors.secondaryBg,borderRadius:i.radii.lg,":focus":{outline:"none"},":focus-visible":{boxShadow:"0 0 0 1px ".concat(i.colors.primary)},color:t?i.colors.gray:i.colors.bodyText}}),""),v=(0,S.Z)("div",{target:"e1b2p2ww14"})((()=>({marginRight:"auto",alignItems:"center",display:"flex"})),""),j=(0,S.Z)("span",{target:"e1b2p2ww13"})((e=>{let{theme:t}=e;return{color:t.colors.darkenedBgMix100,marginRight:t.spacing.lg}}),""),I=(0,S.Z)("span",{target:"e1b2p2ww12"})((e=>{let{theme:t}=e;return{marginBottom:t.spacing.twoXS}}),""),M=(0,S.Z)("div",{target:"e1b2p2ww11"})({name:"1fttcpj",styles:"display:flex;flex-direction:column"}),z=(0,S.Z)("div",{target:"e1b2p2ww10"})((e=>{let{theme:t}=e;return{left:0,right:0,lineHeight:t.lineHeights.tight,paddingTop:t.spacing.md,paddingLeft:t.spacing.lg,paddingRight:t.spacing.lg}}),""),L=(0,S.Z)("ul",{target:"e1b2p2ww9"})((()=>({listStyleType:"none",marginBottom:0})),""),U=(0,S.Z)("li",{target:"e1b2p2ww8"})((e=>{let{theme:t}=e;return{margin:t.spacing.none,padding:t.spacing.none}}),""),C=(0,S.Z)("div",{target:"e1b2p2ww7"})((e=>{let{theme:t}=e;return{display:"flex",alignItems:"baseline",flex:1,paddingLeft:t.spacing.lg,overflow:"hidden"}}),""),B=(0,S.Z)("div",{target:"e1b2p2ww6"})((e=>{let{theme:t}=e;return{marginRight:t.spacing.sm,marginBottom:t.spacing.twoXS,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}}),""),Z=(0,S.Z)("div",{target:"e1b2p2ww5"})((e=>{let{theme:t}=e;return{display:"flex",alignItems:"center",marginBottom:t.spacing.twoXS}}),""),R=(0,S.Z)("span",{target:"e1b2p2ww4"})((e=>{let{theme:t}=e;return{marginRight:t.spacing.twoXS}}),""),k=(0,S.Z)("div",{target:"e1b2p2ww3"})((e=>{let{theme:t}=e;return{display:"flex",padding:t.spacing.twoXS,color:t.colors.darkenedBgMix100}}),""),T=(0,S.Z)("small",{target:"e1b2p2ww2"})((e=>{let{theme:t}=e;return{color:t.colors.danger,fontSize:t.fontSizes.sm,height:t.fontSizes.sm,lineHeight:t.fontSizes.sm,display:"flex",alignItems:"center",whiteSpace:"nowrap"}}),""),D=(0,S.Z)("span",{target:"e1b2p2ww1"})({name:"0",styles:""}),X=e=>({[F]:{display:"flex",flexDirection:"column",alignItems:"flex-start"},[v]:{marginBottom:e.spacing.lg},[j]:{display:"none"},[z]:{paddingRight:e.spacing.lg},[Z]:{maxWidth:"inherit",flex:1,alignItems:"flex-start",marginBottom:e.spacing.sm},[B]:{width:e.sizes.full},[C]:{flexDirection:"column"},[T]:{height:"auto",whiteSpace:"initial"},[D]:{display:"none"},[U]:{margin:e.spacing.none,padding:e.spacing.none}}),E=(0,S.Z)("div",{target:"e1b2p2ww0"})((e=>{let{theme:t}=e;return t.inSidebar?X(t):{["@media (max-width: ".concat(t.breakpoints.sm,")")]:X(t)}}),"");var P=i(74529),A=i(46927),V=i(33746),W=i(40864);const N=e=>{let{multiple:t,acceptedExtensions:i,maxSizeBytes:s}=e;return(0,W.jsxs)(v,{"data-testid":"stFileDropzoneInstructions",children:[(0,W.jsx)(j,{children:(0,W.jsx)(A.Z,{content:P.n,size:"threeXL"})}),(0,W.jsxs)(M,{children:[(0,W.jsxs)(I,{children:["Drag and drop file",t?"s":""," here"]}),(0,W.jsxs)(V.x,{children:["Limit ".concat(h(s,p.Byte,0)," per file"),i.length?" \u2022 ".concat(i.map((e=>e.replace(/^\./,"").toUpperCase())).join(", ")):null]})]})]})},O=e=>{let{onDrop:t,multiple:i,acceptedExtensions:s,maxSizeBytes:n,disabled:l,label:a}=e;return(0,W.jsx)(w.ZP,{onDrop:t,multiple:i,accept:s.length?s:void 0,maxSize:n,disabled:l,useFsAccessApi:!1,children:e=>{let{getRootProps:t,getInputProps:r}=e;return(0,W.jsxs)(F,{...t(),"data-testid":"stFileUploadDropzone",isDisabled:l,"aria-label":a,children:[(0,W.jsx)("input",{"data-testid":"stDropzoneInput",...r()}),(0,W.jsx)(N,{multiple:i,acceptedExtensions:s,maxSizeBytes:n}),(0,W.jsx)(y.ZP,{kind:b.nW.SECONDARY,disabled:l,size:b.V5.SMALL,children:"Browse files"})]})}})};var H=i(13005),_=i.n(H),G=i(30351),K=i(14609);const $=(0,S.Z)("div",{target:"e16k0npc1"})((e=>{let{theme:t}=e;return{display:"flex",alignItems:"center",justifyContent:"space-between",paddingBottom:t.spacing.twoXS,marginBottom:t.spacing.twoXS}}),""),q=(0,S.Z)("div",{target:"e16k0npc0"})((e=>{let{theme:t}=e;return{display:"flex",alignItems:"center",justifyContent:"center",color:t.colors.fadedText40}}),""),J=e=>{let{className:t,currentPage:i,totalPages:s,onNext:n,onPrevious:l}=e;return(0,W.jsxs)($,{className:t,"data-testid":"stPagination",children:[(0,W.jsx)(V.x,{children:"Showing page ".concat(i," of ").concat(s)}),(0,W.jsxs)(q,{children:[(0,W.jsx)(y.ZP,{onClick:l,kind:b.nW.MINIMAL,children:(0,W.jsx)(A.Z,{content:G.s,size:"xl"})}),(0,W.jsx)(y.ZP,{onClick:n,kind:b.nW.MINIMAL,children:(0,W.jsx)(A.Z,{content:K._,size:"xl"})})]})]})};var Y=i(88235);const Q=(e,t)=>Math.ceil(e.length/t),ee=e=>_()((t=>{let{pageSize:i,items:s,resetOnAdd:n,...l}=t;const[r,o]=(0,a.useState)(0),[d,p]=(0,a.useState)(Q(s,i)),c=(0,Y.D)(s);(0,a.useEffect)((()=>{c&&c.length!==s.length&&p(Q(s,i)),c&&c.length<s.length?n&&o(0):r+1>=d&&o(d-1)}),[s,r,i,c,n,d]);const g=s.slice(r*i,r*i+i);return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(e,{items:g,...l}),s.length>i?(0,W.jsx)(J,{className:"streamlit-paginator",pageSize:i,totalPages:d,currentPage:r+1,onNext:()=>{o(Math.min(r+1,d-1))},onPrevious:()=>{o(Math.max(0,r-1))}}):null]})}),e);var te=i(62288),ie=i(87847),se=i(31197),ne=i(90186);const le=e=>{let{fileInfo:t}=e;return"uploading"===t.status.type?(0,W.jsx)(ne.Z,{value:t.status.progress,size:ne.$.SMALL,overrides:{Bar:{style:{marginLeft:0,marginTop:"4px"}}}}):"error"===t.status.type?(0,W.jsxs)(T,{children:[(0,W.jsx)(R,{"data-testid":"stUploadedFileErrorMessage",children:t.status.errorMessage}),(0,W.jsx)(D,{children:(0,W.jsx)(A.Z,{content:te.j,size:"lg"})})]}):"uploaded"===t.status.type?(0,W.jsx)(V.x,{children:h(t.size,p.Byte)}):null},ae=e=>{let{fileInfo:t,onDelete:i}=e;return(0,W.jsxs)(Z,{className:"uploadedFile","data-testid":"stUploadedFile",children:[(0,W.jsx)(k,{children:(0,W.jsx)(A.Z,{content:ie.h,size:"twoXL"})}),(0,W.jsxs)(C,{className:"uploadedFileData",children:[(0,W.jsx)(B,{className:"uploadedFileName",title:t.name,children:t.name}),(0,W.jsx)(le,{fileInfo:t})]}),(0,W.jsx)("div",{"data-testid":"fileDeleteBtn",children:(0,W.jsx)(y.ZP,{onClick:()=>i(t.id),kind:b.nW.MINIMAL,children:(0,W.jsx)(A.Z,{content:se.U,size:"lg"})})})]})},re=ee((e=>{let{items:t,onDelete:i}=e;return(0,W.jsx)(L,{children:t.map((e=>(0,W.jsx)(U,{children:(0,W.jsx)(ae,{fileInfo:e,onDelete:i})},e.id)))})})),oe=e=>(0,W.jsx)(z,{children:(0,W.jsx)(re,{...e})});var de=i(77367);class pe extends a.PureComponent{constructor(e){super(e),this.formClearHelper=new o.K,this.localFileIdCounter=1,this.componentDidUpdate=()=>{if("ready"!==this.status)return;const e=this.createWidgetValue(),{element:t,widgetMgr:i}=this.props,s=i.getFileUploaderStateValue(t);l().isEqual(e,s)||i.setFileUploaderStateValue(t,e,{fromUi:!0})},this.reset=()=>{this.setState({files:[]})},this.dropHandler=(e,t)=>{const{element:i}=this.props,{multipleFiles:s}=i;if(!s&&0===e.length&&t.length>1){const i=t.findIndex((e=>1===e.errors.length&&"too-many-files"===e.errors[0].code));i>=0&&(e.push(t[i].file),t.splice(i,1))}if(this.props.uploadClient.fetchFileURLs(e).then((t=>{if(!s&&e.length>0){const e=this.state.files.find((e=>"error"!==e.status.type));e&&this.deleteFile(e.id)}l().zip(t,e).forEach((e=>{let[t,i]=e;this.uploadFile(t,i)}))})).catch((t=>{this.addFiles(e.map((e=>new de.R(e.name,e.size,this.nextLocalFileId(),{type:"error",errorMessage:t}))))})),t.length>0){const e=t.map((e=>{const{file:t}=e;return new de.R(t.name,t.size,this.nextLocalFileId(),{type:"error",errorMessage:this.getErrorMessage(e.errors[0].code,e.file)})}));this.addFiles(e)}},this.uploadFile=(e,t)=>{const i=s.Z.CancelToken.source(),n=new de.R(t.name,t.size,this.nextLocalFileId(),{type:"uploading",cancelToken:i,progress:1});this.addFile(n),this.props.uploadClient.uploadFile(this.props.element,e.uploadUrl,t,(e=>this.onUploadProgress(e,n.id)),i.token).then((()=>this.onUploadComplete(n.id,e))).catch((e=>{s.Z.isCancel(e)||this.updateFile(n.id,n.setStatus({type:"error",errorMessage:e?e.toString():"Unknown error"}))}))},this.onUploadComplete=(e,t)=>{const i=this.getFile(e);null!=i&&"uploading"===i.status.type&&this.updateFile(i.id,i.setStatus({type:"uploaded",fileId:t.fileId,fileUrls:t}))},this.getErrorMessage=(e,t)=>{switch(e){case"file-too-large":return"File must be ".concat(h(this.maxUploadSizeInBytes,p.Byte)," or smaller.");case"file-invalid-type":return"".concat(t.type," files are not allowed.");case"file-too-small":return"File size is too small.";case"too-many-files":return"Only one file is allowed.";default:return"Unexpected error. Please try again."}},this.deleteFile=e=>{const t=this.getFile(e);null!=t&&("uploading"===t.status.type&&t.status.cancelToken.cancel(),"uploaded"===t.status.type&&t.status.fileUrls.deleteUrl&&this.props.uploadClient.deleteFile(t.status.fileUrls.deleteUrl),this.removeFile(e))},this.addFile=e=>{this.setState((t=>({files:[...t.files,e]})))},this.addFiles=e=>{this.setState((t=>({files:[...t.files,...e]})))},this.removeFile=e=>{this.setState((t=>({files:t.files.filter((t=>t.id!==e))})))},this.getFile=e=>this.state.files.find((t=>t.id===e)),this.updateFile=(e,t)=>{this.setState((i=>({files:i.files.map((i=>i.id===e?t:i))})))},this.onUploadProgress=(e,t)=>{const i=this.getFile(t);if(null==i||"uploading"!==i.status.type)return;const s=Math.round(100*e.loaded/e.total);i.status.progress!==s&&this.updateFile(t,i.setStatus({type:"uploading",cancelToken:i.status.cancelToken,progress:s}))},this.onFormCleared=()=>{this.setState({files:[]},(()=>{const e=this.createWidgetValue();null!=e&&this.props.widgetMgr.setFileUploaderStateValue(this.props.element,e,{fromUi:!0})}))},this.state=this.initialValue}get initialValue(){const e={files:[],newestServerFileId:0},{widgetMgr:t,element:i}=this.props,s=t.getFileUploaderStateValue(i);if(null==s)return e;const{uploadedFileInfo:n}=s;return null==n||0===n.length?e:{files:n.map((e=>{const t=e.name,i=e.size,s=e.fileId,n=e.fileUrls;return new de.R(t,i,this.nextLocalFileId(),{type:"uploaded",fileId:s,fileUrls:n})}))}}componentWillUnmount(){this.formClearHelper.disconnect()}get maxUploadSizeInBytes(){return((e,t,i)=>{if(e<0)throw Error("Size must be 0 or greater");const s=g.findIndex((e=>e===t)),n=g.findIndex((e=>e===i));if(-1===s||-1===n)throw Error("Unexpected byte unit provided");if(s===n)return e;const l=Math.abs(s-n),a=c**l;return s>n?e/a:e*a})(this.props.element.maxUploadSizeMb,p.Megabyte,p.Byte)}get status(){return this.state.files.some((e=>"uploading"===e.status.type))?"updating":"ready"}componentDidMount(){const e=this.createWidgetValue(),{element:t,widgetMgr:i}=this.props;void 0===i.getFileUploaderStateValue(t)&&i.setFileUploaderStateValue(t,e,{fromUi:!1})}createWidgetValue(){const e=this.state.files.filter((e=>"uploaded"===e.status.type)).map((e=>{const{name:t,size:i,status:s}=e,{fileId:n,fileUrls:l}=s;return new r.jM({fileId:n,fileUrls:l,name:t,size:i})}));return new r.xO({uploadedFileInfo:e})}render(){var e;const{files:t}=this.state,{element:i,disabled:s,widgetMgr:n}=this.props,l=i.type;this.formClearHelper.manageFormClearListener(n,i.formId,this.onFormCleared);const a=t.slice().reverse();return(0,W.jsxs)(E,{"data-testid":"stFileUploader",children:[(0,W.jsx)(u.O,{label:i.label,disabled:s,labelVisibility:(0,d.iF)(null===(e=i.labelVisibility)||void 0===e?void 0:e.value),children:i.help&&(0,W.jsx)(m.dT,{children:(0,W.jsx)(f.Z,{content:i.help,placement:x.u.TOP_RIGHT})})}),(0,W.jsx)(O,{onDrop:this.dropHandler,multiple:i.multipleFiles,acceptedExtensions:l,maxSizeBytes:this.maxUploadSizeInBytes,label:i.label,disabled:s}),a.length>0&&(0,W.jsx)(oe,{items:a,pageSize:3,onDelete:this.deleteFile,resetOnAdd:!0})]})}nextLocalFileId(){return this.localFileIdCounter++}}const ce=pe},87814:(e,t,i)=>{i.d(t,{K:()=>n});var s=i(50641);class n{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,i){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,s.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,i),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}}}]);