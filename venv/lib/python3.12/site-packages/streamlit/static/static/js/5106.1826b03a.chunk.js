"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[5106],{87814:(e,t,i)=>{i.d(t,{K:()=>n});var s=i(50641);class n{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,i){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,s.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,i),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}},5106:(e,t,i)=>{i.r(t),i.d(t,{default:()=>w});var s=i(66845),n=i(72706),o=i(70461),r=i(25621),a=i(52347),l=i(87814),d=i(16295),h=i(50641),m=i(53608),u=i.n(m),c=i(98478),p=i(86659),g=i(8879),f=i(68411),b=i(1515),v=i(35704);const T=(0,b.Z)("div",{target:"ew7r33m3"})((e=>{let{disabled:t,theme:i}=e;return{alignItems:"center",backgroundColor:t?i.colors.gray:i.colors.primary,borderTopLeftRadius:"100%",borderTopRightRadius:"100%",borderBottomLeftRadius:"100%",borderBottomRightRadius:"100%",borderTopStyle:"none",borderBottomStyle:"none",borderRightStyle:"none",borderLeftStyle:"none",boxShadow:"none",display:"flex",height:i.radii.xl,justifyContent:"center",width:i.radii.xl,":focus":{outline:"none"},":focus-visible":{boxShadow:"0 0 0 0.2rem ".concat((0,v.DZ)(i.colors.primary,.5))}}}),""),y=(0,b.Z)("div",{target:"ew7r33m2"})((e=>{let{disabled:t,theme:i}=e;return{fontFamily:i.genericFonts.codeFont,fontSize:i.fontSizes.sm,paddingBottom:i.spacing.twoThirdsSmFont,color:t?i.colors.gray:i.colors.primary,top:"-22px",position:"absolute",whiteSpace:"nowrap",backgroundColor:i.colors.transparent,lineHeight:i.lineHeights.base,fontWeight:"normal"}}),""),R=(0,b.Z)("div",{target:"ew7r33m1"})((e=>{let{theme:t}=e;return{paddingBottom:t.spacing.none,paddingLeft:t.spacing.none,paddingRight:t.spacing.none,paddingTop:t.spacing.twoThirdsSmFont,justifyContent:"space-between",alignItems:"center",display:"flex"}}),""),V=(0,b.Z)("div",{target:"ew7r33m0"})((e=>{let{disabled:t,theme:i}=e;return{lineHeight:i.lineHeights.base,fontWeight:"normal",fontSize:i.fontSizes.sm,fontFamily:i.genericFonts.codeFont,color:t?i.colors.fadedText40:"inherit"}}),"");var x=i(40864);class C extends s.PureComponent{constructor(e){super(e),this.formClearHelper=new l.K,this.state=void 0,this.sliderRef=s.createRef(),this.thumbRef=[],this.thumbValueRef=[],this.commitWidgetValueDebounced=void 0,this.commitWidgetValue=e=>{this.props.widgetMgr.setDoubleArrayValue(this.props.element,this.state.value,e)},this.onFormCleared=()=>{this.setState(((e,t)=>({value:t.element.default})),(()=>this.commitWidgetValue({fromUi:!0})))},this.handleChange=e=>{let{value:t}=e;this.setState({value:t},(()=>this.commitWidgetValueDebounced({fromUi:!0})))},this.renderThumb=s.forwardRef(((e,t)=>{var i;const{$value:o,$thumbIndex:r}=e,a=r||0;this.thumbRef[a]=t,(i=this.thumbValueRef)[a]||(i[a]=s.createRef());const l=o?this.formatValue(o[r]):"",d=(0,n.pick)(e,["role","style","aria-valuemax","aria-valuemin","aria-valuenow","tabIndex","onKeyUp","onKeyDown","onMouseEnter","onMouseLeave","draggable"]),h={};return(this.props.element.options.length>0||this.isDateTimeType())&&(h["aria-valuetext"]=l),(0,x.jsx)(T,{...d,disabled:!0===e.$disabled,ref:this.thumbRef[a],"aria-valuetext":l,"aria-label":this.props.element.label,children:(0,x.jsx)(y,{className:"StyledThumbValue","data-testid":"stThumbValue",disabled:!0===e.$disabled,ref:this.thumbValueRef[a],children:l})})})),this.renderTickBar=()=>{const{disabled:e,element:t}=this.props,{max:i,min:s}=t;return(0,x.jsxs)(R,{"data-testid":"stTickBar",children:[(0,x.jsx)(V,{disabled:e,"data-testid":"stTickBarMin",children:this.formatValue(s)}),(0,x.jsx)(V,{disabled:e,"data-testid":"stTickBarMax",children:this.formatValue(i)})]})},this.commitWidgetValueDebounced=(0,h.Ds)(200,this.commitWidgetValue.bind(this)),this.state={value:this.initialValue}}get initialValue(){const e=this.props.widgetMgr.getDoubleArrayValue(this.props.element);return void 0!==e?e:this.props.element.default}componentDidMount(){setTimeout((()=>{this.thumbValueAlignment()}),0),this.props.element.setValue?this.updateFromProtobuf():this.commitWidgetValue({fromUi:!1})}componentDidUpdate(){this.maybeUpdateFromProtobuf()}componentWillUnmount(){this.formClearHelper.disconnect()}maybeUpdateFromProtobuf(){const{setValue:e}=this.props.element;e&&this.updateFromProtobuf()}updateFromProtobuf(){const{value:e}=this.props.element;this.props.element.setValue=!1,this.setState({value:e},(()=>{this.commitWidgetValue({fromUi:!1})}))}get value(){const{min:e,max:t}=this.props.element,{value:i}=this.state;let s=i[0],n=i.length>1?i[1]:i[0];return s>n&&(s=n),s<e&&(s=e),s>t&&(s=t),n<e&&(n=e),n>t&&(n=t),i.length>1?[s,n]:[s]}isDateTimeType(){const{dataType:e}=this.props.element;return e===d.iR.DataType.DATETIME||e===d.iR.DataType.DATE||e===d.iR.DataType.TIME}formatValue(e){const{format:t,options:i}=this.props.element;return this.isDateTimeType()?u().utc(e/1e3).format(t):i.length>0?(0,a.sprintf)(t,i[e]):(0,a.sprintf)(t,e)}alignValueOnThumb(e,t,i){if(e&&t&&i){const s=e.getBoundingClientRect(),n=t.getBoundingClientRect(),o=i.getBoundingClientRect(),r=n.left+n.width/2,a=r-o.width/2<s.left,l=r+o.width/2>s.right;i.style.left=a?"0":"",i.style.right=l?"0":""}}thumbValueAlignment(){var e,t,i,s;const n=this.sliderRef.current,o=null===(e=this.thumbRef[0])||void 0===e?void 0:e.current,r=null===(t=this.thumbRef[1])||void 0===t?void 0:t.current,a=null===(i=this.thumbValueRef[0])||void 0===i?void 0:i.current,l=null===(s=this.thumbValueRef[1])||void 0===s?void 0:s.current;if(this.alignValueOnThumb(n,o,a),this.alignValueOnThumb(n,r,l),n&&o&&r&&a&&l){const e=n.getBoundingClientRect(),t=o.getBoundingClientRect(),i=r.getBoundingClientRect(),s=a.getBoundingClientRect(),d=l.getBoundingClientRect();if(s.right+16>d.left){d.left-16-s.width>e.left?a.style.right="".concat(d.width+16-(i.right-t.right),"px"):l.style.left="".concat(s.width+16-(i.left-t.left),"px")}}}render(){var e;const{disabled:t,element:i,theme:s,width:n,widgetMgr:r}=this.props,{colors:a,fonts:l,fontSizes:d,spacing:m}=s,u={width:n};return this.formClearHelper.manageFormClearListener(r,i.formId,this.onFormCleared),this.thumbValueAlignment(),(0,x.jsxs)("div",{ref:this.sliderRef,className:"stSlider",style:u,children:[(0,x.jsx)(c.O,{label:i.label,disabled:t,labelVisibility:(0,h.iF)(null===(e=i.labelVisibility)||void 0===e?void 0:e.value),children:i.help&&(0,x.jsx)(p.dT,{children:(0,x.jsx)(g.Z,{content:i.help,placement:f.u.TOP_RIGHT})})}),(0,x.jsx)(o.Z,{min:i.min,max:i.max,step:i.step,value:this.value,onChange:this.handleChange,disabled:t,overrides:{Root:{style:{paddingTop:m.twoThirdsSmFont}},Thumb:this.renderThumb,Tick:{style:{fontFamily:l.monospace,fontSize:d.sm}},Track:{style:{backgroundColor:"none !important",paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:m.twoThirdsSmFont}},InnerTrack:{style:e=>{let{$disabled:t}=e;return{height:"4px",...t?{background:a.darkenedBgMix25}:{}}}},TickBar:this.renderTickBar}})]})}}const w=(0,r.b)(C)}}]);