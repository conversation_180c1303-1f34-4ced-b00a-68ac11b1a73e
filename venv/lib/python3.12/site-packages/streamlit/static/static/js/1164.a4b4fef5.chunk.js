"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[1164],{49839:(e,t,n)=>{n.r(t),n.d(t,{default:()=>vt});var i=n(66845),o=n(35396),l=n(17330),a=n(57463),r=n(97943),s=n(41342),d=n(17875),c=n(87814),u=n(62622),m=n(16295),g=n(50641),h=n(25621),p=n(34367),f=n(31011),b=n(21e3),v=n(68411),y=n(9003),w=n(81354),x=n(46927),C=n(66694),E=n(1515),M=n(27466);const T=(0,E.Z)("div",{target:"e2wxzia1"})((e=>{let{theme:t,locked:n,target:i}=e;return{padding:"0.5rem 0 0.5rem 0.5rem",position:"absolute",top:n?"-2.4rem":"-1rem",right:t.spacing.none,transition:"none",...!n&&{opacity:0,"&:active, &:focus-visible, &:hover":{transition:"opacity 150ms 100ms, top 100ms 100ms",opacity:1,top:"-2.4rem"},...i&&{["".concat(i,":hover &, ").concat(i,":active &, ").concat(i,":focus-visible &")]:{transition:"opacity 150ms 100ms, top 100ms 100ms",opacity:1,top:"-2.4rem"}}}}}),""),k=(0,E.Z)("div",{target:"e2wxzia0"})((e=>{let{theme:t}=e;return{color:(0,M.Iy)(t)?t.colors.fadedText60:t.colors.bodyText,display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-end",boxShadow:"1px 2px 8px rgba(0, 0, 0, 0.08)",borderRadius:t.radii.lg,backgroundColor:t.colors.lightenedBg05,width:"fit-content",zIndex:t.zIndices.sidebar+1}}),"");var N=n(40864);function R(e){let{label:t,show_label:n,icon:i,onClick:o}=e;const l=(0,h.u)(),a=n?t:"";return(0,N.jsx)("div",{"data-testid":"stElementToolbarButton",children:(0,N.jsx)(v.Z,{content:(0,N.jsx)(b.ZP,{source:t,allowHTML:!1,style:{fontSize:l.fontSizes.sm}}),placement:v.u.TOP,onMouseEnterDelay:1e3,inline:!0,children:(0,N.jsxs)(y.ZP,{onClick:e=>{o&&o(),e.stopPropagation()},kind:w.nW.ELEMENT_TOOLBAR,children:[i&&(0,N.jsx)(x.Z,{content:i,size:"md",testid:"stElementToolbarButtonIcon"}),a&&(0,N.jsx)("span",{children:a})]})})})}const S=e=>{let{onExpand:t,onCollapse:n,isFullScreen:o,locked:l,children:a,target:r}=e;const{libConfig:s}=i.useContext(C.E);return(0,N.jsx)(T,{className:"stElementToolbar","data-testid":"stElementToolbar",locked:l||o,target:r,children:(0,N.jsxs)(k,{children:[a,t&&!s.disableFullscreenMode&&!o&&(0,N.jsx)(R,{label:"Fullscreen",icon:p.i,onClick:()=>t()}),n&&!s.disableFullscreenMode&&o&&(0,N.jsx)(R,{label:"Close fullscreen",icon:f.m,onClick:()=>n()})]})})};var _=n(72706),O=n(29724),I=n.n(O),D=n(52347),A=n(53608),H=n.n(A);n(87717),n(55842);const V=["true","t","yes","y","on","1"],z=["false","f","no","n","off","0"];function F(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e="\u26a0\ufe0f ".concat(e),{kind:o.p6.Text,readonly:!0,allowOverlay:!0,data:e+(t?"\n\n".concat(t,"\n"):""),displayData:e,isError:!0}}function j(e){return e.hasOwnProperty("isError")&&e.isError}function L(e){return e.hasOwnProperty("isMissingValue")&&e.isMissingValue}function W(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?{kind:o.p6.Loading,allowOverlay:!1,isMissingValue:!0}:{kind:o.p6.Loading,allowOverlay:!1}}function B(e,t){const n=t?"faded":"normal";return{kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,readonly:e,style:n}}function Y(e){return{id:e.id,title:e.title,hasMenu:!1,themeOverride:e.themeOverride,icon:e.icon,...e.isStretched&&{grow:e.isIndex?1:3},...e.width&&{width:e.width}}}function P(e,t){return(0,g.le)(e)?t||{}:(0,g.le)(t)?e||{}:(0,_.merge)(e,t)}function Z(e){if((0,g.le)(e))return[];if("number"===typeof e||"boolean"===typeof e)return[e];if("string"===typeof e){if(""===e)return[];if(!e.trim().startsWith("[")||!e.trim().endsWith("]"))return e.split(",");try{return JSON.parse(e)}catch(t){return[e]}}try{const t=JSON.parse(JSON.stringify(e,((e,t)=>"bigint"===typeof t?Number(t):t)));return(0,_.isArray)(t)?t.map((e=>["string","number","boolean","null"].includes(typeof e)?e:q(e))):[q(t)]}catch(t){return[q(e)]}}function q(e){try{try{return(0,_.toString)(e)}catch(t){return JSON.stringify(e,((e,t)=>"bigint"===typeof t?Number(t):t))}}catch(t){return"[".concat(typeof e,"]")}}function J(e){if((0,g.le)(e))return null;if("boolean"===typeof e)return e;const t=q(e).toLowerCase().trim();return""===t?null:!!V.includes(t)||!z.includes(t)&&void 0}function K(e){if((0,g.le)(e))return null;if((0,_.isArray)(e))return NaN;if("string"===typeof e){if(0===e.trim().length)return null;try{const t=I().unformat(e.trim());if((0,g.bb)(t))return t}catch(t){}}else if(e instanceof Int32Array)return Number(e[0]);return Number(e)}function G(e,t,n){return Number.isNaN(e)||!Number.isFinite(e)?"":(0,g.le)(t)||""===t?(0===n&&(e=Math.round(e)),I()(e).format((0,g.bb)(n)?"0,0.".concat("0".repeat(n)):"0,0.[0000]")):"percent"===t?new Intl.NumberFormat(void 0,{style:"percent",minimumFractionDigits:2,maximumFractionDigits:2}).format(e):["compact","scientific","engineering"].includes(t)?new Intl.NumberFormat(void 0,{notation:t}).format(e):"duration[ns]"===t?H().duration(e/1e6,"milliseconds").humanize():(0,D.sprintf)(t,e)}function U(e,t){return"locale"===t?new Intl.DateTimeFormat(void 0,{dateStyle:"medium",timeStyle:"medium"}).format(e.toDate()):"distance"===t?e.fromNow():"relative"===t?e.calendar():e.format(t)}function X(e){if((0,g.le)(e))return null;if(e instanceof Date)return isNaN(e.getTime())?void 0:e;if("string"===typeof e&&0===e.trim().length)return null;try{const t=Number(e);if(!isNaN(t)){let e=t;t>=10**18?e=t/1e3**3:t>=10**15?e=t/1e6:t>=10**12&&(e=t/1e3);const n=H().unix(e).utc();if(n.isValid())return n.toDate()}if("string"===typeof e){const t=H().utc(e);if(t.isValid())return t.toDate();const n=H().utc(e,[H().HTML5_FMT.TIME_MS,H().HTML5_FMT.TIME_SECONDS,H().HTML5_FMT.TIME]);if(n.isValid())return n.toDate()}}catch(t){return}}function Q(e){if(e%1===0)return 0;let t=e.toString();return-1!==t.indexOf("e")&&(t=e.toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20})),-1===t.indexOf(".")?0:t.split(".")[1].length}const $=new RegExp(/(\r\n|\n|\r)/gm);function ee(e){return-1!==e.indexOf("\n")?e.replace($," "):e}var te=n(23849),ne=n(72789);function ie(e){const t={kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,contentAlignment:e.contentAlignment,readonly:!0,style:e.isIndex?"faded":"normal"};return{...e,kind:"object",sortMode:"default",isEditable:!1,getCell(e){try{const n=(0,g.bb)(e)?q(e):null,i=(0,g.bb)(n)?ee(n):"";return{...t,data:n,displayData:i,isMissingValue:(0,g.le)(e)}}catch(n){return F(q(e),"The value cannot be interpreted as a string. Error: ".concat(n))}},getCellValue:e=>void 0===e.data?null:e.data}}ie.isEditableType=!1;const oe=ie;function le(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(a){n="Invalid validate regex: ".concat(t.validate,".\nError: ").concat(a)}const i={kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,contentAlignment:e.contentAlignment,readonly:!e.isEditable,style:e.isIndex?"faded":"normal"},l=i=>{if((0,g.le)(i))return!e.isRequired;let o=q(i),l=!1;return t.max_chars&&o.length>t.max_chars&&(o=o.slice(0,t.max_chars),l=!0),!(n instanceof RegExp&&!1===n.test(o))&&(!l||o)};return{...e,kind:"text",sortMode:"default",validateInput:l,getCell(e,t){if("string"===typeof n)return F(q(e),n);if(t){const t=l(e);if(!1===t)return F(q(e),"Invalid input.");"string"===typeof t&&(e=t)}try{const t=(0,g.bb)(e)?q(e):null,n=(0,g.bb)(t)?ee(t):"";return{...i,isMissingValue:(0,g.le)(t),data:t,displayData:n}}catch(a){return F("Incompatible value","The value cannot be interpreted as string. Error: ".concat(a))}},getCellValue:e=>void 0===e.data?null:e.data}}le.isEditableType=!0;const ae=le;function re(e,t){return e=t.startsWith("+")||t.startsWith("-")?e.utcOffset(t,!1):e.tz(t)}function se(e,t,n,i,l,a,r){var s;const d=P({format:n,step:i,timezone:r},t.columnTypeOptions);let c,u,m;if((0,g.bb)(d.timezone))try{var h;c=(null===(h=re(H()(),d.timezone))||void 0===h?void 0:h.utcOffset())||void 0}catch(b){}(0,g.bb)(d.min_value)&&(u=X(d.min_value)||void 0),(0,g.bb)(d.max_value)&&(m=X(d.max_value)||void 0);const p={kind:o.p6.Custom,allowOverlay:!0,copyData:"",readonly:!t.isEditable,contentAlign:t.contentAlignment,style:t.isIndex?"faded":"normal",data:{kind:"date-picker-cell",date:void 0,displayDate:"",step:(null===(s=d.step)||void 0===s?void 0:s.toString())||"1",format:l,min:u,max:m}},f=e=>{const n=X(e);return null===n?!t.isRequired:void 0!==n&&(!((0,g.bb)(u)&&a(n)<a(u))&&!((0,g.bb)(m)&&a(n)>a(m)))};return{...t,kind:e,sortMode:"default",validateInput:f,getCell(e,t){if(!0===t){const t=f(e);if(!1===t)return F(q(e),"Invalid input.");t instanceof Date&&(e=t)}const i=X(e);let o="",l="",a=c;if(void 0===i)return F(q(e),"The value cannot be interpreted as a datetime object.");if(null!==i){let e=H().utc(i);if(!e.isValid())return F(q(i),"This should never happen. Please report this bug. \nError: ".concat(e.toString()));if(d.timezone){try{e=re(e,d.timezone)}catch(b){return F(e.toISOString(),"Failed to adjust to the provided timezone: ".concat(d.timezone,". \nError: ").concat(b))}a=e.utcOffset()}try{l=U(e,d.format||n)}catch(b){return F(e.toISOString(),"Failed to format the date for rendering with: ".concat(d.format,". \nError: ").concat(b))}o=U(e,n)}return{...p,copyData:o,isMissingValue:(0,g.le)(i),data:{...p.data,date:i,displayDate:l,timezoneOffset:a}}},getCellValue(e){var t;return(0,g.le)(null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.date)?null:a(e.data.date)}}}function de(e){var t,n,i,o,l;let a="YYYY-MM-DD HH:mm:ss";(null===(t=e.columnTypeOptions)||void 0===t?void 0:t.step)>=60?a="YYYY-MM-DD HH:mm":(null===(n=e.columnTypeOptions)||void 0===n?void 0:n.step)<1&&(a="YYYY-MM-DD HH:mm:ss.SSS");const r=null===(i=e.arrowType)||void 0===i||null===(o=i.meta)||void 0===o?void 0:o.timezone,s=(0,g.bb)(r)||(0,g.bb)(null===e||void 0===e||null===(l=e.columnTypeOptions)||void 0===l?void 0:l.timezone);return se("datetime",e,s?a+"Z":a,1,"datetime-local",(e=>s?e.toISOString():e.toISOString().replace("Z","")),r)}function ce(e){var t,n;let i="HH:mm:ss";return(null===(t=e.columnTypeOptions)||void 0===t?void 0:t.step)>=60?i="HH:mm":(null===(n=e.columnTypeOptions)||void 0===n?void 0:n.step)<1&&(i="HH:mm:ss.SSS"),se("time",e,i,1,"time",(e=>e.toISOString().split("T")[1].replace("Z","")))}function ue(e){return se("date",e,"YYYY-MM-DD",1,"date",(e=>e.toISOString().split("T")[0]))}function me(e){const t={kind:o.p6.Boolean,data:!1,allowOverlay:!1,contentAlign:e.contentAlignment,readonly:!e.isEditable,style:e.isIndex?"faded":"normal"};return{...e,kind:"checkbox",sortMode:"default",getCell(e){let n=null;return n=J(e),void 0===n?F(q(e),"The value cannot be interpreted as boolean."):{...t,data:n,isMissingValue:(0,g.le)(n)}},getCellValue:e=>void 0===e.data?null:e.data}}de.isEditableType=!0,ce.isEditableType=!0,ue.isEditableType=!0,me.isEditableType=!0;const ge=me;function he(e){return e.startsWith("int")&&!e.startsWith("interval")||"range"===e||e.startsWith("uint")}function pe(e){const t=ne.fu.getTypeName(e.arrowType),n=P({step:he(t)?1:void 0,min_value:t.startsWith("uint")?0:void 0,format:"timedelta64[ns]"===t?"duration[ns]":void 0},e.columnTypeOptions),i=(0,g.le)(n.min_value)||n.min_value<0,l=(0,g.bb)(n.step)&&!Number.isNaN(n.step)?Q(n.step):void 0,a={kind:o.p6.Number,data:void 0,displayData:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment||"right",style:e.isIndex?"faded":"normal",allowNegative:i,fixedDecimals:l},r=t=>{let i=K(t);if((0,g.le)(i))return!e.isRequired;if(Number.isNaN(i))return!1;let o=!1;return(0,g.bb)(n.max_value)&&i>n.max_value&&(i=n.max_value,o=!0),!((0,g.bb)(n.min_value)&&i<n.min_value)&&(!o||i)};return{...e,kind:"number",sortMode:"smart",validateInput:r,getCell(e,t){if(!0===t){const t=r(e);if(!1===t)return F(q(e),"Invalid input.");"number"===typeof t&&(e=t)}let i=K(e),o="";if((0,g.bb)(i)){if(Number.isNaN(i))return F(q(e),"The value cannot be interpreted as a number.");if((0,g.bb)(l)&&(s=i,i=0===(d=l)?Math.trunc(s):Math.trunc(s*10**d)/10**d),Number.isInteger(i)&&!Number.isSafeInteger(i))return F(q(e),"The value is larger than the maximum supported integer values in number columns (2^53).");try{o=G(i,n.format,l)}catch(c){return F(q(i),(0,g.bb)(n.format)?"Failed to format the number based on the provided format configuration: (".concat(n.format,"). Error: ").concat(c):"Failed to format the number. Error: ".concat(c))}}var s,d;return{...a,data:i,displayData:o,isMissingValue:(0,g.le)(i)}},getCellValue:e=>void 0===e.data?null:e.data}}pe.isEditableType=!0;const fe=pe;function be(e){let t="string";const n=P({options:"bool"===ne.fu.getTypeName(e.arrowType)?[!0,!1]:[]},e.columnTypeOptions),i=new Set(n.options.map((e=>typeof e)));1===i.size&&(i.has("number")||i.has("bigint")?t="number":i.has("boolean")&&(t="boolean"));const l={kind:o.p6.Custom,allowOverlay:!0,copyData:"",contentAlign:e.contentAlignment,readonly:!e.isEditable,data:{kind:"dropdown-cell",allowedValues:[...!0!==e.isRequired?[null]:[],...n.options.filter((e=>null!==e&&""!==e)).map((e=>q(e)))],value:"",readonly:!e.isEditable}};return{...e,kind:"selectbox",sortMode:"default",getCell(e,t){let n=null;return(0,g.bb)(e)&&""!==e&&(n=q(e)),t&&!l.data.allowedValues.includes(n)?F(q(n),"The value is not part of the allowed options."):{...l,isMissingValue:null===n,copyData:n||"",data:{...l.data,value:n}}},getCellValue(e){var n,i,o,l,a,r,s;return(0,g.le)(null===(n=e.data)||void 0===n?void 0:n.value)||""===(null===(i=e.data)||void 0===i?void 0:i.value)?null:"number"===t?null!==(l=K(null===(a=e.data)||void 0===a?void 0:a.value))&&void 0!==l?l:null:"boolean"===t?null!==(r=J(null===(s=e.data)||void 0===s?void 0:s.value))&&void 0!==r?r:null:null===(o=e.data)||void 0===o?void 0:o.value}}}be.isEditableType=!0;const ve=be;function ye(e){const t={kind:o.p6.Bubble,data:[],allowOverlay:!0,contentAlign:e.contentAlignment,style:e.isIndex?"faded":"normal"};return{...e,kind:"list",sortMode:"default",isEditable:!1,getCell(e){const n=(0,g.le)(e)?[]:Z(e);return{...t,data:n,isMissingValue:(0,g.le)(e),copyData:(0,g.le)(e)?"":q(n.map((e=>(0,_.isString)(e)&&e.includes(",")?e.replace(/,/g," "):e)))}},getCellValue:e=>(0,g.le)(e.data)||L(e)?null:e.data}}ye.isEditableType=!1;const we=ye;function xe(e,t,n){const i=new RegExp("".concat(e,"[,\\s].*{(?:[^}]*[\\s;]{1})?").concat(t,":\\s*([^;}]+)[;]?.*}"),"gm");n=n.replace(/{/g," {");const o=i.exec(n);if(o)return o[1].trim()}function Ce(e,t){const n=e.types.index[t],i=e.indexNames[t];let o=!0;return"range"===ne.fu.getTypeName(n)&&(o=!1),{id:"index-".concat(t),name:i,title:i,isEditable:o,arrowType:n,isIndex:!0,isHidden:!1}}function Ee(e,t){const n=e.columns[0][t];let i,o=e.types.data[t];if((0,g.le)(o)&&(o={meta:null,numpy_type:"object",pandas_type:"object"}),"categorical"===ne.fu.getTypeName(o)){const n=e.getCategoricalOptions(t);(0,g.bb)(n)&&(i={options:n})}return{id:"column-".concat(n,"-").concat(t),name:n,title:n,isEditable:!0,arrowType:o,columnTypeOptions:i,isIndex:!1,isHidden:!1}}function Me(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const i=e.arrowType?ne.fu.getTypeName(e.arrowType):null;let l;if("object"===e.kind)l=e.getCell((0,g.bb)(t.content)?ee(ne.fu.format(t.content,t.contentType,t.field)):null);else if(["time","date","datetime"].includes(e.kind)&&(0,g.bb)(t.content)&&("number"===typeof t.content||"bigint"===typeof t.content)){var a,r;let n;var s,d,c;if("time"===i&&(0,g.bb)(null===(a=t.field)||void 0===a||null===(r=a.type)||void 0===r?void 0:r.unit))n=H().unix(ne.fu.convertToSeconds(t.content,null!==(s=null===(d=t.field)||void 0===d||null===(c=d.type)||void 0===c?void 0:c.unit)&&void 0!==s?s:0)).utc().toDate();else n=H().utc(Number(t.content)).toDate();l=e.getCell(n)}else if("decimal"===i){const n=(0,g.le)(t.content)?null:ne.fu.format(t.content,t.contentType,t.field);l=e.getCell(n)}else l=e.getCell(t.content);if(j(l))return l;if(!e.isEditable){if((0,g.bb)(t.displayContent)){var u;const e=ee(t.displayContent);l.kind===o.p6.Text||l.kind===o.p6.Number?l={...l,displayData:e}:l.kind===o.p6.Custom&&"date-picker-cell"===(null===(u=l.data)||void 0===u?void 0:u.kind)&&(l={...l,data:{...l.data,displayDate:e}})}n&&t.cssId&&(l=function(e,t,n){const i={},o=xe(t,"color",n);o&&(i.textDark=o);const l=xe(t,"background-color",n);return l&&(i.bgCell=l),"yellow"===l&&void 0===o&&(i.textDark="#31333F"),i?{...e,themeOverride:i}:e}(l,t.cssId,n))}return l}function Te(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(a){n="Invalid validate regex: ".concat(t.validate,".\nError: ").concat(a)}const i={kind:o.p6.Uri,data:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment,style:e.isIndex?"faded":"normal"},l=i=>{if((0,g.le)(i))return!e.isRequired;let o=q(i),l=!1;return t.max_chars&&o.length>t.max_chars&&(o=o.slice(0,t.max_chars),l=!0),!(n instanceof RegExp&&!1===n.test(o))&&(!l||o)};return{...e,kind:"link",sortMode:"default",validateInput:l,getCell(e,t){if("string"===typeof n)return F(q(e),n);if(t){const t=l(e);if(!1===t)return F(q(e),"Invalid input.");"string"===typeof t&&(e=t)}return{...i,data:(0,g.bb)(e)?q(e):null,isMissingValue:(0,g.le)(e)}},getCellValue:e=>void 0===e.data?null:e.data}}Te.isEditableType=!0;const ke=Te;function Ne(e){const t={kind:o.p6.Image,data:[],displayData:[],allowAdd:!1,allowOverlay:!0,contentAlign:e.contentAlignment||"center",style:e.isIndex?"faded":"normal"};return{...e,kind:"image",sortMode:"default",isEditable:!1,getCell(e){const n=(0,g.bb)(e)?[q(e)]:[];return{...t,data:n,isMissingValue:!(0,g.bb)(e),displayData:n}},getCellValue:e=>void 0===e.data||0===e.data.length?null:e.data[0]}}Ne.isEditableType=!1;const Re=Ne;function Se(e){const t=he(ne.fu.getTypeName(e.arrowType)),n=P({min_value:0,max_value:t?100:1,step:t?1:.01,format:t?"%3d%%":"percent"},e.columnTypeOptions);let i;try{i=G(n.max_value,n.format)}catch(r){i=q(n.max_value)}const l=(0,g.le)(n.step)||Number.isNaN(n.step)?void 0:Q(n.step),a={kind:o.p6.Custom,allowOverlay:!1,copyData:"",contentAlign:e.contentAlignment,data:{kind:"range-cell",min:n.min_value,max:n.max_value,step:n.step,value:n.min_value,label:String(n.min_value),measureLabel:i,readonly:!0}};return{...e,kind:"progress",sortMode:"smart",isEditable:!1,getCell(e){if((0,g.le)(e))return W();if((0,g.le)(n.min_value)||(0,g.le)(n.max_value)||Number.isNaN(n.min_value)||Number.isNaN(n.max_value)||n.min_value>=n.max_value)return F("Invalid min/max parameters","The min_value (".concat(n.min_value,") and max_value (").concat(n.max_value,") parameters must be valid numbers."));if((0,g.le)(n.step)||Number.isNaN(n.step))return F("Invalid step parameter","The step parameter (".concat(n.step,") must be a valid number."));const t=K(e);if(Number.isNaN(t)||(0,g.le)(t))return F(q(e),"The value cannot be interpreted as a number.");if(Number.isInteger(t)&&!Number.isSafeInteger(t))return F(q(e),"The value is larger than the maximum supported integer values in number columns (2^53).");let i="";try{i=G(t,n.format,l)}catch(r){return F(q(t),(0,g.bb)(n.format)?"Failed to format the number based on the provided format configuration: (".concat(n.format,"). Error: ").concat(r):"Failed to format the number. Error: ".concat(r))}const o=Math.min(n.max_value,Math.max(n.min_value,t));return{...a,isMissingValue:(0,g.le)(e),copyData:String(t),data:{...a.data,value:o,label:i}}},getCellValue(e){var t,n;return e.kind===o.p6.Loading||void 0===(null===(t=e.data)||void 0===t?void 0:t.value)?null:null===(n=e.data)||void 0===n?void 0:n.value}}}Se.isEditableType=!1;const _e=Se;function Oe(e,t,n){const i=P({y_min:0,y_max:1},t.columnTypeOptions),l={kind:o.p6.Custom,allowOverlay:!1,copyData:"",contentAlign:t.contentAlignment,data:{kind:"sparkline-cell",values:[],displayValues:[],graphKind:n,yAxis:[i.y_min,i.y_max]}};return{...t,kind:e,sortMode:"default",isEditable:!1,getCell(e){if((0,g.le)(i.y_min)||(0,g.le)(i.y_max)||Number.isNaN(i.y_min)||Number.isNaN(i.y_max)||i.y_min>=i.y_max)return F("Invalid min/max y-axis configuration","The y_min (".concat(i.y_min,") and y_max (").concat(i.y_max,") configuration options must be valid numbers."));if((0,g.le)(e))return W();const t=Z(e),o=[];let a=[];if(0===t.length)return W();let r=Number.MIN_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER;for(let n=0;n<t.length;n++){const e=K(t[n]);if(Number.isNaN(e)||(0,g.le)(e))return F(q(t),"The value cannot be interpreted as a numeric array. ".concat(q(e)," is not a number."));e>r&&(r=e),e<s&&(s=e),o.push(e)}return"line"===n&&o.length<=2?W():(a=o.length>0&&(r>i.y_max||s<i.y_min)?o.map((e=>r-s===0?r>(i.y_max||1)?i.y_max||1:i.y_min||0:((i.y_max||1)-(i.y_min||0))*((e-s)/(r-s))+(i.y_min||0))):o,{...l,copyData:o.join(","),data:{...l.data,values:a,displayValues:o.map((e=>G(e)))},isMissingValue:(0,g.le)(e)})},getCellValue(e){var t,n;return e.kind===o.p6.Loading||void 0===(null===(t=e.data)||void 0===t?void 0:t.values)?null:null===(n=e.data)||void 0===n?void 0:n.values}}}function Ie(e){return Oe("line_chart",e,"line")}function De(e){return Oe("bar_chart",e,"bar")}Ie.isEditableType=!1,De.isEditableType=!1;const Ae=new Map(Object.entries({object:oe,text:ae,checkbox:ge,selectbox:ve,list:we,number:fe,link:ke,datetime:de,date:ue,time:ce,line_chart:Ie,bar_chart:De,image:Re,progress:_e})),He=[],Ve="_index",ze="_pos:",Fe={small:75,medium:200,large:400};function je(e){if(!(0,g.le)(e))return"number"===typeof e?e:e in Fe?Fe[e]:void 0}function Le(e,t){if(!t)return e;let n;return t.has(e.name)&&e.name!==Ve?n=t.get(e.name):t.has("".concat(ze).concat(e.indexNumber))?n=t.get("".concat(ze).concat(e.indexNumber)):e.isIndex&&t.has(Ve)&&(n=t.get(Ve)),n?(0,_.merge)({...e},{title:n.label,width:je(n.width),isEditable:(0,g.bb)(n.disabled)?!n.disabled:void 0,isHidden:n.hidden,isRequired:n.required,columnTypeOptions:n.type_config,contentAlignment:n.alignment,defaultValue:n.default,help:n.help}):e}function We(e){var t;const n=null===(t=e.columnTypeOptions)||void 0===t?void 0:t.type;let i;return(0,g.bb)(n)&&(Ae.has(n)?i=Ae.get(n):(0,te.KE)("Unknown column type configured in column configuration: ".concat(n))),(0,g.le)(i)&&(i=function(e){let t=e?ne.fu.getTypeName(e):null;return t?(t=t.toLowerCase().trim(),["unicode","empty"].includes(t)?ae:["datetime","datetimetz"].includes(t)?de:"time"===t?ce:"date"===t?ue:["object","bytes"].includes(t)?oe:["bool"].includes(t)?ge:["int8","int16","int32","int64","uint8","uint16","uint32","uint64","float16","float32","float64","float96","float128","range","decimal"].includes(t)?fe:"categorical"===t?ve:t.startsWith("list")?we:oe):oe}(e.arrowType)),i}const Be=function(e,t,n){const o=i.useMemo((()=>function(e){if(!e)return new Map;try{return new Map(Object.entries(JSON.parse(e)))}catch(t){return(0,te.H)(t),new Map}}(e.columns)),[e.columns]),l=e.useContainerWidth||(0,g.bb)(e.width)&&e.width>0;return{columns:i.useMemo((()=>{let i=function(e){var t,n,i,o,l,a;const r=[],s=null!==(t=null===(n=e.types)||void 0===n||null===(i=n.index)||void 0===i?void 0:i.length)&&void 0!==t?t:0,d=null!==(o=null===(l=e.columns)||void 0===l||null===(a=l[0])||void 0===a?void 0:a.length)&&void 0!==o?o:0;if(0===s&&0===d)return r.push({id:"empty-index",title:"",indexNumber:0,isEditable:!1,isIndex:!0}),r;for(let c=0;c<s;c++){const t={...Ce(e,c),indexNumber:c};r.push(t)}for(let c=0;c<d;c++){const t={...Ee(e,c),indexNumber:c+s};r.push(t)}return r}(t).map((t=>{let i={...t,...Le(t,o),isStretched:l};const a=We(i);return(e.editingMode===m.Eh.EditingMode.READ_ONLY||n||!1===a.isEditableType)&&(i={...i,isEditable:!1}),e.editingMode!==m.Eh.EditingMode.READ_ONLY&&1==i.isEditable&&(i={...i,icon:"editable"}),a(i)})).filter((e=>!e.isHidden));if(e.columnOrder&&e.columnOrder.length>0){const t=[];i.forEach((e=>{e.isIndex&&t.push(e)})),e.columnOrder.forEach((e=>{const n=i.find((t=>t.name===e));n&&!n.isIndex&&t.push(n)})),i=t}return i.length>0?i:[oe({id:"empty-index",title:"",indexNumber:0,isEditable:!1,isIndex:!0})]}),[t,o,l,n,e.editingMode,e.columnOrder])}};function Ye(e){return e.isIndex?Ve:(0,g.le)(e.name)?"":e.name}const Pe=class{constructor(e){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[],this.numRows=0,this.numRows=e}toJson(e){const t=new Map;e.forEach((e=>{t.set(e.indexNumber,e)}));const n={edited_rows:{},added_rows:[],deleted_rows:[]};this.editedCells.forEach(((e,i,o)=>{const l={};e.forEach(((e,n,i)=>{const o=t.get(n);o&&(l[Ye(o)]=o.getCellValue(e))})),n.edited_rows[i]=l})),this.addedRows.forEach((e=>{const i={};let o=!1;e.forEach(((e,n,l)=>{const a=t.get(n);if(a){const t=a.getCellValue(e);a.isRequired&&a.isEditable&&L(e)&&(o=!0),(0,g.bb)(t)&&(i[Ye(a)]=t)}})),o||n.added_rows.push(i)})),n.deleted_rows=this.deletedRows;return JSON.stringify(n,((e,t)=>void 0===t?null:t))}fromJson(e,t){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[];const n=JSON.parse(e),i=new Map;t.forEach((e=>{i.set(e.indexNumber,e)}));const o=new Map;t.forEach((e=>{o.set(Ye(e),e)})),Object.keys(n.edited_rows).forEach((e=>{const t=Number(e),i=n.edited_rows[e];Object.keys(i).forEach((e=>{const n=i[e],l=o.get(e);if(l){const e=l.getCell(n);var a;if(e)this.editedCells.has(t)||this.editedCells.set(t,new Map),null===(a=this.editedCells.get(t))||void 0===a||a.set(l.indexNumber,e)}}))})),n.added_rows.forEach((e=>{const t=new Map;Object.keys(e).forEach((n=>{const i=e[n],l=o.get(n);if(l){const e=l.getCell(i);e&&t.set(l.indexNumber,e)}})),this.addedRows.push(t)})),this.deletedRows=n.deleted_rows}isAddedRow(e){return e>=this.numRows}getCell(e,t){if(this.isAddedRow(t))return this.addedRows[t-this.numRows].get(e);const n=this.editedCells.get(t);return void 0!==n?n.get(e):void 0}setCell(e,t,n){if(this.isAddedRow(t)){if(t-this.numRows>=this.addedRows.length)return;this.addedRows[t-this.numRows].set(e,n)}else{void 0===this.editedCells.get(t)&&this.editedCells.set(t,new Map);this.editedCells.get(t).set(e,n)}}addRow(e){this.addedRows.push(e)}deleteRows(e){e.sort(((e,t)=>t-e)).forEach((e=>{this.deleteRow(e)}))}deleteRow(e){(0,g.le)(e)||e<0||(this.isAddedRow(e)?this.addedRows.splice(e-this.numRows,1):(this.deletedRows.includes(e)||(this.deletedRows.push(e),this.deletedRows=this.deletedRows.sort(((e,t)=>e-t))),this.editedCells.delete(e)))}getOriginalRowIndex(e){let t=e;for(let n=0;n<this.deletedRows.length&&!(this.deletedRows[n]>t);n++)t+=1;return t}getNumRows(){return this.numRows+this.addedRows.length-this.deletedRows.length}};var Ze=n(35704);const qe=function(){const e=(0,h.u)(),t=i.useMemo((()=>({editable:e=>'<svg xmlns="http://www.w3.org/2000/svg" height="40" viewBox="0 96 960 960" width="40" fill="'.concat(e.bgColor,'"><path d="m800.641 679.743-64.384-64.384 29-29q7.156-6.948 17.642-6.948 10.485 0 17.742 6.948l29 29q6.948 7.464 6.948 17.95 0 10.486-6.948 17.434l-29 29Zm-310.64 246.256v-64.383l210.82-210.821 64.384 64.384-210.821 210.82h-64.383Zm-360-204.872v-50.254h289.743v50.254H130.001Zm0-162.564v-50.255h454.615v50.255H130.001Zm0-162.307v-50.255h454.615v50.255H130.001Z"/></svg>')})),[]);return{accentColor:e.colors.primary,accentFg:e.colors.white,accentLight:(0,Ze.DZ)(e.colors.primary,.9),borderColor:e.colors.fadedText05,horizontalBorderColor:e.colors.fadedText05,fontFamily:e.genericFonts.bodyFont,bgSearchResult:(0,Ze.DZ)(e.colors.primary,.9),bgIconHeader:e.colors.fadedText60,fgIconHeader:e.colors.white,bgHeader:e.colors.bgMix,bgHeaderHasFocus:e.colors.secondaryBg,bgHeaderHovered:e.colors.secondaryBg,textHeader:e.colors.fadedText60,textHeaderSelected:e.colors.white,textGroupHeader:e.colors.fadedText60,headerFontStyle:"".concat(e.fontSizes.sm),baseFontStyle:e.fontSizes.sm,editorFontSize:e.fontSizes.sm,textDark:e.colors.bodyText,textMedium:(0,Ze.DZ)(e.colors.bodyText,.2),textLight:e.colors.fadedText40,textBubble:e.colors.fadedText60,bgCell:e.colors.bgColor,bgCellMedium:e.colors.bgColor,cellHorizontalPadding:8,cellVerticalPadding:3,bgBubble:e.colors.secondaryBg,bgBubbleSelected:e.colors.secondaryBg,linkColor:e.colors.linkText,drilldownBorder:e.colors.darkenedBgMix25,tableBorderRadius:e.radii.lg,headerIcons:t}};const Je=function(e,t,n,o){return{getCellContent:i.useCallback((i=>{let[l,a]=i;if(l>t.length-1)return F("Column index out of bounds.","This should never happen. Please report this bug.");if(a>n-1)return F("Row index out of bounds.","This should never happen. Please report this bug.");const r=t[l],s=r.indexNumber,d=o.current.getOriginalRowIndex(a);if(r.isEditable||o.current.isAddedRow(d)){const e=o.current.getCell(s,d);if(void 0!==e)return e}try{return Me(r,e.getCell(d+1,s),e.cssStyles)}catch(c){return(0,te.H)(c),F("Error during cell creation.","This should never happen. Please report this bug. \nError: ".concat(c))}}),[t,n,e,o])}};var Ke=n(37753);const Ge=function(e,t,n){const[o,l]=i.useState(),{getCellContent:a,getOriginalIndex:r}=(0,Ke.fF)({columns:t.map((e=>Y(e))),getCellContent:n,rows:e,sort:o}),s=i.useMemo((()=>function(e,t){return void 0===t?e:e.map((e=>e.id===t.column.id?{...e,title:"asc"===t.direction?"\u2191 ".concat(e.title):"\u2193 ".concat(e.title)}:e))}(t,o)),[t,o]),d=i.useCallback((e=>{let t="asc";const n=s[e];if(o&&o.column.id===n.id){if("asc"!==o.direction)return void l(void 0);t="desc"}l({column:Y(n),direction:t,mode:n.sortMode})}),[o,s]);return{columns:s,sortColumn:d,getOriginalIndex:r,getCellContent:a}};var Ue=n(95345);const Xe=",",Qe='"',$e='"',et="\n";function tt(e){return e.map((e=>function(e){if((0,g.le)(e))return"";const t=q(e);if(new RegExp("[".concat([Xe,Qe,et].join(""),"]")).test(t))return"".concat(Qe).concat(t.replace(new RegExp(Qe,"g"),$e+Qe)).concat(Qe);return t}(e))).join(Xe)+et}const nt=function(e,t,n){return{exportToCsv:i.useCallback((async()=>{const i=(new Date).toISOString().slice(0,16).replace(":","-"),o="".concat(i,"_export.csv"),l=await(0,Ue.Kr)({suggestedName:o,types:[{accept:{"text/csv":[".csv"]}}],excludeAcceptAllOption:!1}),a=new TextEncoder,r=await l.createWritable();await r.write(a.encode("\ufeff"));const s=t.map((e=>e.name));await r.write(a.encode(tt(s)));for(let d=0;d<n;d++){const n=[];t.forEach(((t,i,o)=>{n.push(t.getCellValue(e([i,d])))})),await r.write(a.encode(tt(n)))}await r.close()}),[t,n,e])}};const it=function(e,t,n,o,l,a,r){const s=i.useCallback(((t,i)=>{let[a,s]=t;const d=e[a];if(!d.isEditable)return;const c=d.indexNumber,u=n.current.getOriginalRowIndex(l(s)),m=o([a,s]),g=d.getCellValue(m),h=d.getCellValue(i);if(!j(m)&&h===g)return;const p=d.getCell(h,!0);j(p)?(0,te.KE)("Not applying the cell edit since it causes this error:\n ".concat(p.data)):(n.current.setCell(c,u,{...p,lastUpdated:performance.now()}),r())}),[e,n,l,o,r]),d=i.useCallback((()=>{if(t)return;const i=new Map;e.forEach((e=>{i.set(e.indexNumber,e.getCell(e.defaultValue))})),n.current.addRow(i)}),[e,n,t]),c=i.useCallback((()=>{t||(d(),r())}),[d,r,t]),u=i.useCallback((i=>{var o;if(i.rows.length>0){if(t)return!0;const e=i.rows.toArray().map((e=>n.current.getOriginalRowIndex(l(e))));return n.current.deleteRows(e),r(!0),!1}if(null!==(o=i.current)&&void 0!==o&&o.range){const t=[],n=i.current.range;for(let i=n.y;i<n.y+n.height;i++)for(let o=n.x;o<n.x+n.width;o++){const n=e[o];n.isEditable&&!n.isRequired&&(t.push({cell:[o,i]}),s([o,i],n.getCell(null)))}return t.length>0&&(r(),a(t)),!1}return!0}),[e,n,t,a,l,r,s]),m=i.useCallback(((i,s)=>{const[c,u]=i,m=[];for(let h=0;h<s.length;h++){const i=s[h];if(h+u>=n.current.getNumRows()){if(t)break;d()}for(let t=0;t<i.length;t++){const a=i[t],r=h+u,s=t+c;if(s>=e.length)break;const d=e[s];if(d.isEditable){const e=d.getCell(a,!0);if((0,g.bb)(e)&&!j(e)){const t=d.indexNumber,i=n.current.getOriginalRowIndex(l(r)),a=d.getCellValue(o([s,r]));d.getCellValue(e)!==a&&(n.current.setCell(t,i,{...e,lastUpdated:performance.now()}),m.push({cell:[s,r]}))}}}m.length>0&&(r(),a(m))}return!1}),[e,n,t,l,o,d,r,a]),h=i.useCallback(((t,n)=>{const i=t[0];if(i>=e.length)return!0;const o=e[i];if(o.validateInput){const e=o.validateInput(o.getCellValue(n));return!0===e||!1===e?e:o.getCell(e)}return!0}),[e]);return{onCellEdited:s,onPaste:m,onRowAppended:c,onDelete:u,validateCell:h}};const ot=function(e,t){const[n,o]=i.useState(),l=i.useRef(null),a=i.useCallback((n=>{if(clearTimeout(l.current),l.current=0,o(void 0),("header"===n.kind||"cell"===n.kind)&&n.location){const i=n.location[0],a=n.location[1];let r;if(i<0||i>=e.length)return;const s=e[i];if("header"===n.kind&&(0,g.bb)(s))r=s.help;else if("cell"===n.kind){const e=t([i,a]);s.isRequired&&s.isEditable&&L(e)?r="\u26a0\ufe0f Please fill out this cell.":function(e){return e.hasOwnProperty("tooltip")&&""!==e.tooltip}(e)&&(r=e.tooltip)}r&&(l.current=setTimeout((()=>{r&&o({content:r,left:n.bounds.x+n.bounds.width/2,top:n.bounds.y})}),600))}}),[e,t,o,l]);return{tooltip:n,clearTooltip:i.useCallback((()=>{o(void 0)}),[o]),onItemHovered:a}};var lt=n(94379);const at=function(e,t){return{drawCell:i.useCallback((n=>{const{cell:i,theme:l,ctx:a,rect:r}=n,s=t?n.col-1:n.col;if(L(i)&&s<e.length){let t=!1;const i=e[s];return["checkbox","line_chart","bar_chart","progress"].includes(i.kind)||((e=>{const{cell:t,theme:n}=e;(0,o.uN)({...e,theme:{...n,textDark:n.textLight,textMedium:n.textLight},spriteManager:{},hyperWrapping:!1},"None",t.contentAlign)})(n),t=!0),i.isRequired&&i.isEditable&&function(e,t,n){e.save(),e.beginPath(),e.moveTo(t.x+t.width-8,t.y+1),e.lineTo(t.x+t.width,t.y+1),e.lineTo(t.x+t.width,t.y+1+8),e.fillStyle=n.accentColor,e.fill(),e.restore()}(a,r,l),t}return!1}),[e,t]),customRenderers:[...(0,lt.Bn)().customRenderers,...He]}};const rt=function(e){const[t,n]=(0,i.useState)((()=>new Map)),o=i.useCallback(((e,i,o,l)=>{e.id&&n(new Map(t).set(e.id,l))}),[t]);return{columns:i.useMemo((()=>e.map((e=>e.id&&t.has(e.id)&&void 0!==t.get(e.id)?{...e,width:t.get(e.id),grow:0}:e))),[e,t]),onColumnResize:o}},st=35,dt=2*st+3;const ct=function(e,t,n,o,l){let a,r=function(e){return Math.max(e*st+1+2,dt)}(t+1+(e.editingMode===m.Eh.EditingMode.DYNAMIC?1:0)),s=Math.min(r,400);e.height&&(s=Math.max(e.height,dt),r=Math.max(e.height,r)),o&&(s=Math.min(s,o),r=Math.min(r,o),e.height||(s=r));let d=n;e.useContainerWidth?a=n:e.width&&(a=Math.min(Math.max(e.width,52),n),d=Math.min(Math.max(e.width,d),n));const[c,u]=i.useState({width:a||"100%",height:s});return i.useLayoutEffect((()=>{e.useContainerWidth&&"100%"===c.width&&u({width:n,height:c.height})}),[n]),i.useLayoutEffect((()=>{u({width:c.width,height:s})}),[t]),i.useLayoutEffect((()=>{u({width:a||"100%",height:c.height})}),[a]),i.useLayoutEffect((()=>{u({width:c.width,height:s})}),[s]),i.useLayoutEffect((()=>{if(l){const t=e.useContainerWidth||(0,g.bb)(e.width)&&e.width>0;u({width:t?d:"100%",height:r})}else u({width:a||"100%",height:s})}),[l]),{minHeight:dt,maxHeight:r,minWidth:52,maxWidth:d,resizableSize:c,setResizableSize:u}},ut=(0,E.Z)("img",{target:"e24uaba0"})((()=>({maxWidth:"100%",maxHeight:"600px",objectFit:"scale-down"})),""),mt=e=>{let{urls:t}=e;const n=t&&t.length>0?t[0]:"";return n.startsWith("http")?(0,N.jsx)("a",{href:n,target:"_blank",rel:"noreferrer noopener",children:(0,N.jsx)(ut,{src:n})}):(0,N.jsx)(ut,{src:n})};var gt=n(31572),ht=n(13553),pt=n(80152);const ft=function(e){let{top:t,left:n,content:o,clearTooltip:l}=e;const[a,r]=i.useState(!0),s=(0,h.u)(),{colors:d,fontSizes:c,radii:u}=s,m=i.useCallback((()=>{r(!1),l()}),[l,r]);return(0,N.jsx)(gt.Z,{content:(0,N.jsx)(pt.Uo,{className:"stTooltipContent",children:(0,N.jsx)(b.ZP,{style:{fontSize:c.sm},source:o,allowHTML:!1})}),placement:ht.r4.top,accessibilityType:ht.SI.tooltip,showArrow:!1,popoverMargin:5,onClickOutside:m,onEsc:m,overrides:{Body:{style:{borderTopLeftRadius:u.md,borderTopRightRadius:u.md,borderBottomLeftRadius:u.md,borderBottomRightRadius:u.md,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{backgroundColor:(0,M.Iy)(s)?d.bgColor:d.secondaryBg,color:d.bodyText,fontSize:c.sm,fontWeight:"normal",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:a,children:(0,N.jsx)("div",{className:"stTooltipTarget","data-testid":"stTooltipTarget",style:{position:"fixed",top:t,left:n}})})},bt=(0,E.Z)("div",{target:"e1w7nams0"})((e=>{let{theme:t}=e;return{position:"relative",display:"inline-block","& .glideDataEditor":{height:"100%",minWidth:"100%",borderRadius:t.radii.lg},"& .dvn-scroller":{scrollbarWidth:"thin",overflowX:"auto !important",overflowY:"auto !important"}}}),"");n(2739),n(24665);const vt=(0,u.Z)((function(e){let{element:t,data:n,width:u,height:h,disabled:p,widgetMgr:f,isFullScreen:b,expand:v,collapse:y}=e;const w=i.useRef(null),x=i.useRef(null),C=i.useRef(null),E=qe(),[M,T]=i.useState(!0),[k,_]=i.useState(!1),[O,I]=i.useState(!1),[D,A]=i.useState(!1),H=i.useMemo((()=>window.matchMedia&&window.matchMedia("(pointer: coarse)").matches),[]),V=i.useMemo((()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")||window.navigator.userAgent.includes("Chrome")),[]),[z,F]=i.useState({columns:o.EV.empty(),rows:o.EV.empty(),current:void 0}),j=i.useCallback((()=>{F({columns:o.EV.empty(),rows:o.EV.empty(),current:void 0})}),[]),L=i.useCallback((()=>{F({columns:z.columns,rows:z.rows,current:void 0})}),[z]),W=i.useCallback((e=>{var t;null===(t=x.current)||void 0===t||t.updateCells(e)}),[]);(0,g.le)(t.editingMode)&&(t.editingMode=m.Eh.EditingMode.READ_ONLY);const{READ_ONLY:P,DYNAMIC:Z}=m.Eh.EditingMode,q=n.dimensions,J=Math.max(0,q.rows-1),K=0===J&&!(t.editingMode===Z&&q.dataColumns>0),G=J>15e4,U=i.useRef(new Pe(J)),[X,Q]=i.useState(U.current.getNumRows());i.useEffect((()=>{U.current=new Pe(J),Q(U.current.getNumRows())}),[J]);const $=i.useCallback((()=>{U.current=new Pe(J),Q(U.current.getNumRows())}),[J]),{columns:ee}=Be(t,n,p);i.useEffect((()=>{if(t.editingMode!==P){const e=f.getStringValue(t);e&&(U.current.fromJson(e,ee),Q(U.current.getNumRows()))}}),[]);const{getCellContent:te}=Je(n,ee,X,U),{columns:ne,sortColumn:ie,getOriginalIndex:oe,getCellContent:le}=Ge(J,ee,te),ae=i.useCallback((function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];X!==U.current.getNumRows()&&Q(U.current.getNumRows()),e&&j(),(0,g.Ds)(100,(()=>{const e=U.current.toJson(ne);let i=f.getStringValue(t);void 0===i&&(i=new Pe(0).toJson([])),e!==i&&f.setStringValue(t,e,{fromUi:n})}))()}),[f,t,X,j,ne]),{exportToCsv:re}=nt(le,ne,X),{onCellEdited:se,onPaste:de,onRowAppended:ce,onDelete:ue,validateCell:me}=it(ne,t.editingMode!==Z,U,le,oe,W,ae),{tooltip:ge,clearTooltip:he,onItemHovered:pe}=ot(ne,le),{drawCell:fe,customRenderers:be}=at(ne,t.editingMode===Z),{columns:ve,onColumnResize:ye}=rt(ne.map((e=>Y(e)))),{minHeight:we,maxHeight:xe,minWidth:Ce,maxWidth:Ee,resizableSize:Me,setResizableSize:Te}=ct(t,X,u,h,b),ke=i.useCallback((e=>{let[t,n]=e;return{...B(!0,!1),displayData:"empty",contentAlign:"center",allowOverlay:!1,themeOverride:{textDark:E.textLight},span:[0,Math.max(ne.length-1,0)]}}),[ne,E.textLight]);i.useEffect((()=>{const e=new c.K;return e.manageFormClearListener(f,t.formId,$),()=>{e.disconnect()}}),[t.formId,$,f]);const Ne=!K&&t.editingMode===Z&&!p,Re=z.rows.length>0,Se=void 0!==z.current,_e=K?0:ne.filter((e=>e.isIndex)).length;return i.useEffect((()=>{if(C.current){var e,t;const n=null===(e=C.current)||void 0===e||null===(t=e.querySelector(".dvn-stack"))||void 0===t?void 0:t.getBoundingClientRect();n&&(I(n.height>C.current.clientHeight),A(n.width>C.current.clientWidth))}}),[Me,X,ve]),(0,N.jsxs)(bt,{"data-testid":"stDataFrame",className:"stDataFrame",ref:C,onMouseDown:e=>{if(C.current&&V){const t=C.current.getBoundingClientRect();D&&t.height-7<e.clientY-t.top&&e.stopPropagation(),O&&t.width-7<e.clientX-t.left&&e.stopPropagation()}},onBlur:e=>{M||H||e.currentTarget.contains(e.relatedTarget)||L()},children:[(0,N.jsxs)(S,{isFullScreen:b,locked:Re||Se||H&&M,onExpand:v,onCollapse:y,target:bt,children:[Ne&&Re&&(0,N.jsx)(R,{label:"Delete row(s)",icon:a.H,onClick:()=>{ue&&(ue(z),he())}}),Ne&&!Re&&(0,N.jsx)(R,{label:"Add row",icon:r.m,onClick:()=>{ce&&(T(!0),ce(),he())}}),!G&&!K&&(0,N.jsx)(R,{label:"Download as CSV",icon:s.k,onClick:()=>re()}),!K&&(0,N.jsx)(R,{label:"Search",icon:d.o,onClick:()=>{k?_(!1):(T(!0),_(!0)),he()}})]}),(0,N.jsx)(l.e,{"data-testid":"stDataFrameResizable",ref:w,defaultSize:Me,style:{border:"1px solid ".concat(E.borderColor),borderRadius:"".concat(E.tableBorderRadius)},minHeight:we,maxHeight:xe,minWidth:Ce,maxWidth:Ee,size:Me,enable:{top:!1,right:!1,bottom:!1,left:!1,topRight:!1,bottomRight:!0,bottomLeft:!1,topLeft:!1},grid:[1,st],snapGap:st/3,onResizeStop:(e,t,n,i)=>{w.current&&Te({width:w.current.size.width,height:xe-w.current.size.height===3?w.current.size.height+3:w.current.size.height})},children:(0,N.jsx)(o.Nd,{className:"glideDataEditor",ref:x,columns:ve,rows:K?1:X,minColumnWidth:50,maxColumnWidth:1e3,maxColumnAutoWidth:500,rowHeight:st,headerHeight:st,getCellContent:K?ke:le,onColumnResize:ye,freezeColumns:_e,smoothScrollX:!0,smoothScrollY:!0,verticalBorder:e=>!(e>=ne.length&&(t.useContainerWidth||"100%"===Me.width)),getCellsForSelection:!0,rowMarkers:"none",rangeSelect:H?"none":"rect",columnSelect:"none",rowSelect:"none",onItemHovered:pe,keybindings:{downFill:!0},onKeyDown:e=>{(e.ctrlKey||e.metaKey)&&"f"===e.key&&(_((e=>!e)),e.stopPropagation(),e.preventDefault())},showSearch:k,onSearchClose:()=>{_(!1),he()},onHeaderClicked:K||G?void 0:ie,gridSelection:z,onGridSelectionChange:e=>{(M||H)&&(F(e),void 0!==ge&&he())},theme:E,onMouseMove:e=>{"out-of-bounds"===e.kind&&M?T(!1):"out-of-bounds"===e.kind||M||T(!0)},fixedShadowX:!0,fixedShadowY:!0,experimental:{scrollbarWidthOverride:1,...V&&{paddingBottom:D?-6:void 0,paddingRight:O?-6:void 0}},drawCell:fe,customRenderers:be,imageEditorOverride:mt,headerIcons:E.headerIcons,validateCell:me,onPaste:!1,...!K&&t.editingMode!==P&&!p&&{fillHandle:!H,onCellEdited:se,onPaste:de,onDelete:ue},...!K&&t.editingMode===Z&&{trailingRowOptions:{sticky:!1,tint:!0},rowMarkerTheme:{bgCell:E.bgHeader,bgCellMedium:E.bgHeader},rowMarkers:"checkbox",rowSelectionMode:"multi",rowSelect:p?"none":"multi",onRowAppended:p?void 0:ce,onHeaderClicked:void 0}})}),ge&&ge.content&&(0,N.jsx)(ft,{top:ge.top,left:ge.left,content:ge.content,clearTooltip:he})]})}),!0)},87814:(e,t,n)=>{n.d(t,{K:()=>o});var i=n(50641);class o{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,n){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,i.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,n),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}}}]);