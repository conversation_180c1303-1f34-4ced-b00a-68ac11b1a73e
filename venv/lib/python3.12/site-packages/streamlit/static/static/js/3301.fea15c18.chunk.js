(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[3301],{40763:(t,n,e)=>{"use strict";e.d(n,{Z:()=>u});var a=e(66845),r=e(7974),o=e.n(r),i=e(23175),c=e.n(i),s=e(40864);const p=t=>{let{className:n,scriptRunId:e,numParticles:a,numParticleTypes:r,ParticleComponent:i}=t;return(0,s.jsx)("div",{className:c()(n,"stHidden"),"data-testid":"".concat(n),children:o()(a).map((t=>{const n=Math.floor(Math.random()*r);return(0,s.jsx)(i,{particleType:n},e+t)}))})},u=(0,a.memo)(p)},69436:(t,n,e)=>{"use strict";e.r(n),e.d(n,{NUM_FLAKES:()=>v,default:()=>b});var a=e(66845);const r=e.p+"static/media/flake-0.beded754e8024c73d9d2.png",o=e.p+"static/media/flake-1.8077dc154e0bf900aa73.png",i=e.p+"static/media/flake-2.e3f07d06933dd0e84c24.png";var c,s=e(40763),p=e(50669),u=e(1515),l=e(7865);const m=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Math.random()*(t-n)+n},f=(0,u.Z)("img",{target:"ekdfb790"})((t=>{let{theme:n}=t;return{position:"fixed",top:"".concat(-150,"px"),marginLeft:"".concat(-75,"px"),zIndex:n.zIndices.balloons,left:"".concat(m(90,10),"vw"),animationDelay:"".concat(m(4e3),"ms"),height:"".concat(150,"px"),width:"".concat(150,"px"),pointerEvents:"none",animationDuration:"3000ms",animationName:(0,l.F4)(c||(c=(0,p.Z)(["\n  from {\n    transform:\n      translateY(0)\n      rotateX(","deg)\n      rotateY(","deg)\n      rotateZ(","deg);\n  }\n\n  to {\n    transform:\n      translateY(calc(100vh + ","px))\n      rotateX(0)\n      rotateY(0)\n      rotateZ(0);\n  }\n"])),m(360),m(360),m(360),150),animationTimingFunction:"ease-in",animationDirection:"normal",animationIterationCount:1,opacity:1}}),"");var d=e(40864);const v=100,x=[r,o,i],h=x.length,g=t=>{let{particleType:n}=t;return(0,d.jsx)(f,{src:x[n]})},y=function(t){let{scriptRunId:n}=t;return(0,d.jsx)(s.Z,{className:"snow",scriptRunId:n,numParticleTypes:h,numParticles:v,ParticleComponent:g})},b=(0,a.memo)(y)},61054:t=>{var n=Math.ceil,e=Math.max;t.exports=function(t,a,r,o){for(var i=-1,c=e(n((a-t)/(r||1)),0),s=Array(c);c--;)s[o?c:++i]=t,t+=r;return s}},13811:(t,n,e)=>{var a=e(51030),r=/^\s+/;t.exports=function(t){return t?t.slice(0,a(t)+1).replace(r,""):t}},10583:(t,n,e)=>{var a=e(61054),r=e(18621),o=e(78054);t.exports=function(t){return function(n,e,i){return i&&"number"!=typeof i&&r(n,e,i)&&(e=i=void 0),n=o(n),void 0===e?(e=n,n=0):e=o(e),i=void 0===i?n<e?1:-1:o(i),a(n,e,i,t)}}},51030:t=>{var n=/\s/;t.exports=function(t){for(var e=t.length;e--&&n.test(t.charAt(e)););return e}},7974:(t,n,e)=>{var a=e(10583)();t.exports=a},78054:(t,n,e)=>{var a=e(90773),r=1/0;t.exports=function(t){return t?(t=a(t))===r||t===-1/0?17976931348623157e292*(t<0?-1:1):t===t?t:0:0===t?t:0}},90773:(t,n,e)=>{var a=e(13811),r=e(60506),o=e(3490),i=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,s=/^0o[0-7]+$/i,p=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(o(t))return NaN;if(r(t)){var n="function"==typeof t.valueOf?t.valueOf():t;t=r(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=a(t);var e=c.test(t);return e||s.test(t)?p(t.slice(2),e?2:8):i.test(t)?NaN:+t}}}]);