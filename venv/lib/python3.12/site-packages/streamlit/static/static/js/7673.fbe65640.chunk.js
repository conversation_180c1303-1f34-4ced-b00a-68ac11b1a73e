/*! For license information please see 7673.fbe65640.chunk.js.LICENSE.txt */
(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7673],{97943:(e,t,n)=>{"use strict";n.d(t,{m:()=>l});var r=n(25773),o=n(66845),i=n(69),l=o.forwardRef((function(e,t){return o.createElement(i.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),o.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),o.createElement("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}))}));l.displayName="Add"},57463:(e,t,n)=>{"use strict";n.d(t,{H:()=>l});var r=n(25773),o=n(66845),i=n(69),l=o.forwardRef((function(e,t){return o.createElement(i.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),o.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),o.createElement("path",{d:"M16 9v10H8V9h8m-1.5-6h-5l-1 1H5v2h14V4h-3.5l-1-1zM18 7H6v12c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7z"}))}));l.displayName="Delete"},41342:(e,t,n)=>{"use strict";n.d(t,{k:()=>l});var r=n(25773),o=n(66845),i=n(69),l=o.forwardRef((function(e,t){return o.createElement(i.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),o.createElement("rect",{width:24,height:24,fill:"none"}),o.createElement("path",{d:"M18 15v3H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zm-1-4l-1.41-1.41L13 12.17V4h-2v8.17L8.41 9.59 7 11l5 5 5-5z"}))}));l.displayName="FileDownload"},34367:(e,t,n)=>{"use strict";n.d(t,{i:()=>l});var r=n(25773),o=n(66845),i=n(69),l=o.forwardRef((function(e,t){return o.createElement(i.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),o.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),o.createElement("path",{d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"}))}));l.displayName="Fullscreen"},31011:(e,t,n)=>{"use strict";n.d(t,{m:()=>l});var r=n(25773),o=n(66845),i=n(69),l=o.forwardRef((function(e,t){return o.createElement(i.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),o.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),o.createElement("path",{d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"}))}));l.displayName="FullscreenExit"},17875:(e,t,n)=>{"use strict";n.d(t,{o:()=>l});var r=n(25773),o=n(66845),i=n(69),l=o.forwardRef((function(e,t){return o.createElement(i.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),o.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),o.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))}));l.displayName="Search"},29916:e=>{e.exports=function(e,t,n,r){for(var o=-1,i=null==e?0:e.length;++o<i;){var l=e[o];t(r,l,n(l),e)}return r}},10636:(e,t,n)=>{var r=n(74791);e.exports=function(e,t){return!!(null==e?0:e.length)&&r(e,t,0)>-1}},19774:e=>{e.exports=function(e,t,n){for(var r=-1,o=null==e?0:e.length;++r<o;)if(n(t,e[r]))return!0;return!1}},70199:(e,t,n)=>{var r=n(64811);e.exports=function(e,t,n,o){return r(e,(function(e,r,i){t(o,e,n(e),i)})),o}},18471:e=>{e.exports=function(e,t,n){return e===e&&(void 0!==n&&(e=e<=n?e:n),void 0!==t&&(e=e>=t?e:t)),e}},18218:e=>{e.exports=function(e,t,n,r){for(var o=e.length,i=n+(r?1:-1);r?i--:++i<o;)if(t(e[i],i,e))return i;return-1}},29080:(e,t,n)=>{var r=n(73211),o=n(48258);e.exports=function e(t,n,i,l,a){var s=-1,c=t.length;for(i||(i=o),a||(a=[]);++s<c;){var u=t[s];n>0&&i(u)?n>1?e(u,n-1,i,l,a):r(a,u):l||(a[a.length]=u)}return a}},48003:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e,n){return null!=e&&t.call(e,n)}},74791:(e,t,n)=>{var r=n(18218),o=n(18238),i=n(68603);e.exports=function(e,t,n){return t===t?i(e,t,n):r(e,o,n)}},18238:e=>{e.exports=function(e){return e!==e}},61054:e=>{var t=Math.ceil,n=Math.max;e.exports=function(e,r,o,i){for(var l=-1,a=n(t((r-e)/(o||1)),0),s=Array(a);a--;)s[i?a:++l]=e,e+=o;return s}},13811:(e,t,n)=>{var r=n(51030),o=/^\s+/;e.exports=function(e){return e?e.slice(0,r(e)+1).replace(o,""):e}},34768:(e,t,n)=>{var r=n(89632),o=n(10636),i=n(19774),l=n(7086),a=n(58938),s=n(21278);e.exports=function(e,t,n){var c=-1,u=o,d=e.length,h=!0,p=[],f=p;if(n)h=!1,u=i;else if(d>=200){var g=t?null:a(e);if(g)return s(g);h=!1,u=l,f=new r}else f=t?[]:p;e:for(;++c<d;){var m=e[c],v=t?t(m):m;if(m=n||0!==m?m:0,h&&v===v){for(var b=f.length;b--;)if(f[b]===v)continue e;t&&f.push(v),p.push(m)}else u(f,v,n)||(f!==p&&f.push(v),p.push(m))}return p}},98718:(e,t,n)=>{var r=n(29916),o=n(70199),i=n(48603),l=n(12697);e.exports=function(e,t){return function(n,a){var s=l(n)?r:o,c=t?t():{};return s(n,e,i(a,2),c)}}},10583:(e,t,n)=>{var r=n(61054),o=n(18621),i=n(78054);e.exports=function(e){return function(t,n,l){return l&&"number"!=typeof l&&o(t,n,l)&&(n=l=void 0),t=i(t),void 0===n?(n=t,t=0):n=i(n),l=void 0===l?t<n?1:-1:i(l),r(t,n,l,e)}}},58938:(e,t,n)=>{var r=n(64626),o=n(55304),i=n(21278),l=r&&1/i(new r([,-0]))[1]==1/0?function(e){return new r(e)}:o;e.exports=l},48258:(e,t,n)=>{var r=n(27530),o=n(22846),i=n(12697),l=r?r.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(l&&e&&e[l])}},68603:e=>{e.exports=function(e,t,n){for(var r=n-1,o=e.length;++r<o;)if(e[r]===t)return r;return-1}},51030:e=>{var t=/\s/;e.exports=function(e){for(var n=e.length;n--&&t.test(e.charAt(n)););return n}},51586:(e,t,n)=>{var r=n(18471),o=n(90773);e.exports=function(e,t,n){return void 0===n&&(n=t,t=void 0),void 0!==n&&(n=(n=o(n))===n?n:0),void 0!==t&&(t=(t=o(t))===t?t:0),r(o(e),t,n)}},56797:(e,t,n)=>{var r=n(60506),o=n(3338),i=n(90773),l=Math.max,a=Math.min;e.exports=function(e,t,n){var s,c,u,d,h,p,f=0,g=!1,m=!1,v=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function b(t){var n=s,r=c;return s=c=void 0,f=t,d=e.apply(r,n)}function y(e){var n=e-p;return void 0===p||n>=t||n<0||m&&e-f>=u}function w(){var e=o();if(y(e))return x(e);h=setTimeout(w,function(e){var n=t-(e-p);return m?a(n,u-(e-f)):n}(e))}function x(e){return h=void 0,v&&s?b(e):(s=c=void 0,d)}function k(){var e=o(),n=y(e);if(s=arguments,c=this,p=e,n){if(void 0===h)return function(e){return f=e,h=setTimeout(w,t),g?b(e):d}(p);if(m)return clearTimeout(h),h=setTimeout(w,t),b(p)}return void 0===h&&(h=setTimeout(w,t)),d}return t=i(t)||0,r(n)&&(g=!!n.leading,u=(m="maxWait"in n)?l(i(n.maxWait)||0,t):u,v="trailing"in n?!!n.trailing:v),k.cancel=function(){void 0!==h&&clearTimeout(h),f=0,s=p=c=h=void 0},k.flush=function(){return void 0===h?d:x(o())},k}},86995:(e,t,n)=>{var r=n(29080);e.exports=function(e){return(null==e?0:e.length)?r(e,1):[]}},76236:(e,t,n)=>{var r=n(45742),o=n(98718),i=Object.prototype.hasOwnProperty,l=o((function(e,t,n){i.call(e,n)?e[n].push(t):r(e,n,[t])}));e.exports=l},82781:(e,t,n)=>{var r=n(48003),o=n(98869);e.exports=function(e,t){return null!=e&&o(e,t,r)}},55304:e=>{e.exports=function(){}},3338:(e,t,n)=>{var r=n(19661);e.exports=function(){return r.Date.now()}},7974:(e,t,n)=>{var r=n(10583)();e.exports=r},19266:(e,t,n)=>{var r=n(56797),o=n(60506);e.exports=function(e,t,n){var i=!0,l=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return o(n)&&(i="leading"in n?!!n.leading:i,l="trailing"in n?!!n.trailing:l),r(e,t,{leading:i,maxWait:t,trailing:l})}},78054:(e,t,n)=>{var r=n(90773),o=1/0;e.exports=function(e){return e?(e=r(e))===o||e===-1/0?17976931348623157e292*(e<0?-1:1):e===e?e:0:0===e?e:0}},90773:(e,t,n)=>{var r=n(13811),o=n(60506),i=n(3490),l=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,s=/^0o[0-7]+$/i,c=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return NaN;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=r(e);var n=a.test(e);return n||s.test(e)?c(e.slice(2),n?2:8):l.test(e)?NaN:+e}},17015:(e,t,n)=>{var r=n(34768);e.exports=function(e){return e&&e.length?r(e):[]}},87717:function(e,t,n){var r,o,i,l,a;l=this,a=function(e){var t=!1,n=!1,r=!1,o=!1,i="escape years months weeks days hours minutes seconds milliseconds general".split(" "),l=[{type:"seconds",targets:[{type:"minutes",value:60},{type:"hours",value:3600},{type:"days",value:86400},{type:"weeks",value:604800},{type:"months",value:2678400},{type:"years",value:31536e3}]},{type:"minutes",targets:[{type:"hours",value:60},{type:"days",value:1440},{type:"weeks",value:10080},{type:"months",value:44640},{type:"years",value:525600}]},{type:"hours",targets:[{type:"days",value:24},{type:"weeks",value:168},{type:"months",value:744},{type:"years",value:8760}]},{type:"days",targets:[{type:"weeks",value:7},{type:"months",value:31},{type:"years",value:365}]},{type:"months",targets:[{type:"years",value:12}]}];function a(e,t){return!(t.length>e.length)&&-1!==e.indexOf(t)}function s(e){for(var t="";e;)t+="0",e-=1;return t}function c(e,t){var n=e+"+"+v(C(t).sort(),(function(e){return e+":"+t[e]})).join(",");return c.cache[n]||(c.cache[n]=Intl.NumberFormat(e,t)),c.cache[n]}function u(e,t,i){var l,a,d,h=t.useToLocaleString,p=t.useGrouping,f=p&&t.grouping.slice(),g=t.maximumSignificantDigits,m=t.minimumIntegerDigits||1,v=t.fractionDigits||0,b=t.groupingSeparator,y=t.decimalSeparator;if(h&&i){var w,x={minimumIntegerDigits:m,useGrouping:p};return v&&(x.maximumFractionDigits=v,x.minimumFractionDigits=v),g&&e>0&&(x.maximumSignificantDigits=g),r?(o||((w=S({},t)).useGrouping=!1,w.decimalSeparator=".",e=parseFloat(u(e,w),10)),c(i,x).format(e)):(n||((w=S({},t)).useGrouping=!1,w.decimalSeparator=".",e=parseFloat(u(e,w),10)),e.toLocaleString(i,x))}var k=(g?e.toPrecision(g+1):e.toFixed(v+1)).split("e");d=k[1]||"",a=(k=k[0].split("."))[1]||"";var C=(l=k[0]||"").length,M=a.length,E=C+M,R=l+a;(g&&E===g+1||!g&&M===v+1)&&((R=function(e){for(var t=e.split("").reverse(),n=0,r=!0;r&&n<t.length;)n?"9"===t[n]?t[n]="0":(t[n]=(parseInt(t[n],10)+1).toString(),r=!1):(parseInt(t[n],10)<5&&(r=!1),t[n]="0"),n+=1;return r&&t.push("1"),t.reverse().join("")}(R)).length===E+1&&(C+=1),M&&(R=R.slice(0,-1)),l=R.slice(0,C),a=R.slice(C)),g&&(a=a.replace(/0*$/,""));var T=parseInt(d,10);T>0?a.length<=T?(l+=a+=s(T-a.length),a=""):(l+=a.slice(0,T),a=a.slice(T)):T<0&&(a=s(Math.abs(T)-l.length)+l+a,l="0"),g||((a=a.slice(0,v)).length<v&&(a+=s(v-a.length)),l.length<m&&(l=s(m-l.length)+l));var I,O="";if(p)for(k=l;k.length;)f.length&&(I=f.shift()),O&&(O=b+O),O=k.slice(-I)+O,k=k.slice(0,-I);else O=l;return a&&(O=O+y+a),O}function d(e,t){return e.label.length>t.label.length?-1:e.label.length<t.label.length?1:0}c.cache={};var h={durationLabelsStandard:{S:"millisecond",SS:"milliseconds",s:"second",ss:"seconds",m:"minute",mm:"minutes",h:"hour",hh:"hours",d:"day",dd:"days",w:"week",ww:"weeks",M:"month",MM:"months",y:"year",yy:"years"},durationLabelsShort:{S:"msec",SS:"msecs",s:"sec",ss:"secs",m:"min",mm:"mins",h:"hr",hh:"hrs",d:"dy",dd:"dys",w:"wk",ww:"wks",M:"mo",MM:"mos",y:"yr",yy:"yrs"},durationTimeTemplates:{HMS:"h:mm:ss",HM:"h:mm",MS:"m:ss"},durationLabelTypes:[{type:"standard",string:"__"},{type:"short",string:"_"}],durationPluralKey:function(e,t,n){return 1===t&&null===n?e:e+e}};function p(e){return"[object Array]"===Object.prototype.toString.call(e)}function f(e){return"[object Object]"===Object.prototype.toString.call(e)}function g(e,t){var n,r=0,o=e&&e.length||0;for("function"!==typeof t&&(n=t,t=function(e){return e===n});r<o;){if(t(e[r]))return e[r];r+=1}}function m(e,t){var n=0,r=e.length;if(e&&r)for(;n<r;){if(!1===t(e[n],n))return;n+=1}}function v(e,t){var n=0,r=e.length,o=[];if(!e||!r)return o;for(;n<r;)o[n]=t(e[n],n),n+=1;return o}function b(e,t){return v(e,(function(e){return e[t]}))}function y(e){var t=[];return m(e,(function(e){e&&t.push(e)})),t}function w(e){var t=[];return m(e,(function(e){g(t,e)||t.push(e)})),t}function x(e,t){var n=[];return m(e,(function(e){m(t,(function(t){e===t&&n.push(e)}))})),w(n)}function k(e,t){var n=[];return m(e,(function(r,o){if(!t(r))return n=e.slice(o),!1})),n}function S(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function C(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}function M(e,t){var n=0,r=e.length;if(!e||!r)return!1;for(;n<r;){if(!0===t(e[n],n))return!0;n+=1}return!1}function E(e){return"3.6"===e(3.55,"en",{useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:1,maximumFractionDigits:1})}function R(e){var t=!0;return!!(t=(t=(t=t&&"1"===e(1,"en",{minimumIntegerDigits:1}))&&"01"===e(1,"en",{minimumIntegerDigits:2}))&&"001"===e(1,"en",{minimumIntegerDigits:3}))&&!!(t=(t=(t=(t=t&&"100"===e(99.99,"en",{maximumFractionDigits:0,minimumFractionDigits:0}))&&"100.0"===e(99.99,"en",{maximumFractionDigits:1,minimumFractionDigits:1}))&&"99.99"===e(99.99,"en",{maximumFractionDigits:2,minimumFractionDigits:2}))&&"99.990"===e(99.99,"en",{maximumFractionDigits:3,minimumFractionDigits:3}))&&!!(t=(t=(t=(t=(t=t&&"100"===e(99.99,"en",{maximumSignificantDigits:1}))&&"100"===e(99.99,"en",{maximumSignificantDigits:2}))&&"100"===e(99.99,"en",{maximumSignificantDigits:3}))&&"99.99"===e(99.99,"en",{maximumSignificantDigits:4}))&&"99.99"===e(99.99,"en",{maximumSignificantDigits:5}))&&!!(t=(t=t&&"1,000"===e(1e3,"en",{useGrouping:!0}))&&"1000"===e(1e3,"en",{useGrouping:!1}))}function T(){var e,t=[].slice.call(arguments),n={};if(m(t,(function(t,r){if(!r){if(!p(t))throw"Expected array as the first argument to durationsFormat.";e=t}"string"!==typeof t&&"function"!==typeof t?"number"!==typeof t?f(t)&&S(n,t):n.precision=t:n.template=t})),!e||!e.length)return[];n.returnMomentTypes=!0;var r=v(e,(function(e){return e.format(n)})),o=x(i,w(b(function(e){var t=[];return m(e,(function(e){t=t.concat(e)})),t}(r),"type"))),l=n.largest;return l&&(o=o.slice(0,l)),n.returnMomentTypes=!1,n.outputTypes=o,v(e,(function(e){return e.format(n)}))}function I(){var n=[].slice.call(arguments),o=S({},this.format.defaults),s=this.asMilliseconds(),c=this.asMonths();"function"===typeof this.isValid&&!1===this.isValid()&&(s=0,c=0);var E=s<0,R=e.duration(Math.abs(s),"milliseconds"),T=e.duration(Math.abs(c),"months");m(n,(function(e){"string"!==typeof e&&"function"!==typeof e?"number"!==typeof e?f(e)&&S(o,e):o.precision=e:o.template=e}));var I={years:"y",months:"M",weeks:"w",days:"d",hours:"h",minutes:"m",seconds:"s",milliseconds:"S"},O={escape:/\[(.+?)\]/,years:/\*?[Yy]+/,months:/\*?M+/,weeks:/\*?[Ww]+/,days:/\*?[Dd]+/,hours:/\*?[Hh]+/,minutes:/\*?m+/,seconds:/\*?s+/,milliseconds:/\*?S+/,general:/.+?/};o.types=i;var P=function(e){return g(i,(function(t){return O[t].test(e)}))},D=new RegExp(v(i,(function(e){return O[e].source})).join("|"),"g");o.duration=this;var H="function"===typeof o.template?o.template.apply(o):o.template,L=o.outputTypes,z=o.returnMomentTypes,F=o.largest,A=[];L||(p(o.stopTrim)&&(o.stopTrim=o.stopTrim.join("")),o.stopTrim&&m(o.stopTrim.match(D),(function(e){var t=P(e);"escape"!==t&&"general"!==t&&A.push(t)})));var _=e.localeData();_||(_={}),m(C(h),(function(e){"function"!==typeof h[e]?_["_"+e]||(_["_"+e]=h[e]):_[e]||(_[e]=h[e])})),m(C(_._durationTimeTemplates),(function(e){H=H.replace("_"+e+"_",_._durationTimeTemplates[e])}));var V=o.userLocale||e.locale(),N=o.useLeftUnits,B=o.usePlural,W=o.precision,j=o.forceLength,Z=o.useGrouping,U=o.trunc,X=o.useSignificantDigits&&W>0,Y=X?o.precision:0,K=Y,$=o.minValue,G=!1,q=o.maxValue,Q=!1,J=o.useToLocaleString,ee=o.groupingSeparator,te=o.decimalSeparator,ne=o.grouping;J=J&&(t||r);var re=o.trim;p(re)&&(re=re.join(" ")),null===re&&(F||q||X)&&(re="all"),null!==re&&!0!==re&&"left"!==re&&"right"!==re||(re="large"),!1===re&&(re="");var oe=function(e){return e.test(re)},ie=/both/,le=/^all|[^sm]all/,ae=F>0||M([/large/,ie,le],oe),se=M([/small/,ie,le],oe),ce=M([/mid/,le],oe),ue=M([/final/,le],oe),de=v(H.match(D),(function(e,t){var n=P(e);return"*"===e.slice(0,1)&&(e=e.slice(1),"escape"!==n&&"general"!==n&&A.push(n)),{index:t,length:e.length,text:"",token:"escape"===n?e.replace(O.escape,"$1"):e,type:"escape"===n||"general"===n?null:n}})),he={index:0,length:0,token:"",text:"",type:null},pe=[];N&&de.reverse(),m(de,(function(e){if(e.type)return(he.type||he.text)&&pe.push(he),void(he=e);N?he.text=e.token+he.text:he.text+=e.token})),(he.type||he.text)&&pe.push(he),N&&pe.reverse();var fe=x(i,w(y(b(pe,"type"))));if(!fe.length)return b(pe,"text").join("");fe=v(fe,(function(e,t){var n,r=t+1===fe.length,i=!t;n="years"===e||"months"===e?T.as(e):R.as(e);var l=Math.floor(n),a=n-l,s=g(pe,(function(t){return e===t.type}));return i&&q&&n>q&&(Q=!0),r&&$&&Math.abs(o.duration.as(e))<$&&(G=!0),i&&null===j&&s.length>1&&(j=!0),R.subtract(l,e),T.subtract(l,e),{rawValue:n,wholeValue:l,decimalValue:r?a:0,isSmallest:r,isLargest:i,type:e,tokenLength:s.length}}));var ge,me=U?Math.floor:Math.round,ve=function(e,t){var n=Math.pow(10,t);return me(e*n)/n},be=!1,ye=!1,we=function(e,t){var n={useGrouping:Z,groupingSeparator:ee,decimalSeparator:te,grouping:ne,useToLocaleString:J};return X&&(Y<=0?(e.rawValue=0,e.wholeValue=0,e.decimalValue=0):(n.maximumSignificantDigits=Y,e.significantDigits=Y)),Q&&!ye&&(e.isLargest?(e.wholeValue=q,e.decimalValue=0):(e.wholeValue=0,e.decimalValue=0)),G&&!ye&&(e.isSmallest?(e.wholeValue=$,e.decimalValue=0):(e.wholeValue=0,e.decimalValue=0)),e.isSmallest||e.significantDigits&&e.significantDigits-e.wholeValue.toString().length<=0?W<0?e.value=ve(e.wholeValue,W):0===W?e.value=me(e.wholeValue+e.decimalValue):X?(e.value=U?ve(e.rawValue,Y-e.wholeValue.toString().length):e.rawValue,e.wholeValue&&(Y-=e.wholeValue.toString().length)):(n.fractionDigits=W,e.value=U?e.wholeValue+ve(e.decimalValue,W):e.wholeValue+e.decimalValue):X&&e.wholeValue?(e.value=Math.round(ve(e.wholeValue,e.significantDigits-e.wholeValue.toString().length)),Y-=e.wholeValue.toString().length):e.value=e.wholeValue,e.tokenLength>1&&(j||be)&&(n.minimumIntegerDigits=e.tokenLength,ye&&n.maximumSignificantDigits<e.tokenLength&&delete n.maximumSignificantDigits),!be&&(e.value>0||""===re||g(A,e.type)||g(L,e.type))&&(be=!0),e.formattedValue=u(e.value,n,V),n.useGrouping=!1,n.decimalSeparator=".",e.formattedValueEn=u(e.value,n,"en"),2===e.tokenLength&&"milliseconds"===e.type&&(e.formattedValueMS=u(e.value,{minimumIntegerDigits:3,useGrouping:!1},"en").slice(0,2)),e};if((fe=y(fe=v(fe,we))).length>1){var xe=function(e){return g(fe,(function(t){return t.type===e}))};m(l,(function(e){var t=xe(e.type);t&&m(e.targets,(function(e){var n=xe(e.type);n&&parseInt(t.formattedValueEn,10)===e.value&&(t.rawValue=0,t.wholeValue=0,t.decimalValue=0,n.rawValue+=1,n.wholeValue+=1,n.decimalValue=0,n.formattedValueEn=n.wholeValue.toString(),ye=!0)}))}))}return ye&&(be=!1,Y=K,fe=y(fe=v(fe,we))),!L||Q&&!o.trim?(ae&&(fe=k(fe,(function(e){return!e.isSmallest&&!e.wholeValue&&!g(A,e.type)}))),F&&fe.length&&(fe=fe.slice(0,F)),se&&fe.length>1&&(ge=function(e){return!e.wholeValue&&!g(A,e.type)&&!e.isLargest},fe=k(fe.slice().reverse(),ge).reverse()),ce&&(fe=y(fe=v(fe,(function(e,t){return t>0&&t<fe.length-1&&!e.wholeValue?null:e})))),!ue||1!==fe.length||fe[0].wholeValue||!U&&fe[0].isSmallest&&fe[0].rawValue<$||(fe=[])):fe=y(fe=v(fe,(function(e){return g(L,(function(t){return e.type===t}))?e:null}))),z?fe:(m(pe,(function(e){var t=I[e.type],n=g(fe,(function(t){return t.type===e.type}));if(t&&n){var r=n.formattedValueEn.split(".");r[0]=parseInt(r[0],10),r[1]?r[1]=parseFloat("0."+r[1],10):r[1]=null;var o=_.durationPluralKey(t,r[0],r[1]),i=function(e,t){var n=[];return m(C(t),(function(r){if("_durationLabels"===r.slice(0,15)){var o=r.slice(15).toLowerCase();m(C(t[r]),(function(i){i.slice(0,1)===e&&n.push({type:o,key:i,label:t[r][i]})}))}})),n}(t,_),l=!1,s={};m(_._durationLabelTypes,(function(t){var n=g(i,(function(e){return e.type===t.type&&e.key===o}));n&&(s[n.type]=n.label,a(e.text,t.string)&&(e.text=e.text.replace(t.string,n.label),l=!0))})),B&&!l&&(i.sort(d),m(i,(function(t){return s[t.type]===t.label?!a(e.text,t.label)&&void 0:a(e.text,t.label)?(e.text=e.text.replace(t.label,s[t.type]),!1):void 0})))}})),(pe=v(pe,(function(e){if(!e.type)return e.text;var t=g(fe,(function(t){return t.type===e.type}));if(!t)return"";var n="";return N&&(n+=e.text),(E&&Q||!E&&G)&&(n+="< ",Q=!1,G=!1),(E&&G||!E&&Q)&&(n+="> ",Q=!1,G=!1),E&&(t.value>0||""===re||g(A,t.type)||g(L,t.type))&&(n+="-",E=!1),"milliseconds"===e.type&&t.formattedValueMS?n+=t.formattedValueMS:n+=t.formattedValue,N||(n+=e.text),n}))).join("").replace(/(,| |:|\.)*$/,"").replace(/^(,| |:|\.)*/,""))}function O(){var e=this.duration,t=function(t){return e._data[t]},n=g(this.types,t),r=function(e,t){for(var n=e.length;n-=1;)if(t(e[n]))return e[n]}(this.types,t);switch(n){case"milliseconds":return"S __";case"seconds":case"minutes":return"*_MS_";case"hours":return"_HMS_";case"days":if(n===r)return"d __";case"weeks":return n===r?"w __":(null===this.trim&&(this.trim="both"),"w __, d __, h __");case"months":if(n===r)return"M __";case"years":return n===r?"y __":(null===this.trim&&(this.trim="both"),"y __, M __, d __");default:return null===this.trim&&(this.trim="both"),"y __, d __, h __, m __, s __"}}function P(e){if(!e)throw"Moment Duration Format init cannot find moment instance.";e.duration.format=T,e.duration.fn.format=I,e.duration.fn.format.defaults={trim:null,stopTrim:null,largest:null,maxValue:null,minValue:null,precision:0,trunc:!1,forceLength:null,userLocale:null,usePlural:!0,useLeftUnits:!1,useGrouping:!0,useSignificantDigits:!1,template:O,useToLocaleString:!0,groupingSeparator:",",decimalSeparator:".",grouping:[3]},e.updateLocale("en",h)}var D=function(e,t,n){return e.toLocaleString(t,n)};t=function(){try{(0).toLocaleString("i")}catch(e){return"RangeError"===e.name}return!1}()&&R(D),n=t&&E(D);var H=function(e,t,n){if("undefined"!==typeof window&&window&&window.Intl&&window.Intl.NumberFormat)return window.Intl.NumberFormat(t,n).format(e)};return r=R(H),o=r&&E(H),P(e),P},o=[n(53608)],void 0===(i="function"===typeof(r=a)?r.apply(t,o):r)||(e.exports=i),l&&(l.momentDurationFormatSetup=l.moment?a(l.moment):a)},15650:function(e,t,n){var r,o,i;o=[t,n(73074)],r=function(e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(t);function r(e){return e&&e.__esModule?e:{default:e}}e.default=n.default},void 0===(i="function"===typeof r?r.apply(t,o):r)||(e.exports=i)},73074:function(e,t,n){var r,o,i;o=[t,n(66845),n(8984)],r=function(e,t,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHasSupportToCaptureOption=p;var r=i(t),o=i(n);function i(e){return e&&e.__esModule?e:{default:e}}var l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};function a(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var c=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}function d(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var h=!1;function p(e){h=e}try{addEventListener("test",null,Object.defineProperty({},"capture",{get:function(){p(!0)}}))}catch(v){}function f(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{capture:!0};return h?e:e.capture}function g(e){if("touches"in e){var t=e.touches[0];return{x:t.pageX,y:t.pageY}}return{x:e.screenX,y:e.screenY}}var m=function(e){function t(){var e;s(this,t);for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];var i=u(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(r)));return i._handleSwipeStart=i._handleSwipeStart.bind(i),i._handleSwipeMove=i._handleSwipeMove.bind(i),i._handleSwipeEnd=i._handleSwipeEnd.bind(i),i._onMouseDown=i._onMouseDown.bind(i),i._onMouseMove=i._onMouseMove.bind(i),i._onMouseUp=i._onMouseUp.bind(i),i._setSwiperRef=i._setSwiperRef.bind(i),i}return d(t,e),c(t,[{key:"componentDidMount",value:function(){this.swiper&&this.swiper.addEventListener("touchmove",this._handleSwipeMove,f({capture:!0,passive:!1}))}},{key:"componentWillUnmount",value:function(){this.swiper&&this.swiper.removeEventListener("touchmove",this._handleSwipeMove,f({capture:!0,passive:!1}))}},{key:"_onMouseDown",value:function(e){this.props.allowMouseEvents&&(this.mouseDown=!0,document.addEventListener("mouseup",this._onMouseUp),document.addEventListener("mousemove",this._onMouseMove),this._handleSwipeStart(e))}},{key:"_onMouseMove",value:function(e){this.mouseDown&&this._handleSwipeMove(e)}},{key:"_onMouseUp",value:function(e){this.mouseDown=!1,document.removeEventListener("mouseup",this._onMouseUp),document.removeEventListener("mousemove",this._onMouseMove),this._handleSwipeEnd(e)}},{key:"_handleSwipeStart",value:function(e){var t=g(e),n=t.x,r=t.y;this.moveStart={x:n,y:r},this.props.onSwipeStart(e)}},{key:"_handleSwipeMove",value:function(e){if(this.moveStart){var t=g(e),n=t.x,r=t.y,o=n-this.moveStart.x,i=r-this.moveStart.y;this.moving=!0,this.props.onSwipeMove({x:o,y:i},e)&&e.cancelable&&e.preventDefault(),this.movePosition={deltaX:o,deltaY:i}}}},{key:"_handleSwipeEnd",value:function(e){this.props.onSwipeEnd(e);var t=this.props.tolerance;this.moving&&this.movePosition&&(this.movePosition.deltaX<-t?this.props.onSwipeLeft(1,e):this.movePosition.deltaX>t&&this.props.onSwipeRight(1,e),this.movePosition.deltaY<-t?this.props.onSwipeUp(1,e):this.movePosition.deltaY>t&&this.props.onSwipeDown(1,e)),this.moveStart=null,this.moving=!1,this.movePosition=null}},{key:"_setSwiperRef",value:function(e){this.swiper=e,this.props.innerRef(e)}},{key:"render",value:function(){var e=this.props,t=(e.tagName,e.className),n=e.style,o=e.children,i=(e.allowMouseEvents,e.onSwipeUp,e.onSwipeDown,e.onSwipeLeft,e.onSwipeRight,e.onSwipeStart,e.onSwipeMove,e.onSwipeEnd,e.innerRef,e.tolerance,a(e,["tagName","className","style","children","allowMouseEvents","onSwipeUp","onSwipeDown","onSwipeLeft","onSwipeRight","onSwipeStart","onSwipeMove","onSwipeEnd","innerRef","tolerance"]));return r.default.createElement(this.props.tagName,l({ref:this._setSwiperRef,onMouseDown:this._onMouseDown,onTouchStart:this._handleSwipeStart,onTouchEnd:this._handleSwipeEnd,className:t,style:n},i),o)}}]),t}(t.Component);m.displayName="ReactSwipe",m.propTypes={tagName:o.default.string,className:o.default.string,style:o.default.object,children:o.default.node,allowMouseEvents:o.default.bool,onSwipeUp:o.default.func,onSwipeDown:o.default.func,onSwipeLeft:o.default.func,onSwipeRight:o.default.func,onSwipeStart:o.default.func,onSwipeMove:o.default.func,onSwipeEnd:o.default.func,innerRef:o.default.func,tolerance:o.default.number.isRequired},m.defaultProps={tagName:"div",allowMouseEvents:!1,onSwipeUp:function(){},onSwipeDown:function(){},onSwipeLeft:function(){},onSwipeRight:function(){},onSwipeStart:function(){},onSwipeMove:function(){},onSwipeEnd:function(){},innerRef:function(){},tolerance:0},e.default=m},void 0===(i="function"===typeof r?r.apply(t,o):r)||(e.exports=i)},30480:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(e,t,n){var r=0===e?e:e+t;return"translate3d"+("("+("horizontal"===n?[r,0,0]:[0,r,0]).join(",")+")")}},98806:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fadeAnimationHandler=t.slideStopSwipingHandler=t.slideSwipeAnimationHandler=t.slideAnimationHandler=void 0;var r,o=n(66845),i=(r=n(30480))&&r.__esModule?r:{default:r},l=n(34705);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}t.slideAnimationHandler=function(e,t){var n={},r=t.selectedItem,a=r,c=o.Children.count(e.children)-1;if(e.infiniteLoop&&(r<0||r>c))return a<0?e.centerMode&&e.centerSlidePercentage&&"horizontal"===e.axis?n.itemListStyle=(0,l.setPosition)(-(c+2)*e.centerSlidePercentage-(100-e.centerSlidePercentage)/2,e.axis):n.itemListStyle=(0,l.setPosition)(100*-(c+2),e.axis):a>c&&(n.itemListStyle=(0,l.setPosition)(0,e.axis)),n;var u=(0,l.getPosition)(r,e),d=(0,i.default)(u,"%",e.axis),h=e.transitionTime+"ms";return n.itemListStyle={WebkitTransform:d,msTransform:d,OTransform:d,transform:d},t.swiping||(n.itemListStyle=s(s({},n.itemListStyle),{},{WebkitTransitionDuration:h,MozTransitionDuration:h,OTransitionDuration:h,transitionDuration:h,msTransitionDuration:h})),n};t.slideSwipeAnimationHandler=function(e,t,n,r){var i={},a="horizontal"===t.axis,s=o.Children.count(t.children),c=(0,l.getPosition)(n.selectedItem,t),u=t.infiniteLoop?(0,l.getPosition)(s-1,t)-100:(0,l.getPosition)(s-1,t),d=a?e.x:e.y,h=d;0===c&&d>0&&(h=0),c===u&&d<0&&(h=0);var p=c+100/(n.itemSize/h),f=Math.abs(d)>t.swipeScrollTolerance;return t.infiniteLoop&&f&&(0===n.selectedItem&&p>-100?p-=100*s:n.selectedItem===s-1&&p<100*-s&&(p+=100*s)),(!t.preventMovementUntilSwipeScrollTolerance||f||n.swipeMovementStarted)&&(n.swipeMovementStarted||r({swipeMovementStarted:!0}),i.itemListStyle=(0,l.setPosition)(p,t.axis)),f&&!n.cancelClick&&r({cancelClick:!0}),i};t.slideStopSwipingHandler=function(e,t){var n=(0,l.getPosition)(t.selectedItem,e);return{itemListStyle:(0,l.setPosition)(n,e.axis)}};t.fadeAnimationHandler=function(e,t){var n=e.transitionTime+"ms",r="ease-in-out",o={position:"absolute",display:"block",zIndex:-2,minHeight:"100%",opacity:0,top:0,right:0,left:0,bottom:0,transitionTimingFunction:r,msTransitionTimingFunction:r,MozTransitionTimingFunction:r,WebkitTransitionTimingFunction:r,OTransitionTimingFunction:r};return t.swiping||(o=s(s({},o),{},{WebkitTransitionDuration:n,MozTransitionDuration:n,OTransitionDuration:n,transitionDuration:n,msTransitionDuration:n})),{slideStyle:o,selectedStyle:s(s({},o),{},{opacity:1,position:"relative"}),prevStyle:s({},o)}}},42852:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==p(e)&&"function"!==typeof e)return{default:e};var t=h();if(t&&t.has(e))return t.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=r?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}n.default=e,t&&t.set(e,n);return n}(n(66845)),o=d(n(15650)),i=d(n(75385)),l=d(n(91935)),a=d(n(39896)),s=d(n(22124)),c=n(34705),u=n(98806);function d(e){return e&&e.__esModule?e:{default:e}}function h(){if("function"!==typeof WeakMap)return null;var e=new WeakMap;return h=function(){return e},e}function p(e){return p="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function f(){return f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},f.apply(this,arguments)}function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function b(e,t){return b=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},b(e,t)}function y(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=x(e);if(t){var o=x(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"===p(t)||"function"===typeof t))return t;return w(e)}(this,n)}}function w(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function x(e){return x=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},x(e)}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var S=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&b(e,t)}(p,e);var t,n,d,h=y(p);function p(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,p),k(w(t=h.call(this,e)),"thumbsRef",void 0),k(w(t),"carouselWrapperRef",void 0),k(w(t),"listRef",void 0),k(w(t),"itemsRef",void 0),k(w(t),"timer",void 0),k(w(t),"animationHandler",void 0),k(w(t),"setThumbsRef",(function(e){t.thumbsRef=e})),k(w(t),"setCarouselWrapperRef",(function(e){t.carouselWrapperRef=e})),k(w(t),"setListRef",(function(e){t.listRef=e})),k(w(t),"setItemsRef",(function(e,n){t.itemsRef||(t.itemsRef=[]),t.itemsRef[n]=e})),k(w(t),"autoPlay",(function(){r.Children.count(t.props.children)<=1||(t.clearAutoPlay(),t.props.autoPlay&&(t.timer=setTimeout((function(){t.increment()}),t.props.interval)))})),k(w(t),"clearAutoPlay",(function(){t.timer&&clearTimeout(t.timer)})),k(w(t),"resetAutoPlay",(function(){t.clearAutoPlay(),t.autoPlay()})),k(w(t),"stopOnHover",(function(){t.setState({isMouseEntered:!0},t.clearAutoPlay)})),k(w(t),"startOnLeave",(function(){t.setState({isMouseEntered:!1},t.autoPlay)})),k(w(t),"isFocusWithinTheCarousel",(function(){return!!t.carouselWrapperRef&&!((0,a.default)().activeElement!==t.carouselWrapperRef&&!t.carouselWrapperRef.contains((0,a.default)().activeElement))})),k(w(t),"navigateWithKeyboard",(function(e){if(t.isFocusWithinTheCarousel()){var n="horizontal"===t.props.axis,r=n?37:38;(n?39:40)===e.keyCode?t.increment():r===e.keyCode&&t.decrement()}})),k(w(t),"updateSizes",(function(){if(t.state.initialized&&t.itemsRef&&0!==t.itemsRef.length){var e="horizontal"===t.props.axis,n=t.itemsRef[0];if(n){var r=e?n.clientWidth:n.clientHeight;t.setState({itemSize:r}),t.thumbsRef&&t.thumbsRef.updateSizes()}}})),k(w(t),"setMountState",(function(){t.setState({hasMount:!0}),t.updateSizes()})),k(w(t),"handleClickItem",(function(e,n){0!==r.Children.count(t.props.children)&&(t.state.cancelClick?t.setState({cancelClick:!1}):(t.props.onClickItem(e,n),e!==t.state.selectedItem&&t.setState({selectedItem:e})))})),k(w(t),"handleOnChange",(function(e,n){r.Children.count(t.props.children)<=1||t.props.onChange(e,n)})),k(w(t),"handleClickThumb",(function(e,n){t.props.onClickThumb(e,n),t.moveTo(e)})),k(w(t),"onSwipeStart",(function(e){t.setState({swiping:!0}),t.props.onSwipeStart(e)})),k(w(t),"onSwipeEnd",(function(e){t.setState({swiping:!1,cancelClick:!1,swipeMovementStarted:!1}),t.props.onSwipeEnd(e),t.clearAutoPlay(),t.state.autoPlay&&t.autoPlay()})),k(w(t),"onSwipeMove",(function(e,n){t.props.onSwipeMove(n);var r=t.props.swipeAnimationHandler(e,t.props,t.state,t.setState.bind(w(t)));return t.setState(m({},r)),!!Object.keys(r).length})),k(w(t),"decrement",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;t.moveTo(t.state.selectedItem-("number"===typeof e?e:1))})),k(w(t),"increment",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;t.moveTo(t.state.selectedItem+("number"===typeof e?e:1))})),k(w(t),"moveTo",(function(e){if("number"===typeof e){var n=r.Children.count(t.props.children)-1;e<0&&(e=t.props.infiniteLoop?n:0),e>n&&(e=t.props.infiniteLoop?0:n),t.selectItem({selectedItem:e}),t.state.autoPlay&&!1===t.state.isMouseEntered&&t.resetAutoPlay()}})),k(w(t),"onClickNext",(function(){t.increment(1)})),k(w(t),"onClickPrev",(function(){t.decrement(1)})),k(w(t),"onSwipeForward",(function(){t.increment(1),t.props.emulateTouch&&t.setState({cancelClick:!0})})),k(w(t),"onSwipeBackwards",(function(){t.decrement(1),t.props.emulateTouch&&t.setState({cancelClick:!0})})),k(w(t),"changeItem",(function(e){return function(n){(0,c.isKeyboardEvent)(n)&&"Enter"!==n.key||t.moveTo(e)}})),k(w(t),"selectItem",(function(e){t.setState(m({previousItem:t.state.selectedItem},e),(function(){t.setState(t.animationHandler(t.props,t.state))})),t.handleOnChange(e.selectedItem,r.Children.toArray(t.props.children)[e.selectedItem])})),k(w(t),"getInitialImage",(function(){var e=t.props.selectedItem,n=t.itemsRef&&t.itemsRef[e];return(n&&n.getElementsByTagName("img")||[])[0]})),k(w(t),"getVariableItemHeight",(function(e){var n=t.itemsRef&&t.itemsRef[e];if(t.state.hasMount&&n&&n.children.length){var r=n.children[0].getElementsByTagName("img")||[];if(r.length>0){var o=r[0];if(!o.complete){o.addEventListener("load",(function e(){t.forceUpdate(),o.removeEventListener("load",e)}))}}var i=(r[0]||n.children[0]).clientHeight;return i>0?i:null}return null}));var n={initialized:!1,previousItem:e.selectedItem,selectedItem:e.selectedItem,hasMount:!1,isMouseEntered:!1,autoPlay:e.autoPlay,swiping:!1,swipeMovementStarted:!1,cancelClick:!1,itemSize:1,itemListStyle:{},slideStyle:{},selectedStyle:{},prevStyle:{}};return t.animationHandler="function"===typeof e.animationHandler&&e.animationHandler||"fade"===e.animationHandler&&u.fadeAnimationHandler||u.slideAnimationHandler,t.state=m(m({},n),t.animationHandler(e,n)),t}return t=p,(n=[{key:"componentDidMount",value:function(){this.props.children&&this.setupCarousel()}},{key:"componentDidUpdate",value:function(e,t){e.children||!this.props.children||this.state.initialized||this.setupCarousel(),!e.autoFocus&&this.props.autoFocus&&this.forceFocus(),t.swiping&&!this.state.swiping&&this.setState(m({},this.props.stopSwipingHandler(this.props,this.state))),e.selectedItem===this.props.selectedItem&&e.centerMode===this.props.centerMode||(this.updateSizes(),this.moveTo(this.props.selectedItem)),e.autoPlay!==this.props.autoPlay&&(this.props.autoPlay?this.setupAutoPlay():this.destroyAutoPlay(),this.setState({autoPlay:this.props.autoPlay}))}},{key:"componentWillUnmount",value:function(){this.destroyCarousel()}},{key:"setupCarousel",value:function(){var e=this;this.bindEvents(),this.state.autoPlay&&r.Children.count(this.props.children)>1&&this.setupAutoPlay(),this.props.autoFocus&&this.forceFocus(),this.setState({initialized:!0},(function(){var t=e.getInitialImage();t&&!t.complete?t.addEventListener("load",e.setMountState):e.setMountState()}))}},{key:"destroyCarousel",value:function(){this.state.initialized&&(this.unbindEvents(),this.destroyAutoPlay())}},{key:"setupAutoPlay",value:function(){this.autoPlay();var e=this.carouselWrapperRef;this.props.stopOnHover&&e&&(e.addEventListener("mouseenter",this.stopOnHover),e.addEventListener("mouseleave",this.startOnLeave))}},{key:"destroyAutoPlay",value:function(){this.clearAutoPlay();var e=this.carouselWrapperRef;this.props.stopOnHover&&e&&(e.removeEventListener("mouseenter",this.stopOnHover),e.removeEventListener("mouseleave",this.startOnLeave))}},{key:"bindEvents",value:function(){(0,s.default)().addEventListener("resize",this.updateSizes),(0,s.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.props.useKeyboardArrows&&(0,a.default)().addEventListener("keydown",this.navigateWithKeyboard)}},{key:"unbindEvents",value:function(){(0,s.default)().removeEventListener("resize",this.updateSizes),(0,s.default)().removeEventListener("DOMContentLoaded",this.updateSizes);var e=this.getInitialImage();e&&e.removeEventListener("load",this.setMountState),this.props.useKeyboardArrows&&(0,a.default)().removeEventListener("keydown",this.navigateWithKeyboard)}},{key:"forceFocus",value:function(){var e;null===(e=this.carouselWrapperRef)||void 0===e||e.focus()}},{key:"renderItems",value:function(e){var t=this;return this.props.children?r.Children.map(this.props.children,(function(n,o){var l=o===t.state.selectedItem,a=o===t.state.previousItem,s=l&&t.state.selectedStyle||a&&t.state.prevStyle||t.state.slideStyle||{};t.props.centerMode&&"horizontal"===t.props.axis&&(s=m(m({},s),{},{minWidth:t.props.centerSlidePercentage+"%"})),t.state.swiping&&t.state.swipeMovementStarted&&(s=m(m({},s),{},{pointerEvents:"none"}));var c={ref:function(e){return t.setItemsRef(e,o)},key:"itemKey"+o+(e?"clone":""),className:i.default.ITEM(!0,o===t.state.selectedItem,o===t.state.previousItem),onClick:t.handleClickItem.bind(t,o,n),style:s};return r.default.createElement("li",c,t.props.renderItem(n,{isSelected:o===t.state.selectedItem,isPrevious:o===t.state.previousItem}))})):[]}},{key:"renderControls",value:function(){var e=this,t=this.props,n=t.showIndicators,o=t.labels,i=t.renderIndicator,l=t.children;return n?r.default.createElement("ul",{className:"control-dots"},r.Children.map(l,(function(t,n){return i&&i(e.changeItem(n),n===e.state.selectedItem,n,o.item)}))):null}},{key:"renderStatus",value:function(){return this.props.showStatus?r.default.createElement("p",{className:"carousel-status"},this.props.statusFormatter(this.state.selectedItem+1,r.Children.count(this.props.children))):null}},{key:"renderThumbs",value:function(){return this.props.showThumbs&&this.props.children&&0!==r.Children.count(this.props.children)?r.default.createElement(l.default,{ref:this.setThumbsRef,onSelectItem:this.handleClickThumb,selectedItem:this.state.selectedItem,transitionTime:this.props.transitionTime,thumbWidth:this.props.thumbWidth,labels:this.props.labels,emulateTouch:this.props.emulateTouch},this.props.renderThumbs(this.props.children)):null}},{key:"render",value:function(){var e=this;if(!this.props.children||0===r.Children.count(this.props.children))return null;var t=this.props.swipeable&&r.Children.count(this.props.children)>1,n="horizontal"===this.props.axis,l=this.props.showArrows&&r.Children.count(this.props.children)>1,a=l&&(this.state.selectedItem>0||this.props.infiniteLoop)||!1,s=l&&(this.state.selectedItem<r.Children.count(this.props.children)-1||this.props.infiniteLoop)||!1,c=this.renderItems(!0),u=c.shift(),d=c.pop(),h={className:i.default.SLIDER(!0,this.state.swiping),onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:this.state.itemListStyle,tolerance:this.props.swipeScrollTolerance},p={};if(n){if(h.onSwipeLeft=this.onSwipeForward,h.onSwipeRight=this.onSwipeBackwards,this.props.dynamicHeight){var g=this.getVariableItemHeight(this.state.selectedItem);p.height=g||"auto"}}else h.onSwipeUp="natural"===this.props.verticalSwipe?this.onSwipeBackwards:this.onSwipeForward,h.onSwipeDown="natural"===this.props.verticalSwipe?this.onSwipeForward:this.onSwipeBackwards,h.style=m(m({},h.style),{},{height:this.state.itemSize}),p.height=this.state.itemSize;return r.default.createElement("div",{"aria-label":this.props.ariaLabel,className:i.default.ROOT(this.props.className),ref:this.setCarouselWrapperRef,tabIndex:this.props.useKeyboardArrows?0:void 0},r.default.createElement("div",{className:i.default.CAROUSEL(!0),style:{width:this.props.width}},this.renderControls(),this.props.renderArrowPrev(this.onClickPrev,a,this.props.labels.leftArrow),r.default.createElement("div",{className:i.default.WRAPPER(!0,this.props.axis),style:p},t?r.default.createElement(o.default,f({tagName:"ul",innerRef:this.setListRef},h,{allowMouseEvents:this.props.emulateTouch}),this.props.infiniteLoop&&d,this.renderItems(),this.props.infiniteLoop&&u):r.default.createElement("ul",{className:i.default.SLIDER(!0,this.state.swiping),ref:function(t){return e.setListRef(t)},style:this.state.itemListStyle||{}},this.props.infiniteLoop&&d,this.renderItems(),this.props.infiniteLoop&&u)),this.props.renderArrowNext(this.onClickNext,s,this.props.labels.rightArrow),this.renderStatus()),this.renderThumbs())}}])&&v(t.prototype,n),d&&v(t,d),p}(r.default.Component);t.default=S,k(S,"displayName","Carousel"),k(S,"defaultProps",{ariaLabel:void 0,axis:"horizontal",centerSlidePercentage:80,interval:3e3,labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},onClickItem:c.noop,onClickThumb:c.noop,onChange:c.noop,onSwipeStart:function(){},onSwipeEnd:function(){},onSwipeMove:function(){return!1},preventMovementUntilSwipeScrollTolerance:!1,renderArrowPrev:function(e,t,n){return r.default.createElement("button",{type:"button","aria-label":n,className:i.default.ARROW_PREV(!t),onClick:e})},renderArrowNext:function(e,t,n){return r.default.createElement("button",{type:"button","aria-label":n,className:i.default.ARROW_NEXT(!t),onClick:e})},renderIndicator:function(e,t,n,o){return r.default.createElement("li",{className:i.default.DOT(t),onClick:e,onKeyDown:e,value:n,key:n,role:"button",tabIndex:0,"aria-label":"".concat(o," ").concat(n+1)})},renderItem:function(e){return e},renderThumbs:function(e){var t=r.Children.map(e,(function(e){var t=e;if("img"!==e.type&&(t=r.Children.toArray(e.props.children).find((function(e){return"img"===e.type}))),t)return t}));return 0===t.filter((function(e){return e})).length?(console.warn("No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md"),[]):t},statusFormatter:c.defaultStatusFormatter,selectedItem:0,showArrows:!0,showIndicators:!0,showStatus:!0,showThumbs:!0,stopOnHover:!0,swipeScrollTolerance:5,swipeable:!0,transitionTime:350,verticalSwipe:"standard",width:"100%",animationHandler:"slide",swipeAnimationHandler:u.slideSwipeAnimationHandler,stopSwipingHandler:u.slideStopSwipingHandler})},96239:()=>{},34705:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setPosition=t.getPosition=t.isKeyboardEvent=t.defaultStatusFormatter=t.noop=void 0;var r,o=n(66845),i=(r=n(30480))&&r.__esModule?r:{default:r};t.noop=function(){};t.defaultStatusFormatter=function(e,t){return"".concat(e," of ").concat(t)};t.isKeyboardEvent=function(e){return!!e&&e.hasOwnProperty("key")};t.getPosition=function(e,t){if(t.infiniteLoop&&++e,0===e)return 0;var n=o.Children.count(t.children);if(t.centerMode&&"horizontal"===t.axis){var r=-e*t.centerSlidePercentage,i=n-1;return e&&(e!==i||t.infiniteLoop)?r+=(100-t.centerSlidePercentage)/2:e===i&&(r+=100-t.centerSlidePercentage),r}return 100*-e};t.setPosition=function(e,t){var n={};return["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach((function(r){n[r]=(0,i.default)(e,"%",t)})),n}},91935:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==d(e)&&"function"!==typeof e)return{default:e};var t=u();if(t&&t.has(e))return t.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=r?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}n.default=e,t&&t.set(e,n);return n}(n(66845)),o=c(n(75385)),i=n(24202),l=c(n(30480)),a=c(n(15650)),s=c(n(22124));function c(e){return e&&e.__esModule?e:{default:e}}function u(){if("function"!==typeof WeakMap)return null;var e=new WeakMap;return u=function(){return e},e}function d(e){return d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function h(){return h=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},h.apply(this,arguments)}function p(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function f(e,t){return f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},f(e,t)}function g(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=v(e);if(t){var o=v(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"===d(t)||"function"===typeof t))return t;return m(e)}(this,n)}}function m(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function v(e){return v=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},v(e)}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var y=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&f(e,t)}(d,e);var t,n,c,u=g(d);function d(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),b(m(t=u.call(this,e)),"itemsWrapperRef",void 0),b(m(t),"itemsListRef",void 0),b(m(t),"thumbsRef",void 0),b(m(t),"setItemsWrapperRef",(function(e){t.itemsWrapperRef=e})),b(m(t),"setItemsListRef",(function(e){t.itemsListRef=e})),b(m(t),"setThumbsRef",(function(e,n){t.thumbsRef||(t.thumbsRef=[]),t.thumbsRef[n]=e})),b(m(t),"updateSizes",(function(){if(t.props.children&&t.itemsWrapperRef&&t.thumbsRef){var e=r.Children.count(t.props.children),n=t.itemsWrapperRef.clientWidth,o=t.props.thumbWidth?t.props.thumbWidth:(0,i.outerWidth)(t.thumbsRef[0]),l=Math.floor(n/o),a=l<e,s=a?e-l:0;t.setState((function(e,n){return{itemSize:o,visibleItems:l,firstItem:a?t.getFirstItem(n.selectedItem):0,lastPosition:s,showArrows:a}}))}})),b(m(t),"handleClickItem",(function(e,n,r){if(!function(e){return e.hasOwnProperty("key")}(r)||"Enter"===r.key){var o=t.props.onSelectItem;"function"===typeof o&&o(e,n)}})),b(m(t),"onSwipeStart",(function(){t.setState({swiping:!0})})),b(m(t),"onSwipeEnd",(function(){t.setState({swiping:!1})})),b(m(t),"onSwipeMove",(function(e){var n=e.x;if(!t.state.itemSize||!t.itemsWrapperRef||!t.state.visibleItems)return!1;var o=r.Children.count(t.props.children),i=-100*t.state.firstItem/t.state.visibleItems;0===i&&n>0&&(n=0),i===100*-Math.max(o-t.state.visibleItems,0)/t.state.visibleItems&&n<0&&(n=0);var a=i+100/(t.itemsWrapperRef.clientWidth/n);return t.itemsListRef&&["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach((function(e){t.itemsListRef.style[e]=(0,l.default)(a,"%",t.props.axis)})),!0})),b(m(t),"slideRight",(function(e){t.moveTo(t.state.firstItem-("number"===typeof e?e:1))})),b(m(t),"slideLeft",(function(e){t.moveTo(t.state.firstItem+("number"===typeof e?e:1))})),b(m(t),"moveTo",(function(e){e=(e=e<0?0:e)>=t.state.lastPosition?t.state.lastPosition:e,t.setState({firstItem:e})})),t.state={selectedItem:e.selectedItem,swiping:!1,showArrows:!1,firstItem:0,visibleItems:0,lastPosition:0},t}return t=d,(n=[{key:"componentDidMount",value:function(){this.setupThumbs()}},{key:"componentDidUpdate",value:function(e){this.props.selectedItem!==this.state.selectedItem&&this.setState({selectedItem:this.props.selectedItem,firstItem:this.getFirstItem(this.props.selectedItem)}),this.props.children!==e.children&&this.updateSizes()}},{key:"componentWillUnmount",value:function(){this.destroyThumbs()}},{key:"setupThumbs",value:function(){(0,s.default)().addEventListener("resize",this.updateSizes),(0,s.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.updateSizes()}},{key:"destroyThumbs",value:function(){(0,s.default)().removeEventListener("resize",this.updateSizes),(0,s.default)().removeEventListener("DOMContentLoaded",this.updateSizes)}},{key:"getFirstItem",value:function(e){var t=e;return e>=this.state.lastPosition&&(t=this.state.lastPosition),e<this.state.firstItem+this.state.visibleItems&&(t=this.state.firstItem),e<this.state.firstItem&&(t=e),t}},{key:"renderItems",value:function(){var e=this;return this.props.children.map((function(t,n){var i=o.default.ITEM(!1,n===e.state.selectedItem),l={key:n,ref:function(t){return e.setThumbsRef(t,n)},className:i,onClick:e.handleClickItem.bind(e,n,e.props.children[n]),onKeyDown:e.handleClickItem.bind(e,n,e.props.children[n]),"aria-label":"".concat(e.props.labels.item," ").concat(n+1),style:{width:e.props.thumbWidth}};return r.default.createElement("li",h({},l,{role:"button",tabIndex:0}),t)}))}},{key:"render",value:function(){var e=this;if(!this.props.children)return null;var t,n=r.Children.count(this.props.children)>1,i=this.state.showArrows&&this.state.firstItem>0,s=this.state.showArrows&&this.state.firstItem<this.state.lastPosition,c=-this.state.firstItem*(this.state.itemSize||0),u=(0,l.default)(c,"px",this.props.axis),d=this.props.transitionTime+"ms";return t={WebkitTransform:u,MozTransform:u,MsTransform:u,OTransform:u,transform:u,msTransform:u,WebkitTransitionDuration:d,MozTransitionDuration:d,MsTransitionDuration:d,OTransitionDuration:d,transitionDuration:d,msTransitionDuration:d},r.default.createElement("div",{className:o.default.CAROUSEL(!1)},r.default.createElement("div",{className:o.default.WRAPPER(!1),ref:this.setItemsWrapperRef},r.default.createElement("button",{type:"button",className:o.default.ARROW_PREV(!i),onClick:function(){return e.slideRight()},"aria-label":this.props.labels.leftArrow}),n?r.default.createElement(a.default,{tagName:"ul",className:o.default.SLIDER(!1,this.state.swiping),onSwipeLeft:this.slideLeft,onSwipeRight:this.slideRight,onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:t,innerRef:this.setItemsListRef,allowMouseEvents:this.props.emulateTouch},this.renderItems()):r.default.createElement("ul",{className:o.default.SLIDER(!1,this.state.swiping),ref:function(t){return e.setItemsListRef(t)},style:t},this.renderItems()),r.default.createElement("button",{type:"button",className:o.default.ARROW_NEXT(!s),onClick:function(){return e.slideLeft()},"aria-label":this.props.labels.rightArrow})))}}])&&p(t.prototype,n),c&&p(t,c),d}(r.Component);t.default=y,b(y,"displayName","Thumbs"),b(y,"defaultProps",{axis:"horizontal",labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},selectedItem:0,thumbWidth:80,transitionTime:350})},75385:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,o=(r=n(23175))&&r.__esModule?r:{default:r};var i={ROOT:function(e){return(0,o.default)(function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({"carousel-root":!0},e||"",!!e))},CAROUSEL:function(e){return(0,o.default)({carousel:!0,"carousel-slider":e})},WRAPPER:function(e,t){return(0,o.default)({"thumbs-wrapper":!e,"slider-wrapper":e,"axis-horizontal":"horizontal"===t,"axis-vertical":"horizontal"!==t})},SLIDER:function(e,t){return(0,o.default)({thumbs:!e,slider:e,animated:!t})},ITEM:function(e,t,n){return(0,o.default)({thumb:!e,slide:e,selected:t,previous:n})},ARROW_PREV:function(e){return(0,o.default)({"control-arrow control-prev":!0,"control-disabled":e})},ARROW_NEXT:function(e){return(0,o.default)({"control-arrow control-next":!0,"control-disabled":e})},DOT:function(e){return(0,o.default)({dot:!0,selected:e})}};t.default=i},24202:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.outerWidth=void 0;t.outerWidth=function(e){var t=e.offsetWidth,n=getComputedStyle(e);return t+=parseInt(n.marginLeft)+parseInt(n.marginRight)}},44303:(e,t,n)=>{"use strict";Object.defineProperty(t,"lr",{enumerable:!0,get:function(){return r.default}});var r=l(n(42852)),o=n(96239),i=l(n(91935));function l(e){return e&&e.__esModule?e:{default:e}}},39896:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(){return document}},22124:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(){return window}},52347:(e,t,n)=>{var r;!function(){"use strict";var o={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function i(e){return function(e,t){var n,r,l,a,s,c,u,d,h,p=1,f=e.length,g="";for(r=0;r<f;r++)if("string"===typeof e[r])g+=e[r];else if("object"===typeof e[r]){if((a=e[r]).keys)for(n=t[p],l=0;l<a.keys.length;l++){if(void 0==n)throw new Error(i('[sprintf] Cannot access property "%s" of undefined value "%s"',a.keys[l],a.keys[l-1]));n=n[a.keys[l]]}else n=a.param_no?t[a.param_no]:t[p++];if(o.not_type.test(a.type)&&o.not_primitive.test(a.type)&&n instanceof Function&&(n=n()),o.numeric_arg.test(a.type)&&"number"!==typeof n&&isNaN(n))throw new TypeError(i("[sprintf] expecting number but found %T",n));switch(o.number.test(a.type)&&(d=n>=0),a.type){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,a.width?parseInt(a.width):0);break;case"e":n=a.precision?parseFloat(n).toExponential(a.precision):parseFloat(n).toExponential();break;case"f":n=a.precision?parseFloat(n).toFixed(a.precision):parseFloat(n);break;case"g":n=a.precision?String(Number(n.toPrecision(a.precision))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=a.precision?n.substring(0,a.precision):n;break;case"t":n=String(!!n),n=a.precision?n.substring(0,a.precision):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=a.precision?n.substring(0,a.precision):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=a.precision?n.substring(0,a.precision):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}o.json.test(a.type)?g+=n:(!o.number.test(a.type)||d&&!a.sign?h="":(h=d?"+":"-",n=n.toString().replace(o.sign,"")),c=a.pad_char?"0"===a.pad_char?"0":a.pad_char.charAt(1):" ",u=a.width-(h+n).length,s=a.width&&u>0?c.repeat(u):"",g+=a.align?h+n+s:"0"===c?h+s+n:s+h+n)}return g}(function(e){if(a[e])return a[e];var t,n=e,r=[],i=0;for(;n;){if(null!==(t=o.text.exec(n)))r.push(t[0]);else if(null!==(t=o.modulo.exec(n)))r.push("%");else{if(null===(t=o.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(t[2]){i|=1;var l=[],s=t[2],c=[];if(null===(c=o.key.exec(s)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(l.push(c[1]);""!==(s=s.substring(c[0].length));)if(null!==(c=o.key_access.exec(s)))l.push(c[1]);else{if(null===(c=o.index_access.exec(s)))throw new SyntaxError("[sprintf] failed to parse named argument key");l.push(c[1])}t[2]=l}else i|=2;if(3===i)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");r.push({placeholder:t[0],param_no:t[1],keys:t[2],sign:t[3],pad_char:t[4],align:t[5],width:t[6],precision:t[7],type:t[8]})}n=n.substring(t[0].length)}return a[e]=r}(e),arguments)}function l(e,t){return i.apply(null,[e].concat(t||[]))}var a=Object.create(null);t.sprintf=i,t.vsprintf=l,"undefined"!==typeof window&&(window.sprintf=i,window.vsprintf=l,void 0===(r=function(){return{sprintf:i,vsprintf:l}}.call(t,n,t,e))||(e.exports=r))}()},24665:()=>{},2739:()=>{},22951:(e,t,n)=>{"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,{Z:()=>r})},91976:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(55217);function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,(0,r.Z)(o.key),o)}}function i(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}},67591:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(6983);function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,r.Z)(e,t)}},65809:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(64013);function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,l,a=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(a.push(r.value),a.length!==t);s=!0);}catch(u){c=!0,o=u}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw o}}return a}}(e,t)||(0,r.Z)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},67487:(e,t,n)=>{"use strict";n.d(t,{d:()=>u});var r=n(66845);var o=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|inert|itemProp|itemScope|itemType|itemID|itemRef|on|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,i=function(e){var t={};return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}((function(e){return o.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),l=function(){const e=Array.prototype.slice.call(arguments).filter(Boolean),t={},n=[];e.forEach((e=>{(e?e.split(" "):[]).forEach((e=>{if(e.startsWith("atm_")){const[,n]=e.split("_");t[n]=e}else n.push(e)}))}));const r=[];for(const o in t)Object.prototype.hasOwnProperty.call(t,o)&&r.push(t[o]);return r.push(...n),r.join(" ")},a=e=>e.toUpperCase()===e,s=(e,t)=>{const n={};return Object.keys(e).filter((e=>t=>-1===e.indexOf(t))(t)).forEach((t=>{n[t]=e[t]})),n};var c=(e,t)=>{{if("string"===typeof e||"number"===typeof e&&isFinite(e))return;const n="object"===typeof e?JSON.stringify(e):String(e);console.warn("An interpolation evaluated to '".concat(n,"' in the component '").concat(t,"', which is probably a mistake. You should explicitly cast or transform the value to a string."))}};var u=new Proxy((function(e){return t=>{if(Array.isArray(t))throw new Error('Using the "styled" tag in runtime is not supported. Make sure you have set up the Babel plugin correctly. See https://github.com/callstack/linaria#setup');const n=(n,o)=>{const{as:u=e,class:d}=n,h=function(e,t,n){const r=s(t,n);return"string"!==typeof e||-1!==e.indexOf("-")||a(e[0])||Object.keys(r).forEach((e=>{i(e)||delete r[e]})),r}(u,n,["as","class"]);h.ref=o,h.className=t.atomic?l(t.class,h.className||d):l(h.className||d,t.class);const{vars:p}=t;if(p){const e={};for(const i in p){const r=p[i],o=r[0],l=r[1]||"",a="function"===typeof o?o(n):o;c(a,t.name),e["--".concat(i)]="".concat(a).concat(l)}const r=h.style||{},o=Object.keys(r);o.length>0&&o.forEach((t=>{e[t]=r[t]})),h.style=e}return e.__linaria&&e!==u?(h.as=u,r.createElement(e,h)):r.createElement(u,h)},o=r.forwardRef?r.forwardRef(n):e=>{const t=s(e,["innerRef"]);return n(t,e.innerRef)};return o.displayName=t.name,o.__linaria={className:t.class,extends:e},o}}),{get:(e,t)=>e(t)})},94379:(e,t,n)=>{"use strict";n.d(t,{Bn:()=>qt});var r=n(67487),o=n(35396),i=n(66845),l=n(25773),a=n(7865),s=n(50669),c=n(53782),u=n(65809),d=n(33940),h=n(22951),p=n(91976),f=n(67591),g=n(64649),m=n(17664);function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){v(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function w(e){return w=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},w(e)}function x(e,t){return!t||"object"!==typeof t&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function k(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=w(e);if(t){var o=w(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return x(this,n)}}var S=["className","clearValue","cx","getStyles","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],C=function(){};function M(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function E(e,t,n){var r=[n];if(t&&e)for(var o in t)t.hasOwnProperty(o)&&t[o]&&r.push("".concat(M(e,o)));return r.filter((function(e){return e})).map((function(e){return String(e).trim()})).join(" ")}var R=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===(0,d.Z)(e)&&null!==e?[e]:[];var t},T=function(e){return e.className,e.clearValue,e.cx,e.getStyles,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme,y({},(0,c.Z)(e,S))};function I(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function O(e){return I(e)?window.pageYOffset:e.scrollTop}function P(e,t){I(e)?window.scrollTo(0,t):e.scrollTop=t}function D(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:C,o=O(e),i=t-o,l=0;!function t(){var a,s=i*((a=(a=l+=10)/n-1)*a*a+1)+o;P(e,s),l<n?window.requestAnimationFrame(t):r(e)}()}function H(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}var L=!1,z={get passive(){return L=!0}},F="undefined"!==typeof window?window:{};F.addEventListener&&F.removeEventListener&&(F.addEventListener("p",C,z),F.removeEventListener("p",C,!1));var A=L;function _(e){return null!=e}function V(e,t,n){return e?t:n}function N(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,o=e.placement,i=e.shouldScroll,l=e.isFixedPosition,a=e.theme.spacing,s=function(e){var t=getComputedStyle(e),n="absolute"===t.position,r=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var o=e;o=o.parentElement;)if(t=getComputedStyle(o),(!n||"static"!==t.position)&&r.test(t.overflow+t.overflowY+t.overflowX))return o;return document.documentElement}(n),c={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return c;var u,d=s.getBoundingClientRect().height,h=n.getBoundingClientRect(),p=h.bottom,f=h.height,g=h.top,m=n.offsetParent.getBoundingClientRect().top,v=l?window.innerHeight:I(u=s)?window.innerHeight:u.clientHeight,b=O(s),y=parseInt(getComputedStyle(n).marginBottom,10),w=parseInt(getComputedStyle(n).marginTop,10),x=m-w,k=v-g,S=x+b,C=d-b-g,M=p-v+b+y,E=b+g-w,R=160;switch(o){case"auto":case"bottom":if(k>=f)return{placement:"bottom",maxHeight:t};if(C>=f&&!l)return i&&D(s,M,R),{placement:"bottom",maxHeight:t};if(!l&&C>=r||l&&k>=r)return i&&D(s,M,R),{placement:"bottom",maxHeight:l?k-y:C-y};if("auto"===o||l){var T=t,H=l?x:S;return H>=r&&(T=Math.min(H-y-a.controlHeight,t)),{placement:"top",maxHeight:T}}if("bottom"===o)return i&&P(s,M),{placement:"bottom",maxHeight:t};break;case"top":if(x>=f)return{placement:"top",maxHeight:t};if(S>=f&&!l)return i&&D(s,E,R),{placement:"top",maxHeight:t};if(!l&&S>=r||l&&x>=r){var L=t;return(!l&&S>=r||l&&x>=r)&&(L=l?x-w:S-w),i&&D(s,E,R),{placement:"top",maxHeight:L}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(o,'".'))}return c}var B=function(e){return"auto"===e?"bottom":e},W=(0,i.createContext)({getPortalPlacement:null}),j=function(e){(0,f.Z)(n,e);var t=k(n);function n(){var e;(0,h.Z)(this,n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return(e=t.call.apply(t,[this].concat(o))).state={maxHeight:e.props.maxMenuHeight,placement:null},e.context=void 0,e.getPlacement=function(t){var n=e.props,r=n.minMenuHeight,o=n.maxMenuHeight,i=n.menuPlacement,l=n.menuPosition,a=n.menuShouldScrollIntoView,s=n.theme;if(t){var c="fixed"===l,u=N({maxHeight:o,menuEl:t,minHeight:r,placement:i,shouldScroll:a&&!c,isFixedPosition:c,theme:s}),d=e.context.getPortalPlacement;d&&d(u),e.setState(u)}},e.getUpdatedProps=function(){var t=e.props.menuPlacement,n=e.state.placement||B(t);return y(y({},e.props),{},{placement:n,maxHeight:e.state.maxHeight})},e}return(0,p.Z)(n,[{key:"render",value:function(){return(0,this.props.children)({ref:this.getPlacement,placerProps:this.getUpdatedProps()})}}]),n}(i.Component);j.contextType=W;var Z=function(e){var t=e.theme,n=t.spacing.baseUnit;return{color:t.colors.neutral40,padding:"".concat(2*n,"px ").concat(3*n,"px"),textAlign:"center"}},U=Z,X=Z,Y=function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({css:o("noOptionsMessage",e),className:r({"menu-notice":!0,"menu-notice--no-options":!0},n)},i),t)};Y.defaultProps={children:"No options"};var K=function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({css:o("loadingMessage",e),className:r({"menu-notice":!0,"menu-notice--loading":!0},n)},i),t)};K.defaultProps={children:"Loading..."};var $,G=function(e){(0,f.Z)(n,e);var t=k(n);function n(){var e;(0,h.Z)(this,n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return(e=t.call.apply(t,[this].concat(o))).state={placement:null},e.getPortalPlacement=function(t){var n=t.placement;n!==B(e.props.menuPlacement)&&e.setState({placement:n})},e}return(0,p.Z)(n,[{key:"render",value:function(){var e=this.props,t=e.appendTo,n=e.children,r=e.className,o=e.controlElement,i=e.cx,s=e.innerProps,c=e.menuPlacement,u=e.menuPosition,d=e.getStyles,h="fixed"===u;if(!t&&!h||!o)return null;var p=this.state.placement||B(c),f=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(o),g=h?0:window.pageYOffset,v={offset:f[p]+g,position:u,rect:f},b=(0,a.tZ)("div",(0,l.Z)({css:d("menuPortal",v),className:i({"menu-portal":!0},r)},s),n);return(0,a.tZ)(W.Provider,{value:{getPortalPlacement:this.getPortalPlacement}},t?(0,m.createPortal)(b,t):b)}}]),n}(i.Component),q=["size"];var Q={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},J=function(e){var t=e.size,n=(0,c.Z)(e,q);return(0,a.tZ)("svg",(0,l.Z)({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:Q},n))},ee=function(e){return(0,a.tZ)(J,(0,l.Z)({size:20},e),(0,a.tZ)("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},te=function(e){return(0,a.tZ)(J,(0,l.Z)({size:20},e),(0,a.tZ)("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},ne=function(e){var t=e.isFocused,n=e.theme,r=n.spacing.baseUnit,o=n.colors;return{label:"indicatorContainer",color:t?o.neutral60:o.neutral20,display:"flex",padding:2*r,transition:"color 150ms",":hover":{color:t?o.neutral80:o.neutral40}}},re=ne,oe=ne,ie=(0,a.F4)($||($=(0,s.Z)(["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"]))),le=function(e){var t=e.delay,n=e.offset;return(0,a.tZ)("span",{css:(0,a.iv)({animation:"".concat(ie," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:n?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","","","")})},ae=function(e){var t=e.className,n=e.cx,r=e.getStyles,o=e.innerProps,i=e.isRtl;return(0,a.tZ)("div",(0,l.Z)({css:r("loadingIndicator",e),className:n({indicator:!0,"loading-indicator":!0},t)},o),(0,a.tZ)(le,{delay:0,offset:i}),(0,a.tZ)(le,{delay:160,offset:!0}),(0,a.tZ)(le,{delay:320,offset:!i}))};ae.defaultProps={size:4};var se=["data"],ce=["innerRef","isDisabled","isHidden","inputClassName"],ue={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},de={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":y({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},ue)},he=function(e){return y({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},ue)},pe=function(e){var t=e.children,n=e.innerProps;return(0,a.tZ)("div",n,t)};var fe={ClearIndicator:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({css:o("clearIndicator",e),className:r({indicator:!0,"clear-indicator":!0},n)},i),t||(0,a.tZ)(ee,null))},Control:function(e){var t=e.children,n=e.cx,r=e.getStyles,o=e.className,i=e.isDisabled,s=e.isFocused,c=e.innerRef,u=e.innerProps,d=e.menuIsOpen;return(0,a.tZ)("div",(0,l.Z)({ref:c,css:r("control",e),className:n({control:!0,"control--is-disabled":i,"control--is-focused":s,"control--menu-is-open":d},o)},u),t)},DropdownIndicator:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({css:o("dropdownIndicator",e),className:r({indicator:!0,"dropdown-indicator":!0},n)},i),t||(0,a.tZ)(te,null))},DownChevron:te,CrossIcon:ee,Group:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.Heading,s=e.headingProps,c=e.innerProps,u=e.label,d=e.theme,h=e.selectProps;return(0,a.tZ)("div",(0,l.Z)({css:o("group",e),className:r({group:!0},n)},c),(0,a.tZ)(i,(0,l.Z)({},s,{selectProps:h,theme:d,getStyles:o,cx:r}),u),(0,a.tZ)("div",null,t))},GroupHeading:function(e){var t=e.getStyles,n=e.cx,r=e.className,o=T(e);o.data;var i=(0,c.Z)(o,se);return(0,a.tZ)("div",(0,l.Z)({css:t("groupHeading",e),className:n({"group-heading":!0},r)},i))},IndicatorsContainer:function(e){var t=e.children,n=e.className,r=e.cx,o=e.innerProps,i=e.getStyles;return(0,a.tZ)("div",(0,l.Z)({css:i("indicatorsContainer",e),className:r({indicators:!0},n)},o),t)},IndicatorSeparator:function(e){var t=e.className,n=e.cx,r=e.getStyles,o=e.innerProps;return(0,a.tZ)("span",(0,l.Z)({},o,{css:r("indicatorSeparator",e),className:n({"indicator-separator":!0},t)}))},Input:function(e){var t=e.className,n=e.cx,r=e.getStyles,o=e.value,i=T(e),s=i.innerRef,u=i.isDisabled,d=i.isHidden,h=i.inputClassName,p=(0,c.Z)(i,ce);return(0,a.tZ)("div",{className:n({"input-container":!0},t),css:r("input",e),"data-value":o||""},(0,a.tZ)("input",(0,l.Z)({className:n({input:!0},h),ref:s,style:he(d),disabled:u},p)))},LoadingIndicator:ae,Menu:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.innerRef,s=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({css:o("menu",e),className:r({menu:!0},n),ref:i},s),t)},MenuList:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.innerProps,s=e.innerRef,c=e.isMulti;return(0,a.tZ)("div",(0,l.Z)({css:o("menuList",e),className:r({"menu-list":!0,"menu-list--is-multi":c},n),ref:s},i),t)},MenuPortal:G,LoadingMessage:K,NoOptionsMessage:Y,MultiValue:function(e){var t=e.children,n=e.className,r=e.components,o=e.cx,i=e.data,l=e.getStyles,s=e.innerProps,c=e.isDisabled,u=e.removeProps,d=e.selectProps,h=r.Container,p=r.Label,f=r.Remove;return(0,a.tZ)(a.ms,null,(function(r){var g=r.css,m=r.cx;return(0,a.tZ)(h,{data:i,innerProps:y({className:m(g(l("multiValue",e)),o({"multi-value":!0,"multi-value--is-disabled":c},n))},s),selectProps:d},(0,a.tZ)(p,{data:i,innerProps:{className:m(g(l("multiValueLabel",e)),o({"multi-value__label":!0},n))},selectProps:d},t),(0,a.tZ)(f,{data:i,innerProps:y({className:m(g(l("multiValueRemove",e)),o({"multi-value__remove":!0},n)),"aria-label":"Remove ".concat(t||"option")},u),selectProps:d}))}))},MultiValueContainer:pe,MultiValueLabel:pe,MultiValueRemove:function(e){var t=e.children,n=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({role:"button"},n),t||(0,a.tZ)(ee,{size:14}))},Option:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.isDisabled,s=e.isFocused,c=e.isSelected,u=e.innerRef,d=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({css:o("option",e),className:r({option:!0,"option--is-disabled":i,"option--is-focused":s,"option--is-selected":c},n),ref:u,"aria-disabled":i},d),t)},Placeholder:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({css:o("placeholder",e),className:r({placeholder:!0},n)},i),t)},SelectContainer:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.innerProps,s=e.isDisabled,c=e.isRtl;return(0,a.tZ)("div",(0,l.Z)({css:o("container",e),className:r({"--is-disabled":s,"--is-rtl":c},n)},i),t)},SingleValue:function(e){var t=e.children,n=e.className,r=e.cx,o=e.getStyles,i=e.isDisabled,s=e.innerProps;return(0,a.tZ)("div",(0,l.Z)({css:o("singleValue",e),className:r({"single-value":!0,"single-value--is-disabled":i},n)},s),t)},ValueContainer:function(e){var t=e.children,n=e.className,r=e.cx,o=e.innerProps,i=e.isMulti,s=e.getStyles,c=e.hasValue;return(0,a.tZ)("div",(0,l.Z)({css:s("valueContainer",e),className:r({"value-container":!0,"value-container--is-multi":i,"value-container--has-value":c},n)},o),t)}},ge=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];var me=n(30126),ve=n(61687);for(var be={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},ye=function(e){return(0,a.tZ)("span",(0,l.Z)({css:be},e))},we={guidance:function(e){var t=e.isSearchable,n=e.isMulti,r=e.isDisabled,o=e.tabSelectsValue;switch(e.context){case"menu":return"Use Up and Down to choose options".concat(r?"":", press Enter to select the currently focused option",", press Escape to exit the menu").concat(o?", press Tab to select the option and exit the menu":"",".");case"input":return"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(n?" press left to focus selected values":"");case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,n=e.label,r=void 0===n?"":n,o=e.labels,i=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(r,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return"option ".concat(r,i?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,n=e.focused,r=e.options,o=e.label,i=void 0===o?"":o,l=e.selectValue,a=e.isDisabled,s=e.isSelected,c=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&l)return"value ".concat(i," focused, ").concat(c(l,n),".");if("menu"===t){var u=a?" disabled":"",d="".concat(s?"selected":"focused").concat(u);return"option ".concat(i," ").concat(d,", ").concat(c(r,n),".")}return""},onFilter:function(e){var t=e.inputValue,n=e.resultsMessage;return"".concat(n).concat(t?" for search term "+t:"",".")}},xe=function(e){var t=e.ariaSelection,n=e.focusedOption,r=e.focusedValue,o=e.focusableOptions,l=e.isFocused,s=e.selectValue,c=e.selectProps,u=e.id,d=c.ariaLiveMessages,h=c.getOptionLabel,p=c.inputValue,f=c.isMulti,g=c.isOptionDisabled,m=c.isSearchable,v=c.menuIsOpen,b=c.options,w=c.screenReaderStatus,x=c.tabSelectsValue,k=c["aria-label"],S=c["aria-live"],C=(0,i.useMemo)((function(){return y(y({},we),d||{})}),[d]),M=(0,i.useMemo)((function(){var e,n="";if(t&&C.onChange){var r=t.option,o=t.options,i=t.removedValue,l=t.removedValues,a=t.value,c=i||r||(e=a,Array.isArray(e)?null:e),u=c?h(c):"",d=o||l||void 0,p=d?d.map(h):[],f=y({isDisabled:c&&g(c,s),label:u,labels:p},t);n=C.onChange(f)}return n}),[t,C,g,s,h]),E=(0,i.useMemo)((function(){var e="",t=n||r,o=!!(n&&s&&s.includes(n));if(t&&C.onFocus){var i={focused:t,label:h(t),isDisabled:g(t,s),isSelected:o,options:b,context:t===n?"menu":"value",selectValue:s};e=C.onFocus(i)}return e}),[n,r,h,g,C,b,s]),R=(0,i.useMemo)((function(){var e="";if(v&&b.length&&C.onFilter){var t=w({count:o.length});e=C.onFilter({inputValue:p,resultsMessage:t})}return e}),[o,p,v,C,b,w]),T=(0,i.useMemo)((function(){var e="";if(C.guidance){var t=r?"value":v?"menu":"input";e=C.guidance({"aria-label":k,context:t,isDisabled:n&&g(n,s),isMulti:f,isSearchable:m,tabSelectsValue:x})}return e}),[k,n,r,f,g,m,v,C,s,x]),I="".concat(E," ").concat(R," ").concat(T),O=(0,a.tZ)(i.Fragment,null,(0,a.tZ)("span",{id:"aria-selection"},M),(0,a.tZ)("span",{id:"aria-context"},I)),P="initial-input-focus"===(null===t||void 0===t?void 0:t.action);return(0,a.tZ)(i.Fragment,null,(0,a.tZ)(ye,{id:u},P&&O),(0,a.tZ)(ye,{"aria-live":S,"aria-atomic":"false","aria-relevant":"additions text"},l&&!P&&O))},ke=[{base:"A",letters:"A\u24b6\uff21\xc0\xc1\xc2\u1ea6\u1ea4\u1eaa\u1ea8\xc3\u0100\u0102\u1eb0\u1eae\u1eb4\u1eb2\u0226\u01e0\xc4\u01de\u1ea2\xc5\u01fa\u01cd\u0200\u0202\u1ea0\u1eac\u1eb6\u1e00\u0104\u023a\u2c6f"},{base:"AA",letters:"\ua732"},{base:"AE",letters:"\xc6\u01fc\u01e2"},{base:"AO",letters:"\ua734"},{base:"AU",letters:"\ua736"},{base:"AV",letters:"\ua738\ua73a"},{base:"AY",letters:"\ua73c"},{base:"B",letters:"B\u24b7\uff22\u1e02\u1e04\u1e06\u0243\u0182\u0181"},{base:"C",letters:"C\u24b8\uff23\u0106\u0108\u010a\u010c\xc7\u1e08\u0187\u023b\ua73e"},{base:"D",letters:"D\u24b9\uff24\u1e0a\u010e\u1e0c\u1e10\u1e12\u1e0e\u0110\u018b\u018a\u0189\ua779"},{base:"DZ",letters:"\u01f1\u01c4"},{base:"Dz",letters:"\u01f2\u01c5"},{base:"E",letters:"E\u24ba\uff25\xc8\xc9\xca\u1ec0\u1ebe\u1ec4\u1ec2\u1ebc\u0112\u1e14\u1e16\u0114\u0116\xcb\u1eba\u011a\u0204\u0206\u1eb8\u1ec6\u0228\u1e1c\u0118\u1e18\u1e1a\u0190\u018e"},{base:"F",letters:"F\u24bb\uff26\u1e1e\u0191\ua77b"},{base:"G",letters:"G\u24bc\uff27\u01f4\u011c\u1e20\u011e\u0120\u01e6\u0122\u01e4\u0193\ua7a0\ua77d\ua77e"},{base:"H",letters:"H\u24bd\uff28\u0124\u1e22\u1e26\u021e\u1e24\u1e28\u1e2a\u0126\u2c67\u2c75\ua78d"},{base:"I",letters:"I\u24be\uff29\xcc\xcd\xce\u0128\u012a\u012c\u0130\xcf\u1e2e\u1ec8\u01cf\u0208\u020a\u1eca\u012e\u1e2c\u0197"},{base:"J",letters:"J\u24bf\uff2a\u0134\u0248"},{base:"K",letters:"K\u24c0\uff2b\u1e30\u01e8\u1e32\u0136\u1e34\u0198\u2c69\ua740\ua742\ua744\ua7a2"},{base:"L",letters:"L\u24c1\uff2c\u013f\u0139\u013d\u1e36\u1e38\u013b\u1e3c\u1e3a\u0141\u023d\u2c62\u2c60\ua748\ua746\ua780"},{base:"LJ",letters:"\u01c7"},{base:"Lj",letters:"\u01c8"},{base:"M",letters:"M\u24c2\uff2d\u1e3e\u1e40\u1e42\u2c6e\u019c"},{base:"N",letters:"N\u24c3\uff2e\u01f8\u0143\xd1\u1e44\u0147\u1e46\u0145\u1e4a\u1e48\u0220\u019d\ua790\ua7a4"},{base:"NJ",letters:"\u01ca"},{base:"Nj",letters:"\u01cb"},{base:"O",letters:"O\u24c4\uff2f\xd2\xd3\xd4\u1ed2\u1ed0\u1ed6\u1ed4\xd5\u1e4c\u022c\u1e4e\u014c\u1e50\u1e52\u014e\u022e\u0230\xd6\u022a\u1ece\u0150\u01d1\u020c\u020e\u01a0\u1edc\u1eda\u1ee0\u1ede\u1ee2\u1ecc\u1ed8\u01ea\u01ec\xd8\u01fe\u0186\u019f\ua74a\ua74c"},{base:"OI",letters:"\u01a2"},{base:"OO",letters:"\ua74e"},{base:"OU",letters:"\u0222"},{base:"P",letters:"P\u24c5\uff30\u1e54\u1e56\u01a4\u2c63\ua750\ua752\ua754"},{base:"Q",letters:"Q\u24c6\uff31\ua756\ua758\u024a"},{base:"R",letters:"R\u24c7\uff32\u0154\u1e58\u0158\u0210\u0212\u1e5a\u1e5c\u0156\u1e5e\u024c\u2c64\ua75a\ua7a6\ua782"},{base:"S",letters:"S\u24c8\uff33\u1e9e\u015a\u1e64\u015c\u1e60\u0160\u1e66\u1e62\u1e68\u0218\u015e\u2c7e\ua7a8\ua784"},{base:"T",letters:"T\u24c9\uff34\u1e6a\u0164\u1e6c\u021a\u0162\u1e70\u1e6e\u0166\u01ac\u01ae\u023e\ua786"},{base:"TZ",letters:"\ua728"},{base:"U",letters:"U\u24ca\uff35\xd9\xda\xdb\u0168\u1e78\u016a\u1e7a\u016c\xdc\u01db\u01d7\u01d5\u01d9\u1ee6\u016e\u0170\u01d3\u0214\u0216\u01af\u1eea\u1ee8\u1eee\u1eec\u1ef0\u1ee4\u1e72\u0172\u1e76\u1e74\u0244"},{base:"V",letters:"V\u24cb\uff36\u1e7c\u1e7e\u01b2\ua75e\u0245"},{base:"VY",letters:"\ua760"},{base:"W",letters:"W\u24cc\uff37\u1e80\u1e82\u0174\u1e86\u1e84\u1e88\u2c72"},{base:"X",letters:"X\u24cd\uff38\u1e8a\u1e8c"},{base:"Y",letters:"Y\u24ce\uff39\u1ef2\xdd\u0176\u1ef8\u0232\u1e8e\u0178\u1ef6\u1ef4\u01b3\u024e\u1efe"},{base:"Z",letters:"Z\u24cf\uff3a\u0179\u1e90\u017b\u017d\u1e92\u1e94\u01b5\u0224\u2c7f\u2c6b\ua762"},{base:"a",letters:"a\u24d0\uff41\u1e9a\xe0\xe1\xe2\u1ea7\u1ea5\u1eab\u1ea9\xe3\u0101\u0103\u1eb1\u1eaf\u1eb5\u1eb3\u0227\u01e1\xe4\u01df\u1ea3\xe5\u01fb\u01ce\u0201\u0203\u1ea1\u1ead\u1eb7\u1e01\u0105\u2c65\u0250"},{base:"aa",letters:"\ua733"},{base:"ae",letters:"\xe6\u01fd\u01e3"},{base:"ao",letters:"\ua735"},{base:"au",letters:"\ua737"},{base:"av",letters:"\ua739\ua73b"},{base:"ay",letters:"\ua73d"},{base:"b",letters:"b\u24d1\uff42\u1e03\u1e05\u1e07\u0180\u0183\u0253"},{base:"c",letters:"c\u24d2\uff43\u0107\u0109\u010b\u010d\xe7\u1e09\u0188\u023c\ua73f\u2184"},{base:"d",letters:"d\u24d3\uff44\u1e0b\u010f\u1e0d\u1e11\u1e13\u1e0f\u0111\u018c\u0256\u0257\ua77a"},{base:"dz",letters:"\u01f3\u01c6"},{base:"e",letters:"e\u24d4\uff45\xe8\xe9\xea\u1ec1\u1ebf\u1ec5\u1ec3\u1ebd\u0113\u1e15\u1e17\u0115\u0117\xeb\u1ebb\u011b\u0205\u0207\u1eb9\u1ec7\u0229\u1e1d\u0119\u1e19\u1e1b\u0247\u025b\u01dd"},{base:"f",letters:"f\u24d5\uff46\u1e1f\u0192\ua77c"},{base:"g",letters:"g\u24d6\uff47\u01f5\u011d\u1e21\u011f\u0121\u01e7\u0123\u01e5\u0260\ua7a1\u1d79\ua77f"},{base:"h",letters:"h\u24d7\uff48\u0125\u1e23\u1e27\u021f\u1e25\u1e29\u1e2b\u1e96\u0127\u2c68\u2c76\u0265"},{base:"hv",letters:"\u0195"},{base:"i",letters:"i\u24d8\uff49\xec\xed\xee\u0129\u012b\u012d\xef\u1e2f\u1ec9\u01d0\u0209\u020b\u1ecb\u012f\u1e2d\u0268\u0131"},{base:"j",letters:"j\u24d9\uff4a\u0135\u01f0\u0249"},{base:"k",letters:"k\u24da\uff4b\u1e31\u01e9\u1e33\u0137\u1e35\u0199\u2c6a\ua741\ua743\ua745\ua7a3"},{base:"l",letters:"l\u24db\uff4c\u0140\u013a\u013e\u1e37\u1e39\u013c\u1e3d\u1e3b\u017f\u0142\u019a\u026b\u2c61\ua749\ua781\ua747"},{base:"lj",letters:"\u01c9"},{base:"m",letters:"m\u24dc\uff4d\u1e3f\u1e41\u1e43\u0271\u026f"},{base:"n",letters:"n\u24dd\uff4e\u01f9\u0144\xf1\u1e45\u0148\u1e47\u0146\u1e4b\u1e49\u019e\u0272\u0149\ua791\ua7a5"},{base:"nj",letters:"\u01cc"},{base:"o",letters:"o\u24de\uff4f\xf2\xf3\xf4\u1ed3\u1ed1\u1ed7\u1ed5\xf5\u1e4d\u022d\u1e4f\u014d\u1e51\u1e53\u014f\u022f\u0231\xf6\u022b\u1ecf\u0151\u01d2\u020d\u020f\u01a1\u1edd\u1edb\u1ee1\u1edf\u1ee3\u1ecd\u1ed9\u01eb\u01ed\xf8\u01ff\u0254\ua74b\ua74d\u0275"},{base:"oi",letters:"\u01a3"},{base:"ou",letters:"\u0223"},{base:"oo",letters:"\ua74f"},{base:"p",letters:"p\u24df\uff50\u1e55\u1e57\u01a5\u1d7d\ua751\ua753\ua755"},{base:"q",letters:"q\u24e0\uff51\u024b\ua757\ua759"},{base:"r",letters:"r\u24e1\uff52\u0155\u1e59\u0159\u0211\u0213\u1e5b\u1e5d\u0157\u1e5f\u024d\u027d\ua75b\ua7a7\ua783"},{base:"s",letters:"s\u24e2\uff53\xdf\u015b\u1e65\u015d\u1e61\u0161\u1e67\u1e63\u1e69\u0219\u015f\u023f\ua7a9\ua785\u1e9b"},{base:"t",letters:"t\u24e3\uff54\u1e6b\u1e97\u0165\u1e6d\u021b\u0163\u1e71\u1e6f\u0167\u01ad\u0288\u2c66\ua787"},{base:"tz",letters:"\ua729"},{base:"u",letters:"u\u24e4\uff55\xf9\xfa\xfb\u0169\u1e79\u016b\u1e7b\u016d\xfc\u01dc\u01d8\u01d6\u01da\u1ee7\u016f\u0171\u01d4\u0215\u0217\u01b0\u1eeb\u1ee9\u1eef\u1eed\u1ef1\u1ee5\u1e73\u0173\u1e77\u1e75\u0289"},{base:"v",letters:"v\u24e5\uff56\u1e7d\u1e7f\u028b\ua75f\u028c"},{base:"vy",letters:"\ua761"},{base:"w",letters:"w\u24e6\uff57\u1e81\u1e83\u0175\u1e87\u1e85\u1e98\u1e89\u2c73"},{base:"x",letters:"x\u24e7\uff58\u1e8b\u1e8d"},{base:"y",letters:"y\u24e8\uff59\u1ef3\xfd\u0177\u1ef9\u0233\u1e8f\xff\u1ef7\u1e99\u1ef5\u01b4\u024f\u1eff"},{base:"z",letters:"z\u24e9\uff5a\u017a\u1e91\u017c\u017e\u1e93\u1e95\u01b6\u0225\u0240\u2c6c\ua763"}],Se=new RegExp("["+ke.map((function(e){return e.letters})).join("")+"]","g"),Ce={},Me=0;Me<ke.length;Me++)for(var Ee=ke[Me],Re=0;Re<Ee.letters.length;Re++)Ce[Ee.letters[Re]]=Ee.base;var Te=function(e){return e.replace(Se,(function(e){return Ce[e]}))},Ie=(0,ve.Z)(Te),Oe=function(e){return e.replace(/^\s+|\s+$/g,"")},Pe=function(e){return"".concat(e.label," ").concat(e.value)},De=["innerRef"];function He(e){var t=e.innerRef,n=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=Object.entries(e).filter((function(e){var t=(0,u.Z)(e,1)[0];return!n.includes(t)}));return o.reduce((function(e,t){var n=(0,u.Z)(t,2),r=n[0],o=n[1];return e[r]=o,e}),{})}((0,c.Z)(e,De),"onExited","in","enter","exit","appear");return(0,a.tZ)("input",(0,l.Z)({ref:t},n,{css:(0,a.iv)({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","","","")}))}var Le=function(e){e.preventDefault(),e.stopPropagation()};var ze=["boxSizing","height","overflow","paddingRight","position"],Fe={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function Ae(e){e.preventDefault()}function _e(e){e.stopPropagation()}function Ve(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;0===e?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function Ne(){return"ontouchstart"in window||navigator.maxTouchPoints}var Be=!("undefined"===typeof window||!window.document||!window.document.createElement),We=0,je={capture:!1,passive:!1};var Ze=function(){return document.activeElement&&document.activeElement.blur()},Ue={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function Xe(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,o=function(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,o=e.onTopArrive,l=e.onTopLeave,a=(0,i.useRef)(!1),s=(0,i.useRef)(!1),c=(0,i.useRef)(0),u=(0,i.useRef)(null),d=(0,i.useCallback)((function(e,t){if(null!==u.current){var i=u.current,c=i.scrollTop,d=i.scrollHeight,h=i.clientHeight,p=u.current,f=t>0,g=d-h-c,m=!1;g>t&&a.current&&(r&&r(e),a.current=!1),f&&s.current&&(l&&l(e),s.current=!1),f&&t>g?(n&&!a.current&&n(e),p.scrollTop=d,m=!0,a.current=!0):!f&&-t>c&&(o&&!s.current&&o(e),p.scrollTop=0,m=!0,s.current=!0),m&&Le(e)}}),[n,r,o,l]),h=(0,i.useCallback)((function(e){d(e,e.deltaY)}),[d]),p=(0,i.useCallback)((function(e){c.current=e.changedTouches[0].clientY}),[]),f=(0,i.useCallback)((function(e){var t=c.current-e.changedTouches[0].clientY;d(e,t)}),[d]),g=(0,i.useCallback)((function(e){if(e){var t=!!A&&{passive:!1};e.addEventListener("wheel",h,t),e.addEventListener("touchstart",p,t),e.addEventListener("touchmove",f,t)}}),[f,p,h]),m=(0,i.useCallback)((function(e){e&&(e.removeEventListener("wheel",h,!1),e.removeEventListener("touchstart",p,!1),e.removeEventListener("touchmove",f,!1))}),[f,p,h]);return(0,i.useEffect)((function(){if(t){var e=u.current;return g(e),function(){m(e)}}}),[t,g,m]),function(e){u.current=e}}({isEnabled:void 0===r||r,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),l=function(e){var t=e.isEnabled,n=e.accountForScrollbars,r=void 0===n||n,o=(0,i.useRef)({}),l=(0,i.useRef)(null),a=(0,i.useCallback)((function(e){if(Be){var t=document.body,n=t&&t.style;if(r&&ze.forEach((function(e){var t=n&&n[e];o.current[e]=t})),r&&We<1){var i=parseInt(o.current.paddingRight,10)||0,l=document.body?document.body.clientWidth:0,a=window.innerWidth-l+i||0;Object.keys(Fe).forEach((function(e){var t=Fe[e];n&&(n[e]=t)})),n&&(n.paddingRight="".concat(a,"px"))}t&&Ne()&&(t.addEventListener("touchmove",Ae,je),e&&(e.addEventListener("touchstart",Ve,je),e.addEventListener("touchmove",_e,je))),We+=1}}),[r]),s=(0,i.useCallback)((function(e){if(Be){var t=document.body,n=t&&t.style;We=Math.max(We-1,0),r&&We<1&&ze.forEach((function(e){var t=o.current[e];n&&(n[e]=t)})),t&&Ne()&&(t.removeEventListener("touchmove",Ae,je),e&&(e.removeEventListener("touchstart",Ve,je),e.removeEventListener("touchmove",_e,je)))}}),[r]);return(0,i.useEffect)((function(){if(t){var e=l.current;return a(e),function(){s(e)}}}),[t,a,s]),function(e){l.current=e}}({isEnabled:n});return(0,a.tZ)(i.Fragment,null,n&&(0,a.tZ)("div",{onClick:Ze,css:Ue}),t((function(e){o(e),l(e)})))}var Ye={clearIndicator:oe,container:function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},control:function(e){var t=e.isDisabled,n=e.isFocused,r=e.theme,o=r.colors,i=r.borderRadius,l=r.spacing;return{label:"control",alignItems:"center",backgroundColor:t?o.neutral5:o.neutral0,borderColor:t?o.neutral10:n?o.primary:o.neutral20,borderRadius:i,borderStyle:"solid",borderWidth:1,boxShadow:n?"0 0 0 1px ".concat(o.primary):void 0,cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:l.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms","&:hover":{borderColor:n?o.primary:o.neutral30}}},dropdownIndicator:re,group:function(e){var t=e.theme.spacing;return{paddingBottom:2*t.baseUnit,paddingTop:2*t.baseUnit}},groupHeading:function(e){var t=e.theme.spacing;return{label:"group",color:"#999",cursor:"default",display:"block",fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*t.baseUnit,paddingRight:3*t.baseUnit,textTransform:"uppercase"}},indicatorsContainer:function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},indicatorSeparator:function(e){var t=e.isDisabled,n=e.theme,r=n.spacing.baseUnit,o=n.colors;return{label:"indicatorSeparator",alignSelf:"stretch",backgroundColor:t?o.neutral10:o.neutral20,marginBottom:2*r,marginTop:2*r,width:1}},input:function(e){var t=e.isDisabled,n=e.value,r=e.theme,o=r.spacing,i=r.colors;return y({margin:o.baseUnit/2,paddingBottom:o.baseUnit/2,paddingTop:o.baseUnit/2,visibility:t?"hidden":"visible",color:i.neutral80,transform:n?"translateZ(0)":""},de)},loadingIndicator:function(e){var t=e.isFocused,n=e.size,r=e.theme,o=r.colors,i=r.spacing.baseUnit;return{label:"loadingIndicator",color:t?o.neutral60:o.neutral20,display:"flex",padding:2*i,transition:"color 150ms",alignSelf:"center",fontSize:n,lineHeight:1,marginRight:n,textAlign:"center",verticalAlign:"middle"}},loadingMessage:X,menu:function(e){var t,n=e.placement,r=e.theme,o=r.borderRadius,i=r.spacing,l=r.colors;return t={label:"menu"},(0,g.Z)(t,function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(n),"100%"),(0,g.Z)(t,"backgroundColor",l.neutral0),(0,g.Z)(t,"borderRadius",o),(0,g.Z)(t,"boxShadow","0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)"),(0,g.Z)(t,"marginBottom",i.menuGutter),(0,g.Z)(t,"marginTop",i.menuGutter),(0,g.Z)(t,"position","absolute"),(0,g.Z)(t,"width","100%"),(0,g.Z)(t,"zIndex",1),t},menuList:function(e){var t=e.maxHeight,n=e.theme.spacing.baseUnit;return{maxHeight:t,overflowY:"auto",paddingBottom:n,paddingTop:n,position:"relative",WebkitOverflowScrolling:"touch"}},menuPortal:function(e){var t=e.rect,n=e.offset,r=e.position;return{left:t.left,position:r,top:n,width:t.width,zIndex:1}},multiValue:function(e){var t=e.theme,n=t.spacing,r=t.borderRadius;return{label:"multiValue",backgroundColor:t.colors.neutral10,borderRadius:r/2,display:"flex",margin:n.baseUnit/2,minWidth:0}},multiValueLabel:function(e){var t=e.theme,n=t.borderRadius,r=t.colors,o=e.cropWithEllipsis;return{borderRadius:n/2,color:r.neutral80,fontSize:"85%",overflow:"hidden",padding:3,paddingLeft:6,textOverflow:o||void 0===o?"ellipsis":void 0,whiteSpace:"nowrap"}},multiValueRemove:function(e){var t=e.theme,n=t.spacing,r=t.borderRadius,o=t.colors;return{alignItems:"center",borderRadius:r/2,backgroundColor:e.isFocused?o.dangerLight:void 0,display:"flex",paddingLeft:n.baseUnit,paddingRight:n.baseUnit,":hover":{backgroundColor:o.dangerLight,color:o.danger}}},noOptionsMessage:U,option:function(e){var t=e.isDisabled,n=e.isFocused,r=e.isSelected,o=e.theme,i=o.spacing,l=o.colors;return{label:"option",backgroundColor:r?l.primary:n?l.primary25:"transparent",color:t?l.neutral20:r?l.neutral0:"inherit",cursor:"default",display:"block",fontSize:"inherit",padding:"".concat(2*i.baseUnit,"px ").concat(3*i.baseUnit,"px"),width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",":active":{backgroundColor:t?void 0:r?l.primary:l.primary50}}},placeholder:function(e){var t=e.theme,n=t.spacing;return{label:"placeholder",color:t.colors.neutral50,gridArea:"1 / 1 / 2 / 3",marginLeft:n.baseUnit/2,marginRight:n.baseUnit/2}},singleValue:function(e){var t=e.isDisabled,n=e.theme,r=n.spacing,o=n.colors;return{label:"singleValue",color:t?o.neutral40:o.neutral80,gridArea:"1 / 1 / 2 / 3",marginLeft:r.baseUnit/2,marginRight:r.baseUnit/2,maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},valueContainer:function(e){var t=e.theme.spacing,n=e.isMulti,r=e.hasValue,o=e.selectProps.controlShouldRenderValue;return{alignItems:"center",display:n&&r&&o?"flex":"grid",flex:1,flexWrap:"wrap",padding:"".concat(t.baseUnit/2,"px ").concat(2*t.baseUnit,"px"),WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"}}};var Ke,$e={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},Ge={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:H(),captureMenuScroll:!H(),closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var n=y({ignoreCase:!0,ignoreAccents:!0,stringify:Pe,trim:!0,matchFrom:"any"},Ke),r=n.ignoreCase,o=n.ignoreAccents,i=n.stringify,l=n.trim,a=n.matchFrom,s=l?Oe(t):t,c=l?Oe(i(e)):i(e);return r&&(s=s.toLowerCase(),c=c.toLowerCase()),o&&(s=Ie(s),c=Te(c)),"start"===a?c.substr(0,s.length)===s:c.indexOf(s)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:function(e){return e.label},getOptionValue:function(e){return e.value},isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!function(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0};function qe(e,t,n,r){return{type:"option",data:t,isDisabled:rt(e,t,n),isSelected:ot(e,t,n),label:tt(e,t),value:nt(e,t),index:r}}function Qe(e,t){return e.options.map((function(n,r){if("options"in n){var o=n.options.map((function(n,r){return qe(e,n,t,r)})).filter((function(t){return et(e,t)}));return o.length>0?{type:"group",data:n,options:o,index:r}:void 0}var i=qe(e,n,t,r);return et(e,i)?i:void 0})).filter(_)}function Je(e){return e.reduce((function(e,t){return"group"===t.type?e.push.apply(e,(0,me.Z)(t.options.map((function(e){return e.data})))):e.push(t.data),e}),[])}function et(e,t){var n=e.inputValue,r=void 0===n?"":n,o=t.data,i=t.isSelected,l=t.label,a=t.value;return(!lt(e)||!i)&&it(e,{label:l,value:a,data:o},r)}var tt=function(e,t){return e.getOptionLabel(t)},nt=function(e,t){return e.getOptionValue(t)};function rt(e,t,n){return"function"===typeof e.isOptionDisabled&&e.isOptionDisabled(t,n)}function ot(e,t,n){if(n.indexOf(t)>-1)return!0;if("function"===typeof e.isOptionSelected)return e.isOptionSelected(t,n);var r=nt(e,t);return n.some((function(t){return nt(e,t)===r}))}function it(e,t,n){return!e.filterOption||e.filterOption(t,n)}var lt=function(e){var t=e.hideSelectedOptions,n=e.isMulti;return void 0===t?n:t},at=1,st=function(e){(0,f.Z)(n,e);var t=k(n);function n(e){var r;return(0,h.Z)(this,n),(r=t.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0},r.blockOptionHover=!1,r.isComposing=!1,r.commonProps=void 0,r.initialTouchX=0,r.initialTouchY=0,r.instancePrefix="",r.openAfterFocus=!1,r.scrollToFocusedOptionOnUpdate=!1,r.userIsDragging=void 0,r.controlRef=null,r.getControlRef=function(e){r.controlRef=e},r.focusedOptionRef=null,r.getFocusedOptionRef=function(e){r.focusedOptionRef=e},r.menuListRef=null,r.getMenuListRef=function(e){r.menuListRef=e},r.inputRef=null,r.getInputRef=function(e){r.inputRef=e},r.focus=r.focusInput,r.blur=r.blurInput,r.onChange=function(e,t){var n=r.props,o=n.onChange,i=n.name;t.name=i,r.ariaOnChange(e,t),o(e,t)},r.setValue=function(e,t,n){var o=r.props,i=o.closeMenuOnSelect,l=o.isMulti,a=o.inputValue;r.onInputChange("",{action:"set-value",prevInputValue:a}),i&&(r.setState({inputIsHiddenAfterUpdate:!l}),r.onMenuClose()),r.setState({clearFocusValueOnUpdate:!0}),r.onChange(e,{action:t,option:n})},r.selectOption=function(e){var t=r.props,n=t.blurInputOnSelect,o=t.isMulti,i=t.name,l=r.state.selectValue,a=o&&r.isOptionSelected(e,l),s=r.isOptionDisabled(e,l);if(a){var c=r.getOptionValue(e);r.setValue(l.filter((function(e){return r.getOptionValue(e)!==c})),"deselect-option",e)}else{if(s)return void r.ariaOnChange(e,{action:"select-option",option:e,name:i});o?r.setValue([].concat((0,me.Z)(l),[e]),"select-option",e):r.setValue(e,"select-option")}n&&r.blurInput()},r.removeValue=function(e){var t=r.props.isMulti,n=r.state.selectValue,o=r.getOptionValue(e),i=n.filter((function(e){return r.getOptionValue(e)!==o})),l=V(t,i,i[0]||null);r.onChange(l,{action:"remove-value",removedValue:e}),r.focusInput()},r.clearValue=function(){var e=r.state.selectValue;r.onChange(V(r.props.isMulti,[],null),{action:"clear",removedValues:e})},r.popValue=function(){var e=r.props.isMulti,t=r.state.selectValue,n=t[t.length-1],o=t.slice(0,t.length-1),i=V(e,o,o[0]||null);r.onChange(i,{action:"pop-value",removedValue:n})},r.getValue=function(){return r.state.selectValue},r.cx=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return E.apply(void 0,[r.props.classNamePrefix].concat(t))},r.getOptionLabel=function(e){return tt(r.props,e)},r.getOptionValue=function(e){return nt(r.props,e)},r.getStyles=function(e,t){var n=Ye[e](t);n.boxSizing="border-box";var o=r.props.styles[e];return o?o(n,t):n},r.getElementId=function(e){return"".concat(r.instancePrefix,"-").concat(e)},r.getComponents=function(){return e=r.props,y(y({},fe),e.components);var e},r.buildCategorizedOptions=function(){return Qe(r.props,r.state.selectValue)},r.getCategorizedOptions=function(){return r.props.menuIsOpen?r.buildCategorizedOptions():[]},r.buildFocusableOptions=function(){return Je(r.buildCategorizedOptions())},r.getFocusableOptions=function(){return r.props.menuIsOpen?r.buildFocusableOptions():[]},r.ariaOnChange=function(e,t){r.setState({ariaSelection:y({value:e},t)})},r.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),r.focusInput())},r.onMenuMouseMove=function(e){r.blockOptionHover=!1},r.onControlMouseDown=function(e){if(!e.defaultPrevented){var t=r.props.openMenuOnClick;r.state.isFocused?r.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&r.onMenuClose():t&&r.openMenu("first"):(t&&(r.openAfterFocus=!0),r.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},r.onDropdownIndicatorMouseDown=function(e){if((!e||"mousedown"!==e.type||0===e.button)&&!r.props.isDisabled){var t=r.props,n=t.isMulti,o=t.menuIsOpen;r.focusInput(),o?(r.setState({inputIsHiddenAfterUpdate:!n}),r.onMenuClose()):r.openMenu("first"),e.preventDefault()}},r.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(r.clearValue(),e.preventDefault(),r.openAfterFocus=!1,"touchend"===e.type?r.focusInput():setTimeout((function(){return r.focusInput()})))},r.onScroll=function(e){"boolean"===typeof r.props.closeMenuOnScroll?e.target instanceof HTMLElement&&I(e.target)&&r.props.onMenuClose():"function"===typeof r.props.closeMenuOnScroll&&r.props.closeMenuOnScroll(e)&&r.props.onMenuClose()},r.onCompositionStart=function(){r.isComposing=!0},r.onCompositionEnd=function(){r.isComposing=!1},r.onTouchStart=function(e){var t=e.touches,n=t&&t.item(0);n&&(r.initialTouchX=n.clientX,r.initialTouchY=n.clientY,r.userIsDragging=!1)},r.onTouchMove=function(e){var t=e.touches,n=t&&t.item(0);if(n){var o=Math.abs(n.clientX-r.initialTouchX),i=Math.abs(n.clientY-r.initialTouchY);r.userIsDragging=o>5||i>5}},r.onTouchEnd=function(e){r.userIsDragging||(r.controlRef&&!r.controlRef.contains(e.target)&&r.menuListRef&&!r.menuListRef.contains(e.target)&&r.blurInput(),r.initialTouchX=0,r.initialTouchY=0)},r.onControlTouchEnd=function(e){r.userIsDragging||r.onControlMouseDown(e)},r.onClearIndicatorTouchEnd=function(e){r.userIsDragging||r.onClearIndicatorMouseDown(e)},r.onDropdownIndicatorTouchEnd=function(e){r.userIsDragging||r.onDropdownIndicatorMouseDown(e)},r.handleInputChange=function(e){var t=r.props.inputValue,n=e.currentTarget.value;r.setState({inputIsHiddenAfterUpdate:!1}),r.onInputChange(n,{action:"input-change",prevInputValue:t}),r.props.menuIsOpen||r.onMenuOpen()},r.onInputFocus=function(e){r.props.onFocus&&r.props.onFocus(e),r.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(r.openAfterFocus||r.props.openMenuOnFocus)&&r.openMenu("first"),r.openAfterFocus=!1},r.onInputBlur=function(e){var t=r.props.inputValue;r.menuListRef&&r.menuListRef.contains(document.activeElement)?r.inputRef.focus():(r.props.onBlur&&r.props.onBlur(e),r.onInputChange("",{action:"input-blur",prevInputValue:t}),r.onMenuClose(),r.setState({focusedValue:null,isFocused:!1}))},r.onOptionHover=function(e){r.blockOptionHover||r.state.focusedOption===e||r.setState({focusedOption:e})},r.shouldHideSelectedOptions=function(){return lt(r.props)},r.onKeyDown=function(e){var t=r.props,n=t.isMulti,o=t.backspaceRemovesValue,i=t.escapeClearsValue,l=t.inputValue,a=t.isClearable,s=t.isDisabled,c=t.menuIsOpen,u=t.onKeyDown,d=t.tabSelectsValue,h=t.openMenuOnFocus,p=r.state,f=p.focusedOption,g=p.focusedValue,m=p.selectValue;if(!s&&("function"!==typeof u||(u(e),!e.defaultPrevented))){switch(r.blockOptionHover=!0,e.key){case"ArrowLeft":if(!n||l)return;r.focusValue("previous");break;case"ArrowRight":if(!n||l)return;r.focusValue("next");break;case"Delete":case"Backspace":if(l)return;if(g)r.removeValue(g);else{if(!o)return;n?r.popValue():a&&r.clearValue()}break;case"Tab":if(r.isComposing)return;if(e.shiftKey||!c||!d||!f||h&&r.isOptionSelected(f,m))return;r.selectOption(f);break;case"Enter":if(229===e.keyCode)break;if(c){if(!f)return;if(r.isComposing)return;r.selectOption(f);break}return;case"Escape":c?(r.setState({inputIsHiddenAfterUpdate:!1}),r.onInputChange("",{action:"menu-close",prevInputValue:l}),r.onMenuClose()):a&&i&&r.clearValue();break;case" ":if(l)return;if(!c){r.openMenu("first");break}if(!f)return;r.selectOption(f);break;case"ArrowUp":c?r.focusOption("up"):r.openMenu("last");break;case"ArrowDown":c?r.focusOption("down"):r.openMenu("first");break;case"PageUp":if(!c)return;r.focusOption("pageup");break;case"PageDown":if(!c)return;r.focusOption("pagedown");break;case"Home":if(!c)return;r.focusOption("first");break;case"End":if(!c)return;r.focusOption("last");break;default:return}e.preventDefault()}},r.instancePrefix="react-select-"+(r.props.instanceId||++at),r.state.selectValue=R(e.value),r}return(0,p.Z)(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput()}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDisabled,r=t.menuIsOpen,o=this.state.isFocused;(o&&!n&&e.isDisabled||o&&r&&!e.menuIsOpen)&&this.focusInput(),o&&n&&!e.isDisabled&&this.setState({isFocused:!1},this.onMenuClose),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(!function(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),o=t.offsetHeight/3;r.bottom+o>n.bottom?P(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+o,e.scrollHeight)):r.top-o<n.top&&P(e,Math.max(t.offsetTop-o,0))}(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,n=this.state,r=n.selectValue,o=n.isFocused,i=this.buildFocusableOptions(),l="first"===e?0:i.length-1;if(!this.props.isMulti){var a=i.indexOf(r[0]);a>-1&&(l=a)}this.scrollToFocusedOptionOnUpdate=!(o&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:i[l]},(function(){return t.onMenuOpen()}))}},{key:"focusValue",value:function(e){var t=this.state,n=t.selectValue,r=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var o=n.indexOf(r);r||(o=-1);var i=n.length-1,l=-1;if(n.length){switch(e){case"previous":l=0===o?0:-1===o?i:o-1;break;case"next":o>-1&&o<i&&(l=o+1)}this.setState({inputIsHidden:-1!==l,focusedValue:n[l]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,n=this.state.focusedOption,r=this.getFocusableOptions();if(r.length){var o=0,i=r.indexOf(n);n||(i=-1),"up"===e?o=i>0?i-1:r.length-1:"down"===e?o=(i+1)%r.length:"pageup"===e?(o=i-t)<0&&(o=0):"pagedown"===e?(o=i+t)>r.length-1&&(o=r.length-1):"last"===e&&(o=r.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:r[o],focusedValue:null})}}},{key:"getTheme",value:function(){return this.props.theme?"function"===typeof this.props.theme?this.props.theme($e):y(y({},$e),this.props.theme):$e}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,n=this.getStyles,r=this.getValue,o=this.selectOption,i=this.setValue,l=this.props,a=l.isMulti,s=l.isRtl,c=l.options;return{clearValue:e,cx:t,getStyles:n,getValue:r,hasValue:this.hasValue(),isMulti:a,isRtl:s,options:c,selectOption:o,selectProps:l,setValue:i,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,n=e.isMulti;return void 0===t?n:t}},{key:"isOptionDisabled",value:function(e,t){return rt(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return ot(this.props,e,t)}},{key:"filterOption",value:function(e,t){return it(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"===typeof this.props.formatOptionLabel){var n=this.props.inputValue,r=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:n,selectValue:r})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,n=e.isSearchable,r=e.inputId,o=e.inputValue,a=e.tabIndex,s=e.form,c=e.menuIsOpen,u=this.getComponents().Input,d=this.state,h=d.inputIsHidden,p=d.ariaSelection,f=this.commonProps,g=r||this.getElementId("input"),m=y(y(y({"aria-autocomplete":"list","aria-expanded":c,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],role:"combobox"},c&&{"aria-controls":this.getElementId("listbox"),"aria-owns":this.getElementId("listbox")}),!n&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null===p||void 0===p?void 0:p.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return n?i.createElement(u,(0,l.Z)({},f,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:g,innerRef:this.getInputRef,isDisabled:t,isHidden:h,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:a,form:s,type:"text",value:o},m)):i.createElement(He,(0,l.Z)({id:g,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:C,onFocus:this.onInputFocus,disabled:t,tabIndex:a,inputMode:"none",form:s,value:""},m))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),n=t.MultiValue,r=t.MultiValueContainer,o=t.MultiValueLabel,a=t.MultiValueRemove,s=t.SingleValue,c=t.Placeholder,u=this.commonProps,d=this.props,h=d.controlShouldRenderValue,p=d.isDisabled,f=d.isMulti,g=d.inputValue,m=d.placeholder,v=this.state,b=v.selectValue,y=v.focusedValue,w=v.isFocused;if(!this.hasValue()||!h)return g?null:i.createElement(c,(0,l.Z)({},u,{key:"placeholder",isDisabled:p,isFocused:w,innerProps:{id:this.getElementId("placeholder")}}),m);if(f)return b.map((function(t,s){var c=t===y,d="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return i.createElement(n,(0,l.Z)({},u,{components:{Container:r,Label:o,Remove:a},isFocused:c,isDisabled:p,key:d,index:s,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))}));if(g)return null;var x=b[0];return i.createElement(s,(0,l.Z)({},u,{data:x,isDisabled:p}),this.formatOptionLabel(x,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,n=this.props,r=n.isDisabled,o=n.isLoading,a=this.state.isFocused;if(!this.isClearable()||!e||r||!this.hasValue()||o)return null;var s={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return i.createElement(e,(0,l.Z)({},t,{innerProps:s,isFocused:a}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,n=this.props,r=n.isDisabled,o=n.isLoading,a=this.state.isFocused;if(!e||!o)return null;return i.createElement(e,(0,l.Z)({},t,{innerProps:{"aria-hidden":"true"},isDisabled:r,isFocused:a}))}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,n=e.IndicatorSeparator;if(!t||!n)return null;var r=this.commonProps,o=this.props.isDisabled,a=this.state.isFocused;return i.createElement(n,(0,l.Z)({},r,{isDisabled:o,isFocused:a}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,n=this.props.isDisabled,r=this.state.isFocused,o={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return i.createElement(e,(0,l.Z)({},t,{innerProps:o,isDisabled:n,isFocused:r}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),n=t.Group,r=t.GroupHeading,o=t.Menu,a=t.MenuList,s=t.MenuPortal,c=t.LoadingMessage,u=t.NoOptionsMessage,d=t.Option,h=this.commonProps,p=this.state.focusedOption,f=this.props,g=f.captureMenuScroll,m=f.inputValue,v=f.isLoading,b=f.loadingMessage,y=f.minMenuHeight,w=f.maxMenuHeight,x=f.menuIsOpen,k=f.menuPlacement,S=f.menuPosition,C=f.menuPortalTarget,M=f.menuShouldBlockScroll,E=f.menuShouldScrollIntoView,R=f.noOptionsMessage,T=f.onMenuScrollToTop,I=f.onMenuScrollToBottom;if(!x)return null;var O,P=function(t,n){var r=t.type,o=t.data,a=t.isDisabled,s=t.isSelected,c=t.label,u=t.value,f=p===o,g=a?void 0:function(){return e.onOptionHover(o)},m=a?void 0:function(){return e.selectOption(o)},v="".concat(e.getElementId("option"),"-").concat(n),b={id:v,onClick:m,onMouseMove:g,onMouseOver:g,tabIndex:-1};return i.createElement(d,(0,l.Z)({},h,{innerProps:b,data:o,isDisabled:a,isSelected:s,key:v,label:c,type:r,value:u,isFocused:f,innerRef:f?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())O=this.getCategorizedOptions().map((function(t){if("group"===t.type){var o=t.data,a=t.options,s=t.index,c="".concat(e.getElementId("group"),"-").concat(s),u="".concat(c,"-heading");return i.createElement(n,(0,l.Z)({},h,{key:c,data:o,options:a,Heading:r,headingProps:{id:u,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map((function(e){return P(e,"".concat(s,"-").concat(e.index))})))}if("option"===t.type)return P(t,"".concat(t.index))}));else if(v){var D=b({inputValue:m});if(null===D)return null;O=i.createElement(c,h,D)}else{var H=R({inputValue:m});if(null===H)return null;O=i.createElement(u,h,H)}var L={minMenuHeight:y,maxMenuHeight:w,menuPlacement:k,menuPosition:S,menuShouldScrollIntoView:E},z=i.createElement(j,(0,l.Z)({},h,L),(function(t){var n=t.ref,r=t.placerProps,s=r.placement,c=r.maxHeight;return i.createElement(o,(0,l.Z)({},h,L,{innerRef:n,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove,id:e.getElementId("listbox")},isLoading:v,placement:s}),i.createElement(Xe,{captureEnabled:g,onTopArrive:T,onBottomArrive:I,lockEnabled:M},(function(t){return i.createElement(a,(0,l.Z)({},h,{innerRef:function(n){e.getMenuListRef(n),t(n)},isLoading:v,maxHeight:c,focusedOption:p}),O)})))}));return C||"fixed"===S?i.createElement(s,(0,l.Z)({},h,{appendTo:C,controlElement:this.controlRef,menuPlacement:k,menuPosition:S}),z):z}},{key:"renderFormField",value:function(){var e=this,t=this.props,n=t.delimiter,r=t.isDisabled,o=t.isMulti,l=t.name,a=this.state.selectValue;if(l&&!r){if(o){if(n){var s=a.map((function(t){return e.getOptionValue(t)})).join(n);return i.createElement("input",{name:l,type:"hidden",value:s})}var c=a.length>0?a.map((function(t,n){return i.createElement("input",{key:"i-".concat(n),name:l,type:"hidden",value:e.getOptionValue(t)})})):i.createElement("input",{name:l,type:"hidden"});return i.createElement("div",null,c)}var u=a[0]?this.getOptionValue(a[0]):"";return i.createElement("input",{name:l,type:"hidden",value:u})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,n=t.ariaSelection,r=t.focusedOption,o=t.focusedValue,a=t.isFocused,s=t.selectValue,c=this.getFocusableOptions();return i.createElement(xe,(0,l.Z)({},e,{id:this.getElementId("live-region"),ariaSelection:n,focusedOption:r,focusedValue:o,isFocused:a,selectValue:s,focusableOptions:c}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,n=e.IndicatorsContainer,r=e.SelectContainer,o=e.ValueContainer,a=this.props,s=a.className,c=a.id,u=a.isDisabled,d=a.menuIsOpen,h=this.state.isFocused,p=this.commonProps=this.getCommonProps();return i.createElement(r,(0,l.Z)({},p,{className:s,innerProps:{id:c,onKeyDown:this.onKeyDown},isDisabled:u,isFocused:h}),this.renderLiveRegion(),i.createElement(t,(0,l.Z)({},p,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:u,isFocused:h,menuIsOpen:d}),i.createElement(o,(0,l.Z)({},p,{isDisabled:u}),this.renderPlaceholderOrValue(),this.renderInput()),i.createElement(n,(0,l.Z)({},p,{isDisabled:u}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevProps,r=t.clearFocusValueOnUpdate,o=t.inputIsHiddenAfterUpdate,i=t.ariaSelection,l=t.isFocused,a=t.prevWasFocused,s=e.options,c=e.value,u=e.menuIsOpen,d=e.inputValue,h=e.isMulti,p=R(c),f={};if(n&&(c!==n.value||s!==n.options||u!==n.menuIsOpen||d!==n.inputValue)){var g=u?function(e,t){return Je(Qe(e,t))}(e,p):[],m=r?function(e,t){var n=e.focusedValue,r=e.selectValue.indexOf(n);if(r>-1){if(t.indexOf(n)>-1)return n;if(r<t.length)return t[r]}return null}(t,p):null,v=function(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}(t,g);f={selectValue:p,focusedOption:v,focusedValue:m,clearFocusValueOnUpdate:!1}}var b=null!=o&&e!==n?{inputIsHidden:o,inputIsHiddenAfterUpdate:void 0}:{},w=i,x=l&&a;return l&&!x&&(w={value:V(h,p,p[0]||null),options:p,action:"initial-input-focus"},x=!a),"initial-input-focus"===(null===i||void 0===i?void 0:i.action)&&(w=null),y(y(y({},f),b),{},{prevProps:e,ariaSelection:w,prevWasFocused:x})}}]),n}(i.Component);st.defaultProps=Ge;var ct=n(25621),ut=n(17715),dt=(0,i.forwardRef)((function(e,t){var n=function(e){var t=e.defaultInputValue,n=void 0===t?"":t,r=e.defaultMenuIsOpen,o=void 0!==r&&r,l=e.defaultValue,a=void 0===l?null:l,s=e.inputValue,d=e.menuIsOpen,h=e.onChange,p=e.onInputChange,f=e.onMenuClose,g=e.onMenuOpen,m=e.value,v=(0,c.Z)(e,ge),b=(0,i.useState)(void 0!==s?s:n),w=(0,u.Z)(b,2),x=w[0],k=w[1],S=(0,i.useState)(void 0!==d?d:o),C=(0,u.Z)(S,2),M=C[0],E=C[1],R=(0,i.useState)(void 0!==m?m:a),T=(0,u.Z)(R,2),I=T[0],O=T[1],P=(0,i.useCallback)((function(e,t){"function"===typeof h&&h(e,t),O(e)}),[h]),D=(0,i.useCallback)((function(e,t){var n;"function"===typeof p&&(n=p(e,t)),k(void 0!==n?n:e)}),[p]),H=(0,i.useCallback)((function(){"function"===typeof g&&g(),E(!0)}),[g]),L=(0,i.useCallback)((function(){"function"===typeof f&&f(),E(!1)}),[f]),z=void 0!==s?s:x,F=void 0!==d?d:M,A=void 0!==m?m:I;return y(y({},v),{},{inputValue:z,menuIsOpen:F,onChange:P,onInputChange:D,onMenuClose:L,onMenuOpen:H,value:A})}(e);return i.createElement(st,(0,l.Z)({ref:t},n))}));i.Component;const ht=dt;var pt=[[50,5],[61.23,39.55],[97.55,39.55],[68.16,60.9],[79.39,95.45],[50,74.1],[20.61,95.45],[31.84,60.9],[2.45,39.55],[38.77,39.55]];function ft(e,t,n){let r=!1;for(const o of pt){const i=(o[0]-50)*(n/100)+t[0],l=(o[1]-50)*(n/100)+t[1];r?e.lineTo(i,l):(e.moveTo(i,l),r=!0)}e.closePath()}var gt=()=>(0,i.createElement)("svg",{width:"100",height:"100",viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,i.createElement)("path",{d:"M47.1468 13.7811C48.0449 11.0172 51.9551 11.0172 52.8532 13.7812L60.5522 37.4762C60.9538 38.7123 62.1056 39.5491 63.4053 39.5491H88.3198C91.226 39.5491 92.4343 43.268 90.0831 44.9762L69.9269 59.6205C68.8755 60.3845 68.4355 61.7386 68.8371 62.9746L76.5361 86.6697C77.4342 89.4336 74.2707 91.732 71.9196 90.0238L51.7634 75.3794C50.7119 74.6155 49.2881 74.6155 48.2366 75.3795L28.0804 90.0238C25.7293 91.732 22.5659 89.4336 23.4639 86.6697L31.1629 62.9746C31.5645 61.7386 31.1245 60.3845 30.0731 59.6205L9.91686 44.9762C7.56572 43.268 8.77405 39.5491 11.6802 39.5491H36.5947C37.8944 39.5491 39.0462 38.7123 39.4478 37.4762L47.1468 13.7811Z",fill:"currentColor"})),mt=(0,r.d)("div")({name:"EditorWrap",class:"e17a8fyf"}),vt={kind:o.p6.Custom,isMatch:e=>"star-cell"===e.data.kind,needsHover:!0,draw:(e,t)=>{const{ctx:n,theme:r,rect:o,hoverAmount:i}=e,{rating:l}=t.data,a=r.cellHorizontalPadding;let s=o.x+a;const c=Math.min(5,Math.ceil(l));s+=8,n.beginPath();for(let u=0;u<c;u++)ft(n,[s,o.y+o.height/2],16),s+=18;return n.fillStyle=r.textDark,n.globalAlpha=.6+.4*i,n.fill(),n.globalAlpha=1,!0},provideEditor:()=>e=>(0,i.createElement)(mt,null,[0,1,2,3,4].map((t=>(0,i.createElement)("div",{key:t,className:e.value.data.rating<t+1?"inactive":"active",onClick:()=>{e.onChange({...e.value,data:{...e.value.data,rating:t+1}})}},(0,i.createElement)(gt,null))))),onPaste:(e,t)=>{const n=Number.parseInt(e);return{...t,rating:Number.isNaN(n)?0:n}}},bt=vt,yt={kind:o.p6.Custom,isMatch:e=>"sparkline-cell"===e.data.kind,needsHover:!0,needsHoverPosition:!0,draw:(e,t)=>{var n;const{ctx:r,theme:i,rect:l,hoverAmount:a,hoverX:s}=e;let{values:c,yAxis:u,color:d,graphKind:h="line",displayValues:p}=t.data;const[f,g]=u;if(0===c.length)return!0;c=c.map((e=>Math.min(1,Math.max(0,(e-f)/(g-f)))));const m=i.cellHorizontalPadding,v=m+l.x,b=l.y+3,y=l.height-6,w=l.width-2*m,x=g<=0?b:f>=0?b+y:b+y*(g/(g-f));if(f<=0&&g>=0&&(r.beginPath(),r.moveTo(v,x),r.lineTo(v+w,x),r.globalAlpha=.4,r.lineWidth=1,r.strokeStyle=i.textLight,r.stroke(),r.globalAlpha=1),"bar"===h){r.beginPath();const e=2,o=(w-(c.length-1)*e)/c.length;let l=v;for(const t of c){const n=b+y-t*y;r.moveTo(l,x),r.lineTo(l+o,x),r.lineTo(l+o,n),r.lineTo(l,n),l+=o+e}r.fillStyle=null!=(n=t.data.color)?n:i.accentColor,r.fill()}else{1===c.length&&(c=[c[0],c[0]]),r.beginPath();const e=(l.width-16)/(c.length-1),t=c.map(((t,n)=>({x:v+e*n,y:b+y-t*y})));let n;for(r.moveTo(t[0].x,t[0].y),n=1;n<t.length-2;n++){const e=(t[n].x+t[n+1].x)/2,o=(t[n].y+t[n+1].y)/2;r.quadraticCurveTo(t[n].x,t[n].y,e,o)}r.quadraticCurveTo(t[n].x,t[n].y,t[n+1].x,t[n+1].y),r.strokeStyle=null!=d?d:i.accentColor,r.lineWidth=1+.5*a,r.stroke(),r.lineTo(l.x+l.width-m,x),r.lineTo(l.x+m,x),r.closePath(),r.globalAlpha=.2+.2*a;const u=r.createLinearGradient(0,b,0,b*****y);u.addColorStop(0,null!=d?d:i.accentColor);const[f,g,w]=(0,o.dF)(null!=d?d:i.accentColor);if(u.addColorStop(1,"rgba(".concat(f,", ").concat(g,", ").concat(w,", 0)")),r.fillStyle=u,r.fill(),r.globalAlpha=1,void 0!==s&&"line"===h&&void 0!==p){r.beginPath();const t=Math.min(c.length-1,Math.max(0,Math.round((s-m)/e)));r.moveTo(v+t*e,l.y),r.lineTo(v+t*e,l.y+l.height),r.lineWidth=1,r.strokeStyle=i.textLight,r.stroke(),r.save(),r.font="8px ".concat(i.fontFamily),r.fillStyle=i.textMedium,r.textBaseline="top",r.fillText(p[t],v,l.y+i.cellVerticalPadding),r.restore()}}return!0},provideEditor:()=>{},onPaste:(e,t)=>t},wt=yt;function xt(e,t,n,r,o,i){0!==i?("number"===typeof i&&(i={tl:i,tr:i,br:i,bl:i}),i={tl:Math.min(i.tl,o/2,r/2),tr:Math.min(i.tr,o/2,r/2),bl:Math.min(i.bl,o/2,r/2),br:Math.min(i.br,o/2,r/2)},e.moveTo(t+i.tl,n),e.arcTo(t+r,n,t+r,n+i.tr,i.tr),e.arcTo(t+r,n+o,t+r-i.br,n+o,i.br),e.arcTo(t,n+o,t,n+o-i.bl,i.bl),e.arcTo(t,n,t+i.tl,n,i.tl)):e.rect(t,n,r,o)}var kt=20,St=(0,r.d)("div")({name:"EditorWrap",class:"e43amum",vars:{"e43amum-0":[e=>e.tagHeight/2,"px"],"e43amum-1":[e=>e.tagHeight,"px"],"e43amum-2":[e=>e.innerPad,"px"]}}),Ct={kind:o.p6.Custom,isMatch:e=>"tags-cell"===e.data.kind,draw:(e,t)=>{var n,r;const{ctx:i,theme:l,rect:a}=e,{possibleTags:s,tags:c}=t.data,u={x:a.x+l.cellHorizontalPadding,y:a.y+l.cellVerticalPadding,width:a.width-2*l.cellHorizontalPadding,height:a.height-2*l.cellVerticalPadding},d=Math.max(1,Math.floor(u.height/26));let h=u.x,p=1,f=u.y+(u.height-d*kt-6*(d-1))/2;for(const g of c){const e=null!=(r=null==(n=s.find((e=>e.tag===g)))?void 0:n.color)?r:l.bgBubble;i.font="12px ".concat(l.fontFamily);const t=(0,o.P7)(g,i).width+12,a=10;if(h!==u.x&&h+t>u.x+u.width&&p<d&&(p++,f+=26,h=u.x),i.fillStyle=e,i.beginPath(),xt(i,h,f,t,kt,10),i.fill(),i.fillStyle=l.textDark,i.fillText(g,h+6,f+a+(0,o.aX)(i,"12px ".concat(l.fontFamily))),h+=t+8,h>u.x+u.width&&p>=d)break}return!0},provideEditor:()=>e=>{const{onChange:t,value:n}=e,{possibleTags:r,tags:o,readonly:l=!1}=n.data;return(0,i.createElement)(St,{tagHeight:kt,innerPad:6,className:l?"readonly":""},r.map((r=>{const a=-1!==o.indexOf(r.tag);return(0,i.createElement)("label",{key:r.tag},!l&&(0,i.createElement)("input",{className:"gdg-input",type:"checkbox",checked:a,onChange:()=>{const i=a?o.filter((e=>e!==r.tag)):[...o,r.tag];t({...e.value,data:{...n.data,tags:i}})}}),(0,i.createElement)("div",{className:"pill "+(a?"selected":"unselected"),style:{backgroundColor:a?r.color:void 0}},r.tag))})))},onPaste:(e,t)=>({...t,tags:t.possibleTags.map((e=>e.tag)).filter((t=>e.split(",").map((e=>e.trim())).includes(t)))})},Mt={kind:o.p6.Custom,isMatch:e=>"user-profile-cell"===e.data.kind,draw:(e,t)=>{const{ctx:n,rect:r,theme:i,imageLoader:l,col:a,row:s}=e,{image:c,name:u,initial:d,tint:h}=t.data,p=i.cellHorizontalPadding,f=Math.min(12,r.height/2-i.cellVerticalPadding),g=r.x+p,m=l.loadOrGetImage(c,a,s);n.save(),n.beginPath(),n.arc(g+f,r.y+r.height/2,f,0,2*Math.PI),n.globalAlpha=.2,n.fillStyle=h,n.fill(),n.globalAlpha=1,n.font="600 16px ".concat(i.fontFamily);const v=(0,o.P7)(d[0],n);return n.fillText(d[0],g+f-v.width/2,r.y+r.height/2+(0,o.aX)(n,"600 16px ".concat(i.fontFamily))),void 0!==m&&(n.save(),n.beginPath(),n.arc(g+f,r.y+r.height/2,f,0,2*Math.PI),n.clip(),n.drawImage(m,g,r.y+r.height/2-f,2*f,2*f),n.restore()),void 0!==u&&(n.font="".concat(i.baseFontStyle," ").concat(i.fontFamily),n.fillStyle=i.textDark,n.fillText(u,g+2*f+p,r.y+r.height/2+(0,o.aX)(n,i))),n.restore(),!0},provideEditor:()=>e=>{var t;const{isHighlighted:n,onChange:r,value:l}=e;return(0,i.createElement)(o.t5,{highlight:n,autoFocus:!0,value:null!=(t=l.data.name)?t:"",onChange:e=>r({...l,data:{...l.data,name:e.target.value}})})},onPaste:(e,t)=>({...t,name:e})},Et=e=>{const{Menu:t}=fe,{children:n,...r}=e;return(0,i.createElement)(t,{...r},n)},Rt=(0,r.d)("div")({name:"Wrap",class:"w13j932a"}),Tt=(0,r.d)("div")({name:"PortalWrap",class:"p19663q2"}),It=(0,r.d)("div")({name:"ReadOnlyWrap",class:"r1jyvvws"}),Ot=e=>{const{value:t,onFinishedEditing:n,initialValue:r}=e,{allowedValues:l,value:a}=t.data,[s,c]=(0,i.useState)(a),[u,d]=(0,i.useState)(null!=r?r:""),h=(0,o.Fg)(),p=(0,i.useMemo)((()=>l.map((e=>({value:e,label:e})))),[l]);return t.readonly?(0,i.createElement)(It,null,(0,i.createElement)(o.t5,{highlight:!0,autoFocus:!1,disabled:!0,value:null!=s?s:"",onChange:()=>{}})):(0,i.createElement)(Rt,null,(0,i.createElement)(ht,{className:"glide-select",inputValue:u,onInputChange:d,menuPlacement:"auto",value:p.find((e=>e.value===s)),styles:{control:e=>({...e,border:0,boxShadow:"none"}),option:e=>({...e,fontSize:h.editorFontSize,fontFamily:h.fontFamily,":empty::after":{content:'"&nbsp;"',visibility:"hidden"}})},theme:e=>({...e,colors:{...e.colors,neutral0:h.bgCell,neutral5:h.bgCell,neutral10:h.bgCell,neutral20:h.bgCellMedium,neutral30:h.bgCellMedium,neutral40:h.bgCellMedium,neutral50:h.textLight,neutral60:h.textMedium,neutral70:h.textMedium,neutral80:h.textDark,neutral90:h.textDark,neutral100:h.textDark,primary:h.accentColor,primary75:h.accentColor,primary50:h.accentColor,primary25:h.accentLight}}),menuPortalTarget:document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null,Menu:e=>(0,i.createElement)(Tt,null,(0,i.createElement)(Et,{className:"click-outside-ignore",...e}))},options:p,onChange:async e=>{null!==e&&(c(e.value),await new Promise((e=>window.requestAnimationFrame(e))),n({...t,data:{...t.data,value:e.value}}))}}))},Pt={kind:o.p6.Custom,isMatch:e=>"dropdown-cell"===e.data.kind,draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{value:l}=t.data;return l&&(n.fillStyle=r.textDark,n.fillText(l,i.x+r.cellHorizontalPadding,i.y+i.height/2+(0,o.aX)(n,r))),!0},measure:(e,t)=>{const{value:n}=t.data;return n?e.measureText(n).width+16:16},provideEditor:()=>({editor:Ot,disablePadding:!0,deletedValue:e=>({...e,copyData:"",data:{...e.data,value:""}})}),onPaste:(e,t)=>({...t,value:t.allowedValues.includes(e)?e:t.value})},Dt=(0,i.lazy)((async()=>await n.e(5733).then(n.bind(n,45733)))),Ht={kind:o.p6.Custom,isMatch:e=>"article-cell"===e.data.kind,draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{markdown:l}=t.data;let a=l;a.includes("\n")&&(a=a.split(/\r?\n/)[0]);const s=i.width/4;return a.length>s&&(a=a.slice(0,s)),n.fillStyle=r.textDark,n.fillText(a,i.x+r.cellHorizontalPadding,i.y+i.height/2+(0,o.aX)(n,r)),!0},provideEditor:()=>({editor:e=>(0,i.createElement)(i.Suspense,{fallback:null},(0,i.createElement)(Dt,{...e})),styleOverride:{position:"fixed",left:"12.5vw",top:"12.5vh",width:"75vw",borderRadius:"9px",maxWidth:"unset",maxHeight:"unset"},disablePadding:!0}),onPaste:(e,t)=>({...t,markdown:e})},Lt={marginRight:8},zt={display:"flex",alignItems:"center",flexGrow:1},Ft={kind:o.p6.Custom,isMatch:e=>"range-cell"===e.data.kind,draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{min:l,max:a,value:s,label:c,measureLabel:u}=t.data,d=i.x+r.cellHorizontalPadding,h=i.y+i.height/2,p=(s-l)/(a-l);n.save();let f=0;void 0!==c&&(n.font="12px ".concat(r.fontFamily),f=(0,o.P7)(null!=u?u:c,n,"12px ".concat(r.fontFamily)).width+r.cellHorizontalPadding);const g=i.width-2*r.cellHorizontalPadding-f;if(g>=6){const e=n.createLinearGradient(d,h,d+g,h);e.addColorStop(0,r.accentColor),e.addColorStop(p,r.accentColor),e.addColorStop(p,r.bgBubble),e.addColorStop(1,r.bgBubble),n.beginPath(),n.fillStyle=e,xt(n,d,h-3,g,6,3),n.fill(),n.beginPath(),xt(n,d+.5,h-3+.5,g-1,5,2.5),n.strokeStyle=r.accentLight,n.lineWidth=1,n.stroke()}return void 0!==c&&(n.textAlign="right",n.fillStyle=r.textDark,n.fillText(c,i.x+i.width-r.cellHorizontalPadding,h+(0,o.aX)(n,"12px ".concat(r.fontFamily)))),n.restore(),!0},provideEditor:()=>e=>{const{data:t}=e.value,n=t.value.toString(),r=t.min.toString(),o=t.max.toString(),l=t.step.toString();return(0,i.createElement)("label",{style:zt},(0,i.createElement)("input",{style:Lt,type:"range",value:n,min:r,max:o,step:l,onChange:n=>{e.onChange({...e.value,data:{...t,value:Number(n.target.value)}})}}),n)},onPaste:(e,t)=>{let n=Number.parseFloat(e);return n=Number.isNaN(n)?t.value:Math.max(t.min,Math.min(t.max,n)),{...t,value:n}}},At={kind:o.p6.Custom,isMatch:e=>"spinner-cell"===e.data.kind,draw:e=>{const{ctx:t,theme:n,rect:r,requestAnimationFrame:o}=e,i=window.performance.now()%1e3/1e3,l=r.x+r.width/2,a=r.y+r.height/2;return t.arc(l,a,Math.min(12,r.height/6),2*Math.PI*i,2*Math.PI*i*****Math.PI),t.strokeStyle=n.textMedium,t.lineWidth=1.5,t.stroke(),t.lineWidth=1,o(),!0},provideEditor:()=>{}},_t=(0,r.d)("input")({name:"StyledInputBox",class:"s1sdc9r3"}),Vt=(e,t)=>{if(void 0===t||null===t)return"";const n=t.toISOString();switch(e){case"date":return n.split("T")[0];case"datetime-local":return n.replace("Z","");case"time":return n.split("T")[1].replace("Z","");default:throw new Error("Unknown date kind ".concat(e))}},Nt=e=>{const t=e.value.data,{format:n,displayDate:r}=t,l=void 0===t.step||Number.isNaN(Number(t.step))?void 0:Number(t.step),a=t.min instanceof Date?Vt(n,t.min):t.min,s=t.max instanceof Date?Vt(n,t.max):t.max;let c=t.date;const u=t.timezoneOffset?60*t.timezoneOffset*1e3:0;u&&c&&(c=new Date(c.getTime()+u));const d=Vt(n,c);return e.value.readonly?i.createElement(o.t5,{highlight:!0,autoFocus:!1,disabled:!0,value:null!=r?r:"",onChange:()=>{}}):i.createElement(_t,{"data-testid":"date-picker-cell",required:!0,type:n,defaultValue:d,min:a,max:s,step:l,autoFocus:!0,onChange:t=>{isNaN(t.target.valueAsNumber)?e.onChange({...e.value,data:{...e.value.data,date:void 0}}):e.onChange({...e.value,data:{...e.value.data,date:new Date(t.target.valueAsNumber-u)}})}})},Bt={kind:o.p6.Custom,isMatch:e=>"date-picker-cell"===e.data.kind,draw:(e,t)=>{const{displayDate:n}=t.data;return(0,o.uN)(e,n,t.contentAlign),!0},measure:(e,t)=>{const{displayDate:n}=t.data;return e.measureText(n).width+16},provideEditor:()=>({editor:Nt}),onPaste:(e,t)=>{let n=NaN;return e&&(n=Number(e).valueOf(),Number.isNaN(n)&&(n=Date.parse(e),"time"===t.format&&Number.isNaN(n)&&(n=Date.parse("1970-01-01T".concat(e,"Z"))))),{...t,date:Number.isNaN(n)?void 0:new Date(n)}}};function Wt(e){if("click"!==e.cell.data.navigateOn!==e.ctrlKey)return;const t=document.createElement("canvas").getContext("2d",{alpha:!1});if(null===t)return;const{posX:n,bounds:r,cell:i,theme:l}=e,a="".concat(l.baseFontStyle," ").concat(l.fontFamily);t.font=a;const{links:s}=i.data,c=l.cellHorizontalPadding;let u=r.x+c;const d=r.x+n;for(const[h,p]of s.entries()){const e=h<s.length-1,n=(0,o.P7)(p.title,t),r=e?(0,o.P7)(p.title+",",t,a):n;if(d>u&&d<u+n.width)return p;u+=r.width+4}}var jt={kind:o.p6.Custom,needsHover:!0,needsHoverPosition:!0,isMatch:e=>"links-cell"===e.data.kind,onSelect:e=>{void 0!==Wt(e)&&e.preventDefault()},onClick:e=>{var t;const n=Wt(e);void 0!==n&&(null==(t=n.onClick)||t.call(n),e.preventDefault())},draw:(e,t)=>{const{ctx:n,rect:r,theme:i,hoverX:l=-100,highlighted:a}=e,{links:s,underlineOffset:c=5}=t.data,u=i.cellHorizontalPadding;let d=r.x+u;const h=r.x+l,p="".concat(i.baseFontStyle," ").concat(i.fontFamily),f=(0,o.aX)(n,p),g=r.y+r.height/2+f;for(const[m,v]of s.entries()){const e=m<s.length-1,t=(0,o.P7)(v.title,n,p),r=e?(0,o.P7)(v.title+",",n,p):t;h>d&&h<d+t.width&&(n.moveTo(d,Math.floor(g+c)+.5),n.lineTo(d+t.width,Math.floor(g+c)+.5),n.strokeStyle=i.textDark,n.stroke(),n.fillStyle=a?(0,o.NH)(i.accentLight,i.bgCell):i.bgCell,n.fillText(e?v.title+",":v.title,d-1,g),n.fillText(e?v.title+",":v.title,d+1,g),n.fillText(e?v.title+",":v.title,d-2,g),n.fillText(e?v.title+",":v.title,d+2,g)),n.fillStyle=i.textDark,n.fillText(e?v.title+",":v.title,d,g),d+=r.width+4}return!0},provideEditor:()=>e=>{const{value:t,onChange:n}=e,{links:r,maxLinks:o=Number.MAX_SAFE_INTEGER}=t.data;return(0,i.createElement)(Zt,{onKeyDown:Ut},r.map(((e,o)=>{var l;return(0,i.createElement)(Xt,{key:o,link:null!=(l=e.href)?l:"",title:e.title,focus:0===o,onDelete:r.length>1?()=>{const e=[...r];e.splice(o,1),n({...t,data:{...t.data,links:e}})}:void 0,onChange:(e,i)=>{const l=[...r];l[o]={href:e,title:i},n({...t,data:{...t.data,links:l}})}})})),(0,i.createElement)("button",{disabled:r.length>=o,className:"add-link",onClick:()=>{const e=[...r,{title:""}];n({...t,data:{...t.data,links:e}})}},"Add link"))},onPaste:(e,t)=>{const n=e.split(",");if(!t.links.some(((e,t)=>n[t]!==e.title)))return{...t,links:n.map((e=>({title:e})))}}},Zt=(0,r.d)("div")({name:"LinksCellEditorStyle",class:"lneeve5"});function Ut(e){"Tab"===e.key&&e.stopPropagation()}var Xt=e=>{const{link:t,onChange:n,title:r,onDelete:o,focus:l}=e;return(0,i.createElement)("div",{className:"gdg-link-title-editor"},(0,i.createElement)("input",{className:"gdg-title-input",value:r,placeholder:"Title",autoFocus:l,onChange:e=>{n(t,e.target.value)}}),(0,i.createElement)("input",{className:"gdg-link-input",value:t,placeholder:"URL",onChange:e=>{n(e.target.value,r)}}),void 0!==o&&(0,i.createElement)("button",{onClick:o},(0,i.createElement)("svg",{width:16,height:16,viewBox:"0 0 24 24",fill:"none",id:"icon-import",xmlns:"http://www.w3.org/2000/svg"},(0,i.createElement)("path",{d:"M3 6L5 6L21 6",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.createElement)("path",{d:"M17.9019 6C18.491 6 18.9525 6.50676 18.8975 7.09334L17.67 20.1867C17.5736 21.2144 16.711 22 15.6787 22H8.32127C7.28902 22 6.42635 21.2144 6.33 20.1867L5.1025 7.09334C5.04751 6.50676 5.50898 6 6.09813 6H17.9019Z",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.createElement)("path",{d:"M14.4499 10.211L13.9949 17",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.createElement)("path",{d:"M9.55499 10.211L10.0049 17",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.createElement)("path",{d:"M7.5 2.25H16.5",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}))))},Yt=jt;function Kt(e,t,n){if("string"===typeof e)return void 0!==t[e]?t[e]:e;let[r,i]=e;return void 0!==t[r]&&(r=t[r]),void 0!==t[i]&&(i=t[i]),(0,o.Nz)(r,i,n)}var $t={kind:o.p6.Custom,isMatch:e=>"button-cell"===e.data.kind,needsHover:!0,onSelect:e=>e.preventDefault(),onClick:e=>{var t,n;null==(n=(t=e.cell.data).onClick)||n.call(t)},drawPrep:e=>{const{ctx:t}=e;return t.textAlign="center",{deprep:e=>{e.ctx.textAlign="start"}}},draw:(e,t)=>{const{ctx:n,theme:r,rect:i,hoverAmount:l}=e,{title:a,backgroundColor:s,color:c,borderColor:u,borderRadius:d}=t.data,h=Math.floor(i.x+r.cellHorizontalPadding+1),p=Math.floor(i.y+r.cellVerticalPadding+1),f=Math.ceil(i.width-2*r.cellHorizontalPadding-1),g=Math.ceil(i.height-2*r.cellVerticalPadding-1);return void 0!==s&&(n.beginPath(),xt(n,h,p,f,g,null!=d?d:0),n.fillStyle=Kt(s,r,l),n.fill()),void 0!==u&&(n.beginPath(),xt(n,h+.5,p+.5,f-1,g-1,null!=d?d:0),n.strokeStyle=Kt(u,r,l),n.lineWidth=1,n.stroke()),n.fillStyle=Kt(null!=c?c:r.accentColor,r,l),n.fillText(a,h+f/2,p+g/2+(0,o.aX)(n,"".concat(r.baseFontStyle," ").concat(r.fontFamily))),!0},provideEditor:void 0},Gt=[bt,wt,Ct,Mt,Pt,Ht,At,Ft,Bt,Yt,$t];function qt(){return(0,o.R$)(Gt)}},37753:(e,t,n)=>{"use strict";n.d(t,{fF:()=>s});var r=n(35396),o=n(7974),i=n(66845);function l(e){var t,n,o,i,l;switch(e.kind){case r.p6.Number:return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:"";case r.p6.Boolean:return null!==(o=null===(i=e.data)||void 0===i?void 0:i.toString())&&void 0!==o?o:"";case r.p6.Markdown:case r.p6.RowID:case r.p6.Text:case r.p6.Uri:return null!==(l=e.data)&&void 0!==l?l:"";case r.p6.Bubble:case r.p6.Image:return e.data.join("");case r.p6.Drilldown:return e.data.map((e=>e.text)).join("");case r.p6.Protected:case r.p6.Loading:return"";case r.p6.Custom:return e.copyData}}function a(e){if("number"===typeof e)return e;if(e.length>0){const t=Number(e);isNaN(t)||(e=t)}return e}function s(e){var t;const{sort:n,rows:r,getCellContent:s}=e;let c=void 0===n?void 0:e.columns.findIndex((e=>n.column===e||void 0!==e.id&&n.column.id===e.id));-1===c&&(c=void 0);const u=null!==(t=null===n||void 0===n?void 0:n.direction)&&void 0!==t?t:"asc",d=i.useMemo((()=>{if(void 0===c)return;const e=new Array(r),t=[c,0];for(let n=0;n<r;n++)t[1]=n,e[n]=l(s(t));let i;return i="raw"===(null===n||void 0===n?void 0:n.mode)?o(r).sort(((t,n)=>function(e,t){return e>t?1:e===t?0:-1}(e[t],e[n]))):"smart"===(null===n||void 0===n?void 0:n.mode)?o(r).sort(((t,n)=>function(e,t){return e=a(e),t=a(t),"string"===typeof e&&"string"===typeof t?e.localeCompare(t):"number"===typeof e&&"number"===typeof t?e===t?0:e>t?1:-1:e==t?0:e>t?1:-1}(e[t],e[n]))):o(r).sort(((t,n)=>e[t].localeCompare(e[n]))),"desc"===u&&i.reverse(),i}),[s,r,null===n||void 0===n?void 0:n.mode,u,c]),h=i.useCallback((e=>void 0===d?e:d[e]),[d]),p=i.useCallback((e=>{let[t,n]=e;return void 0===d||(n=d[n]),s([t,n])}),[s,d]);return void 0===d?{getCellContent:e.getCellContent,getOriginalIndex:h}:{getOriginalIndex:h,getCellContent:p}}},47920:(e,t,n)=>{"use strict";n.d(t,{d:()=>u});var r=n(66845);var o=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|inert|itemProp|itemScope|itemType|itemID|itemRef|on|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,i=function(e){var t={};return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}((function(e){return o.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),l=function(){const e=Array.prototype.slice.call(arguments).filter(Boolean),t={},n=[];e.forEach((e=>{(e?e.split(" "):[]).forEach((e=>{if(e.startsWith("atm_")){const[,n]=e.split("_");t[n]=e}else n.push(e)}))}));const r=[];for(const o in t)Object.prototype.hasOwnProperty.call(t,o)&&r.push(t[o]);return r.push(...n),r.join(" ")},a=e=>e.toUpperCase()===e,s=(e,t)=>{const n={};return Object.keys(e).filter((e=>t=>-1===e.indexOf(t))(t)).forEach((t=>{n[t]=e[t]})),n};var c=(e,t)=>{{if("string"===typeof e||"number"===typeof e&&isFinite(e))return;const n="object"===typeof e?JSON.stringify(e):String(e);console.warn("An interpolation evaluated to '".concat(n,"' in the component '").concat(t,"', which is probably a mistake. You should explicitly cast or transform the value to a string."))}};var u=new Proxy((function(e){return t=>{if(Array.isArray(t))throw new Error('Using the "styled" tag in runtime is not supported. Make sure you have set up the Babel plugin correctly. See https://github.com/callstack/linaria#setup');const n=(n,o)=>{const{as:u=e,class:d}=n,h=function(e,t,n){const r=s(t,n);return"string"!==typeof e||-1!==e.indexOf("-")||a(e[0])||Object.keys(r).forEach((e=>{i(e)||delete r[e]})),r}(u,n,["as","class"]);h.ref=o,h.className=t.atomic?l(t.class,h.className||d):l(h.className||d,t.class);const{vars:p}=t;if(p){const e={};for(const i in p){const r=p[i],o=r[0],l=r[1]||"",a="function"===typeof o?o(n):o;c(a,t.name),e["--".concat(i)]="".concat(a).concat(l)}const r=h.style||{},o=Object.keys(r);o.length>0&&o.forEach((t=>{e[t]=r[t]})),h.style=e}return e.__linaria&&e!==u?(h.as=u,r.createElement(e,h)):r.createElement(u,h)},o=r.forwardRef?r.forwardRef(n):e=>{const t=s(e,["innerRef"]);return n(t,e.innerRef)};return o.displayName=t.name,o.__linaria={className:t.class,extends:e},o}}),{get:(e,t)=>e(t)})},35396:(e,t,n)=>{"use strict";n.d(t,{EV:()=>Ne,Nd:()=>Pr,p6:()=>me,t5:()=>dr,NH:()=>Ut,uN:()=>Rt,aX:()=>St,Nz:()=>Xt,P7:()=>kt,dF:()=>jt,R$:()=>Hr,Fg:()=>ge});var r=n(47920),o=n(66845),i=n(51586),l=n(17015),a=n(86995),s=n(7974),c=n(56797),u=n(17664),d=n(82781),h=n(19266),p=new Map,f=new Map,g=new Map;function m(e,t,n,r){var o,i;let l=f.get(n);if(r&&void 0!==l&&l.count>2e4){let r=g.get(n);if(void 0===r&&(r=function(e,t){var n;let r=new Map,o=0;for(let s of"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,.-+=?"){let t=e.measureText(s).width;r.set(s,t),o+=t}let i=o/r.size,l=(t/i+3)/4,a=r.keys();for(let s of a)r.set(s,(null!=(n=r.get(s))?n:i)*l);return r}(e,l.size),g.set(n,r)),l.count>5e5){let e=0;for(let n of t)e+=null!=(o=r.get(n))?o:l.size;return 1.01*e}let i=e.measureText(t);return function(e,t,n,r,o){var i,l,a;let s=0,c={};for(let d of e)s+=null!=(i=n.get(d))?i:o,c[d]=(null!=(l=c[d])?l:0)+1;let u=t-s;for(let d of Object.keys(c)){let e=c[d],t=null!=(a=n.get(d))?a:o,i=t+u*(t*e/s)*r/e;n.set(d,i)}}(t,i.width,r,Math.max(.05,1-l.count/2e5),l.size),f.set(n,{count:l.count+t.length,size:l.size}),i.width}let a=e.measureText(t),s=a.width/t.length;if((null!=(i=null==l?void 0:l.count)?i:0)>2e4)return a.width;if(void 0===l)f.set(n,{count:t.length,size:s});else{let e=s-l.size,r=t.length/(l.count+t.length),o=l.size+e*r;f.set(n,{count:l.count+t.length,size:o})}return a.width}function v(e,t,n,r,o,i,l,a){if(t.length<=1)return t.length;if(o<n)return-1;let s=Math.floor(n/o*i),c=m(e,t.slice(0,Math.max(0,s)),r,l),u=null==a?void 0:a(t);if(c!==n)if(c<n){for(;c<n;)s++,c=m(e,t.slice(0,Math.max(0,s)),r,l);s--}else for(;c>n;){let n=void 0!==u?0:t.lastIndexOf(" ",s-1);n>0?s=n:s--,c=m(e,t.slice(0,Math.max(0,s)),r,l)}if(" "!==t[s]){let e=0;if(void 0===u)e=t.lastIndexOf(" ",s);else for(let t of u){if(t>s)break;e=t}e>0&&(s=e)}return s}var b=n(76236),y=n(44303),w=n(64649);function x(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let k={async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1};const S=/[&<>"']/,C=new RegExp(S.source,"g"),M=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,E=new RegExp(M.source,"g"),R={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},T=e=>R[e];function I(e,t){if(t){if(S.test(e))return e.replace(C,T)}else if(M.test(e))return e.replace(E,T);return e}const O=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function P(e){return e.replace(O,((e,t)=>"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""))}const D=/(^|[^\[])\^/g;function H(e,t){e="string"===typeof e?e:e.source,t=t||"";const n={replace:(t,r)=>(r=(r=r.source||r).replace(D,"$1"),e=e.replace(t,r),n),getRegex:()=>new RegExp(e,t)};return n}const L=/[^\w:]/g,z=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function F(e,t,n){if(e){let e;try{e=decodeURIComponent(P(n)).replace(L,"").toLowerCase()}catch(r){return null}if(0===e.indexOf("javascript:")||0===e.indexOf("vbscript:")||0===e.indexOf("data:"))return null}t&&!z.test(n)&&(n=function(e,t){A[" "+e]||(_.test(e)?A[" "+e]=e+"/":A[" "+e]=j(e,"/",!0));e=A[" "+e];const n=-1===e.indexOf(":");return"//"===t.substring(0,2)?n?t:e.replace(V,"$1")+t:"/"===t.charAt(0)?n?t:e.replace(N,"$1")+t:e+t}(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch(r){return null}return n}const A={},_=/^[^:]+:\/*[^/]*$/,V=/^([^:]+:)[\s\S]*$/,N=/^([^:]+:\/*[^/]*)[\s\S]*$/;const B={exec:function(){}};function W(e,t){const n=e.replace(/\|/g,((e,t,n)=>{let r=!1,o=t;for(;--o>=0&&"\\"===n[o];)r=!r;return r?"|":" |"})).split(/ \|/);let r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function j(e,t,n){const r=e.length;if(0===r)return"";let o=0;for(;o<r;){const i=e.charAt(r-o-1);if(i!==t||n){if(i===t||!n)break;o++}else o++}return e.slice(0,r-o)}function Z(e,t){if(t<1)return"";let n="";for(;t>1;)1&t&&(n+=e),t>>=1,e+=e;return n+e}function U(e,t,n,r){const o=t.href,i=t.title?I(t.title):null,l=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){r.state.inLink=!0;const e={type:"link",raw:n,href:o,title:i,text:l,tokens:r.inlineTokens(l)};return r.state.inLink=!1,e}return{type:"image",raw:n,href:o,title:i,text:I(l)}}class X{constructor(e){this.options=e||k}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const e=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:j(e,"\n")}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const e=t[0],n=function(e,t){const n=e.match(/^(\s+)(?:```)/);if(null===n)return t;const r=n[1];return t.split("\n").map((e=>{const t=e.match(/^\s+/);if(null===t)return e;const[n]=t;return n.length>=r.length?e.slice(r.length):e})).join("\n")}(e,t[3]||"");return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline._escapes,"$1"):t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(/#$/.test(e)){const t=j(e,"#");this.options.pedantic?e=t.trim():t&&!/ $/.test(t)||(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const e=t[0].replace(/^ *>[ \t]?/gm,""),n=this.lexer.state.top;this.lexer.state.top=!0;const r=this.lexer.blockTokens(e);return this.lexer.state.top=n,{type:"blockquote",raw:t[0],tokens:r,text:e}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n,r,o,i,l,a,s,c,u,d,h,p,f=t[1].trim();const g=f.length>1,m={type:"list",raw:"",ordered:g,start:g?+f.slice(0,-1):"",loose:!1,items:[]};f=g?"\\d{1,9}\\".concat(f.slice(-1)):"\\".concat(f),this.options.pedantic&&(f=g?f:"[*+-]");const v=new RegExp("^( {0,3}".concat(f,")((?:[\t ][^\\n]*)?(?:\\n|$))"));for(;e&&(p=!1,t=v.exec(e))&&!this.rules.block.hr.test(e);){if(n=t[0],e=e.substring(n.length),c=t[2].split("\n",1)[0].replace(/^\t+/,(e=>" ".repeat(3*e.length))),u=e.split("\n",1)[0],this.options.pedantic?(i=2,h=c.trimLeft()):(i=t[2].search(/[^ ]/),i=i>4?1:i,h=c.slice(i),i+=t[1].length),a=!1,!c&&/^ *$/.test(u)&&(n+=u+"\n",e=e.substring(u.length+1),p=!0),!p){const t=new RegExp("^ {0,".concat(Math.min(3,i-1),"}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))")),r=new RegExp("^ {0,".concat(Math.min(3,i-1),"}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)")),o=new RegExp("^ {0,".concat(Math.min(3,i-1),"}(?:```|~~~)")),l=new RegExp("^ {0,".concat(Math.min(3,i-1),"}#"));for(;e&&(d=e.split("\n",1)[0],u=d,this.options.pedantic&&(u=u.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!o.test(u))&&!l.test(u)&&!t.test(u)&&!r.test(e);){if(u.search(/[^ ]/)>=i||!u.trim())h+="\n"+u.slice(i);else{if(a)break;if(c.search(/[^ ]/)>=4)break;if(o.test(c))break;if(l.test(c))break;if(r.test(c))break;h+="\n"+u}a||u.trim()||(a=!0),n+=d+"\n",e=e.substring(d.length+1),c=u.slice(i)}}m.loose||(s?m.loose=!0:/\n *\n *$/.test(n)&&(s=!0)),this.options.gfm&&(r=/^\[[ xX]\] /.exec(h),r&&(o="[ ] "!==r[0],h=h.replace(/^\[[ xX]\] +/,""))),m.items.push({type:"list_item",raw:n,task:!!r,checked:o,loose:!1,text:h}),m.raw+=n}m.items[m.items.length-1].raw=n.trimRight(),m.items[m.items.length-1].text=h.trimRight(),m.raw=m.raw.trimRight();const b=m.items.length;for(l=0;l<b;l++)if(this.lexer.state.top=!1,m.items[l].tokens=this.lexer.blockTokens(m.items[l].text,[]),!m.loose){const e=m.items[l].tokens.filter((e=>"space"===e.type)),t=e.length>0&&e.some((e=>/\n.*\n/.test(e.raw)));m.loose=t}if(m.loose)for(l=0;l<b;l++)m.items[l].loose=!0;return m}}html(e){const t=this.rules.block.html.exec(e);if(t){const e={type:"html",raw:t[0],pre:!this.options.sanitizer&&("pre"===t[1]||"script"===t[1]||"style"===t[1]),text:t[0]};if(this.options.sanitize){const n=this.options.sanitizer?this.options.sanitizer(t[0]):I(t[0]);e.type="paragraph",e.text=n,e.tokens=this.lexer.inline(n)}return e}}def(e){const t=this.rules.block.def.exec(e);if(t){const e=t[1].toLowerCase().replace(/\s+/g," "),n=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline._escapes,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){const t=this.rules.block.table.exec(e);if(t){const e={type:"table",header:W(t[1]).map((e=>({text:e}))),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(e.header.length===e.align.length){e.raw=t[0];let n,r,o,i,l=e.align.length;for(n=0;n<l;n++)/^ *-+: *$/.test(e.align[n])?e.align[n]="right":/^ *:-+: *$/.test(e.align[n])?e.align[n]="center":/^ *:-+ *$/.test(e.align[n])?e.align[n]="left":e.align[n]=null;for(l=e.rows.length,n=0;n<l;n++)e.rows[n]=W(e.rows[n],e.header.length).map((e=>({text:e})));for(l=e.header.length,r=0;r<l;r++)e.header[r].tokens=this.lexer.inline(e.header[r].text);for(l=e.rows.length,r=0;r<l;r++)for(i=e.rows[r],o=0;o<i.length;o++)i[o].tokens=this.lexer.inline(i[o].text);return e}}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:I(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):I(t[0]):t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const e=t[2].trim();if(!this.options.pedantic&&/^</.test(e)){if(!/>$/.test(e))return;const t=j(e.slice(0,-1),"\\");if((e.length-t.length)%2===0)return}else{const e=function(e,t){if(-1===e.indexOf(t[1]))return-1;const n=e.length;let r=0,o=0;for(;o<n;o++)if("\\"===e[o])o++;else if(e[o]===t[0])r++;else if(e[o]===t[1]&&(r--,r<0))return o;return-1}(t[2],"()");if(e>-1){const n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){const e=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(e)?n.slice(1):n.slice(1,-1)),U(t,{href:n?n.replace(this.rules.inline._escapes,"$1"):n,title:r?r.replace(this.rules.inline._escapes,"$1"):r},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let e=(n[2]||n[1]).replace(/\s+/g," ");if(e=t[e.toLowerCase()],!e){const e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return U(n,e,n[0],this.lexer)}}emStrong(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=this.rules.inline.emStrong.lDelim.exec(e);if(!r)return;if(r[3]&&n.match(/[\p{L}\p{N}]/u))return;const o=r[1]||r[2]||"";if(!o||o&&(""===n||this.rules.inline.punctuation.exec(n))){const n=r[0].length-1;let o,i,l=n,a=0;const s="*"===r[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(s.lastIndex=0,t=t.slice(-1*e.length+n);null!=(r=s.exec(t));){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(i=o.length,r[3]||r[4]){l+=i;continue}if((r[5]||r[6])&&n%3&&!((n+i)%3)){a+=i;continue}if(l-=i,l>0)continue;i=Math.min(i,i+l+a);const t=e.slice(0,n+r.index+(r[0].length-o.length)+i);if(Math.min(n,i)%2){const e=t.slice(1,-1);return{type:"em",raw:t,text:e,tokens:this.lexer.inlineTokens(e)}}const s=t.slice(2,-2);return{type:"strong",raw:t,text:s,tokens:this.lexer.inlineTokens(s)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(/\n/g," ");const n=/[^ ]/.test(e),r=/^ /.test(e)&&/ $/.test(e);return n&&r&&(e=e.substring(1,e.length-1)),e=I(e,!0),{type:"codespan",raw:t[0],text:e}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e,t){const n=this.rules.inline.autolink.exec(e);if(n){let e,r;return"@"===n[2]?(e=I(this.options.mangle?t(n[1]):n[1]),r="mailto:"+e):(e=I(n[1]),r=e),{type:"link",raw:n[0],text:e,href:r,tokens:[{type:"text",raw:e,text:e}]}}}url(e,t){let n;if(n=this.rules.inline.url.exec(e)){let e,r;if("@"===n[2])e=I(this.options.mangle?t(n[0]):n[0]),r="mailto:"+e;else{let t;do{t=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0]}while(t!==n[0]);e=I(n[0]),r="www."===n[1]?"http://"+n[0]:n[0]}return{type:"link",raw:n[0],text:e,href:r,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e,t){const n=this.rules.inline.text.exec(e);if(n){let e;return e=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):I(n[0]):n[0]:I(this.options.smartypants?t(n[0]):n[0]),{type:"text",raw:n[0],text:e}}}}const Y={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:B,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/};Y.def=H(Y.def).replace("label",Y._label).replace("title",Y._title).getRegex(),Y.bullet=/(?:[*+-]|\d{1,9}[.)])/,Y.listItemStart=H(/^( *)(bull) */).replace("bull",Y.bullet).getRegex(),Y.list=H(Y.list).replace(/bull/g,Y.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+Y.def.source+")").getRegex(),Y._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Y._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,Y.html=H(Y.html,"i").replace("comment",Y._comment).replace("tag",Y._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Y.paragraph=H(Y._paragraph).replace("hr",Y.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Y._tag).getRegex(),Y.blockquote=H(Y.blockquote).replace("paragraph",Y.paragraph).getRegex(),Y.normal={...Y},Y.gfm={...Y.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"},Y.gfm.table=H(Y.gfm.table).replace("hr",Y.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Y._tag).getRegex(),Y.gfm.paragraph=H(Y._paragraph).replace("hr",Y.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",Y.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Y._tag).getRegex(),Y.pedantic={...Y.normal,html:H("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",Y._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:B,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:H(Y.normal._paragraph).replace("hr",Y.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",Y.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const K={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:B,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:B,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};function $(e){return e.replace(/---/g,"\u2014").replace(/--/g,"\u2013").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1\u2018").replace(/'/g,"\u2019").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1\u201c").replace(/"/g,"\u201d").replace(/\.{3}/g,"\u2026")}function G(e){let t,n,r="";const o=e.length;for(t=0;t<o;t++)n=e.charCodeAt(t),Math.random()>.5&&(n="x"+n.toString(16)),r+="&#"+n+";";return r}K._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",K.punctuation=H(K.punctuation).replace(/punctuation/g,K._punctuation).getRegex(),K.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,K.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g,K._comment=H(Y._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),K.emStrong.lDelim=H(K.emStrong.lDelim).replace(/punct/g,K._punctuation).getRegex(),K.emStrong.rDelimAst=H(K.emStrong.rDelimAst,"g").replace(/punct/g,K._punctuation).getRegex(),K.emStrong.rDelimUnd=H(K.emStrong.rDelimUnd,"g").replace(/punct/g,K._punctuation).getRegex(),K._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,K._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,K._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,K.autolink=H(K.autolink).replace("scheme",K._scheme).replace("email",K._email).getRegex(),K._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,K.tag=H(K.tag).replace("comment",K._comment).replace("attribute",K._attribute).getRegex(),K._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,K._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,K._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,K.link=H(K.link).replace("label",K._label).replace("href",K._href).replace("title",K._title).getRegex(),K.reflink=H(K.reflink).replace("label",K._label).replace("ref",Y._label).getRegex(),K.nolink=H(K.nolink).replace("ref",Y._label).getRegex(),K.reflinkSearch=H(K.reflinkSearch,"g").replace("reflink",K.reflink).replace("nolink",K.nolink).getRegex(),K.normal={...K},K.pedantic={...K.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:H(/^!?\[(label)\]\((.*?)\)/).replace("label",K._label).getRegex(),reflink:H(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",K._label).getRegex()},K.gfm={...K.normal,escape:H(K.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},K.gfm.url=H(K.gfm.url,"i").replace("email",K.gfm._extended_email).getRegex(),K.breaks={...K.gfm,br:H(K.br).replace("{2,}","*").getRegex(),text:H(K.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};class q{constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||k,this.options.tokenizer=this.options.tokenizer||new X,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:Y.normal,inline:K.normal};this.options.pedantic?(t.block=Y.pedantic,t.inline=K.pedantic):this.options.gfm&&(t.block=Y.gfm,this.options.breaks?t.inline=K.breaks:t.inline=K.gfm),this.tokenizer.rules=t}static get rules(){return{block:Y,inline:K}}static lex(e,t){return new q(t).lex(e)}static lexInline(e,t){return new q(t).inlineTokens(e)}lex(e){let t;for(e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens}blockTokens(e){let t,n,r,o,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,((e,t,n)=>t+"    ".repeat(n.length)));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((n=>!!(t=n.call({lexer:this},e,i))&&(e=e.substring(t.raw.length),i.push(t),!0)))))if(t=this.tokenizer.space(e))e=e.substring(t.raw.length),1===t.raw.length&&i.length>0?i[i.length-1].raw+="\n":i.push(t);else if(t=this.tokenizer.code(e))e=e.substring(t.raw.length),n=i[i.length-1],!n||"paragraph"!==n.type&&"text"!==n.type?i.push(t):(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(t=this.tokenizer.fences(e))e=e.substring(t.raw.length),i.push(t);else if(t=this.tokenizer.heading(e))e=e.substring(t.raw.length),i.push(t);else if(t=this.tokenizer.hr(e))e=e.substring(t.raw.length),i.push(t);else if(t=this.tokenizer.blockquote(e))e=e.substring(t.raw.length),i.push(t);else if(t=this.tokenizer.list(e))e=e.substring(t.raw.length),i.push(t);else if(t=this.tokenizer.html(e))e=e.substring(t.raw.length),i.push(t);else if(t=this.tokenizer.def(e))e=e.substring(t.raw.length),n=i[i.length-1],!n||"paragraph"!==n.type&&"text"!==n.type?this.tokens.links[t.tag]||(this.tokens.links[t.tag]={href:t.href,title:t.title}):(n.raw+="\n"+t.raw,n.text+="\n"+t.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(t=this.tokenizer.table(e))e=e.substring(t.raw.length),i.push(t);else if(t=this.tokenizer.lheading(e))e=e.substring(t.raw.length),i.push(t);else{if(r=e,this.options.extensions&&this.options.extensions.startBlock){let t=1/0;const n=e.slice(1);let o;this.options.extensions.startBlock.forEach((function(e){o=e.call({lexer:this},n),"number"===typeof o&&o>=0&&(t=Math.min(t,o))})),t<1/0&&t>=0&&(r=e.substring(0,t+1))}if(this.state.top&&(t=this.tokenizer.paragraph(r)))n=i[i.length-1],o&&"paragraph"===n.type?(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):i.push(t),o=r.length!==e.length,e=e.substring(t.raw.length);else if(t=this.tokenizer.text(e))e=e.substring(t.raw.length),n=i[i.length-1],n&&"text"===n.type?(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):i.push(t);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return this.state.top=!0,i}inline(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e){let t,n,r,o,i,l,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=e;if(this.tokens.links){const e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(o=this.tokenizer.rules.inline.reflinkSearch.exec(s));)e.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,o.index)+"["+Z("a",o[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(o=this.tokenizer.rules.inline.blockSkip.exec(s));)s=s.slice(0,o.index)+"["+Z("a",o[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(o=this.tokenizer.rules.inline.escapedEmSt.exec(s));)s=s.slice(0,o.index+o[0].length-2)+"++"+s.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;e;)if(i||(l=""),i=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((n=>!!(t=n.call({lexer:this},e,a))&&(e=e.substring(t.raw.length),a.push(t),!0)))))if(t=this.tokenizer.escape(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.tag(e))e=e.substring(t.raw.length),n=a[a.length-1],n&&"text"===t.type&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):a.push(t);else if(t=this.tokenizer.link(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(t.raw.length),n=a[a.length-1],n&&"text"===t.type&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):a.push(t);else if(t=this.tokenizer.emStrong(e,s,l))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.codespan(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.br(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.del(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.autolink(e,G))e=e.substring(t.raw.length),a.push(t);else if(this.state.inLink||!(t=this.tokenizer.url(e,G))){if(r=e,this.options.extensions&&this.options.extensions.startInline){let t=1/0;const n=e.slice(1);let o;this.options.extensions.startInline.forEach((function(e){o=e.call({lexer:this},n),"number"===typeof o&&o>=0&&(t=Math.min(t,o))})),t<1/0&&t>=0&&(r=e.substring(0,t+1))}if(t=this.tokenizer.inlineText(r,$))e=e.substring(t.raw.length),"_"!==t.raw.slice(-1)&&(l=t.raw.slice(-1)),i=!0,n=a[a.length-1],n&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):a.push(t);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}else e=e.substring(t.raw.length),a.push(t);return a}}class Q{constructor(e){this.options=e||k}code(e,t,n){const r=(t||"").match(/\S*/)[0];if(this.options.highlight){const t=this.options.highlight(e,r);null!=t&&t!==e&&(n=!0,e=t)}return e=e.replace(/\n$/,"")+"\n",r?'<pre><code class="'+this.options.langPrefix+I(r)+'">'+(n?e:I(e,!0))+"</code></pre>\n":"<pre><code>"+(n?e:I(e,!0))+"</code></pre>\n"}blockquote(e){return"<blockquote>\n".concat(e,"</blockquote>\n")}html(e){return e}heading(e,t,n,r){if(this.options.headerIds){const o=this.options.headerPrefix+r.slug(n);return"<h".concat(t,' id="').concat(o,'">').concat(e,"</h").concat(t,">\n")}return"<h".concat(t,">").concat(e,"</h").concat(t,">\n")}hr(){return this.options.xhtml?"<hr/>\n":"<hr>\n"}list(e,t,n){const r=t?"ol":"ul";return"<"+r+(t&&1!==n?' start="'+n+'"':"")+">\n"+e+"</"+r+">\n"}listitem(e){return"<li>".concat(e,"</li>\n")}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(e){return"<p>".concat(e,"</p>\n")}table(e,t){return t&&(t="<tbody>".concat(t,"</tbody>")),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"}tablerow(e){return"<tr>\n".concat(e,"</tr>\n")}tablecell(e,t){const n=t.header?"th":"td";return(t.align?"<".concat(n,' align="').concat(t.align,'">'):"<".concat(n,">"))+e+"</".concat(n,">\n")}strong(e){return"<strong>".concat(e,"</strong>")}em(e){return"<em>".concat(e,"</em>")}codespan(e){return"<code>".concat(e,"</code>")}br(){return this.options.xhtml?"<br/>":"<br>"}del(e){return"<del>".concat(e,"</del>")}link(e,t,n){if(null===(e=F(this.options.sanitize,this.options.baseUrl,e)))return n;let r='<a href="'+e+'"';return t&&(r+=' title="'+t+'"'),r+=">"+n+"</a>",r}image(e,t,n){if(null===(e=F(this.options.sanitize,this.options.baseUrl,e)))return n;let r='<img src="'.concat(e,'" alt="').concat(n,'"');return t&&(r+=' title="'.concat(t,'"')),r+=this.options.xhtml?"/>":">",r}text(e){return e}}class J{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class ee{constructor(){this.seen={}}serialize(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(e,t){let n=e,r=0;if(this.seen.hasOwnProperty(n)){r=this.seen[e];do{r++,n=e+"-"+r}while(this.seen.hasOwnProperty(n))}return t||(this.seen[e]=r,this.seen[n]=0),n}slug(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.serialize(e);return this.getNextSafeSlug(n,t.dryrun)}}class te{constructor(e){this.options=e||k,this.options.renderer=this.options.renderer||new Q,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new J,this.slugger=new ee}static parse(e,t){return new te(t).parse(e)}static parseInline(e,t){return new te(t).parseInline(e)}parse(e){let t,n,r,o,i,l,a,s,c,u,d,h,p,f,g,m,v,b,y,w=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],x="";const k=e.length;for(t=0;t<k;t++)if(u=e[t],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[u.type]&&(y=this.options.extensions.renderers[u.type].call({parser:this},u),!1!==y||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(u.type)))x+=y||"";else switch(u.type){case"space":continue;case"hr":x+=this.renderer.hr();continue;case"heading":x+=this.renderer.heading(this.parseInline(u.tokens),u.depth,P(this.parseInline(u.tokens,this.textRenderer)),this.slugger);continue;case"code":x+=this.renderer.code(u.text,u.lang,u.escaped);continue;case"table":for(s="",a="",o=u.header.length,n=0;n<o;n++)a+=this.renderer.tablecell(this.parseInline(u.header[n].tokens),{header:!0,align:u.align[n]});for(s+=this.renderer.tablerow(a),c="",o=u.rows.length,n=0;n<o;n++){for(l=u.rows[n],a="",i=l.length,r=0;r<i;r++)a+=this.renderer.tablecell(this.parseInline(l[r].tokens),{header:!1,align:u.align[r]});c+=this.renderer.tablerow(a)}x+=this.renderer.table(s,c);continue;case"blockquote":c=this.parse(u.tokens),x+=this.renderer.blockquote(c);continue;case"list":for(d=u.ordered,h=u.start,p=u.loose,o=u.items.length,c="",n=0;n<o;n++)g=u.items[n],m=g.checked,v=g.task,f="",g.task&&(b=this.renderer.checkbox(m),p?g.tokens.length>0&&"paragraph"===g.tokens[0].type?(g.tokens[0].text=b+" "+g.tokens[0].text,g.tokens[0].tokens&&g.tokens[0].tokens.length>0&&"text"===g.tokens[0].tokens[0].type&&(g.tokens[0].tokens[0].text=b+" "+g.tokens[0].tokens[0].text)):g.tokens.unshift({type:"text",text:b}):f+=b),f+=this.parse(g.tokens,p),c+=this.renderer.listitem(f,v,m);x+=this.renderer.list(c,d,h);continue;case"html":x+=this.renderer.html(u.text);continue;case"paragraph":x+=this.renderer.paragraph(this.parseInline(u.tokens));continue;case"text":for(c=u.tokens?this.parseInline(u.tokens):u.text;t+1<k&&"text"===e[t+1].type;)u=e[++t],c+="\n"+(u.tokens?this.parseInline(u.tokens):u.text);x+=w?this.renderer.paragraph(c):c;continue;default:{const e='Token with "'+u.type+'" type was not found.';if(this.options.silent)return void console.error(e);throw new Error(e)}}return x}parseInline(e,t){t=t||this.renderer;let n,r,o,i="";const l=e.length;for(n=0;n<l;n++)if(r=e[n],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]&&(o=this.options.extensions.renderers[r.type].call({parser:this},r),!1!==o||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)))i+=o||"";else switch(r.type){case"escape":case"text":i+=t.text(r.text);break;case"html":i+=t.html(r.text);break;case"link":i+=t.link(r.href,r.title,this.parseInline(r.tokens,t));break;case"image":i+=t.image(r.href,r.title,r.text);break;case"strong":i+=t.strong(this.parseInline(r.tokens,t));break;case"em":i+=t.em(this.parseInline(r.tokens,t));break;case"codespan":i+=t.codespan(r.text);break;case"br":i+=t.br();break;case"del":i+=t.del(this.parseInline(r.tokens,t));break;default:{const e='Token with "'+r.type+'" type was not found.';if(this.options.silent)return void console.error(e);throw new Error(e)}}return i}}class ne{constructor(e){this.options=e||k}preprocess(e){return e}postprocess(e){return e}}function re(e,t){return(n,r,o)=>{"function"===typeof r&&(o=r,r=null);const i={...r},l=function(e,t,n){return r=>{if(r.message+="\nPlease report this to https://github.com/markedjs/marked.",e){const e="<p>An error occurred:</p><pre>"+I(r.message+"",!0)+"</pre>";return t?Promise.resolve(e):n?void n(null,e):e}if(t)return Promise.reject(r);if(!n)throw r;n(r)}}((r={...oe.defaults,...i}).silent,r.async,o);if("undefined"===typeof n||null===n)return l(new Error("marked(): input parameter is undefined or null"));if("string"!==typeof n)return l(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(function(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}(r),r.hooks&&(r.hooks.options=r),o){const i=r.highlight;let s;try{r.hooks&&(n=r.hooks.preprocess(n)),s=e(n,r)}catch(a){return l(a)}const c=function(e){let n;if(!e)try{r.walkTokens&&oe.walkTokens(s,r.walkTokens),n=t(s,r),r.hooks&&(n=r.hooks.postprocess(n))}catch(a){e=a}return r.highlight=i,e?l(e):o(null,n)};if(!i||i.length<3)return c();if(delete r.highlight,!s.length)return c();let u=0;return oe.walkTokens(s,(function(e){"code"===e.type&&(u++,setTimeout((()=>{i(e.text,e.lang,(function(t,n){if(t)return c(t);null!=n&&n!==e.text&&(e.text=n,e.escaped=!0),u--,0===u&&c()}))}),0))})),void(0===u&&c())}if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(n):n).then((t=>e(t,r))).then((e=>r.walkTokens?Promise.all(oe.walkTokens(e,r.walkTokens)).then((()=>e)):e)).then((e=>t(e,r))).then((e=>r.hooks?r.hooks.postprocess(e):e)).catch(l);try{r.hooks&&(n=r.hooks.preprocess(n));const o=e(n,r);r.walkTokens&&oe.walkTokens(o,r.walkTokens);let i=t(o,r);return r.hooks&&(i=r.hooks.postprocess(i)),i}catch(a){return l(a)}}}function oe(e,t,n){return re(q.lex,te.parse)(e,t,n)}(0,w.Z)(ne,"passThroughHooks",new Set(["preprocess","postprocess"])),oe.options=oe.setOptions=function(e){var t;return oe.defaults={...oe.defaults,...e},t=oe.defaults,k=t,oe},oe.getDefaults=x,oe.defaults=k,oe.use=function(){const e=oe.defaults.extensions||{renderers:{},childTokens:{}};for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];n.forEach((t=>{const n={...t};if(n.async=oe.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach((t=>{if(!t.name)throw new Error("extension name required");if(t.renderer){const n=e.renderers[t.name];e.renderers[t.name]=n?function(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];let i=t.renderer.apply(this,r);return!1===i&&(i=n.apply(this,r)),i}:t.renderer}if(t.tokenizer){if(!t.level||"block"!==t.level&&"inline"!==t.level)throw new Error("extension level must be 'block' or 'inline'");e[t.level]?e[t.level].unshift(t.tokenizer):e[t.level]=[t.tokenizer],t.start&&("block"===t.level?e.startBlock?e.startBlock.push(t.start):e.startBlock=[t.start]:"inline"===t.level&&(e.startInline?e.startInline.push(t.start):e.startInline=[t.start]))}t.childTokens&&(e.childTokens[t.name]=t.childTokens)})),n.extensions=e),t.renderer){const e=oe.defaults.renderer||new Q;for(const n in t.renderer){const r=e[n];e[n]=function(){for(var o=arguments.length,i=new Array(o),l=0;l<o;l++)i[l]=arguments[l];let a=t.renderer[n].apply(e,i);return!1===a&&(a=r.apply(e,i)),a}}n.renderer=e}if(t.tokenizer){const e=oe.defaults.tokenizer||new X;for(const n in t.tokenizer){const r=e[n];e[n]=function(){for(var o=arguments.length,i=new Array(o),l=0;l<o;l++)i[l]=arguments[l];let a=t.tokenizer[n].apply(e,i);return!1===a&&(a=r.apply(e,i)),a}}n.tokenizer=e}if(t.hooks){const e=oe.defaults.hooks||new ne;for(const n in t.hooks){const r=e[n];ne.passThroughHooks.has(n)?e[n]=o=>{if(oe.defaults.async)return Promise.resolve(t.hooks[n].call(e,o)).then((t=>r.call(e,t)));const i=t.hooks[n].call(e,o);return r.call(e,i)}:e[n]=function(){for(var o=arguments.length,i=new Array(o),l=0;l<o;l++)i[l]=arguments[l];let a=t.hooks[n].apply(e,i);return!1===a&&(a=r.apply(e,i)),a}}n.hooks=e}if(t.walkTokens){const e=oe.defaults.walkTokens;n.walkTokens=function(n){let r=[];return r.push(t.walkTokens.call(this,n)),e&&(r=r.concat(e.call(this,n))),r}}oe.setOptions(n)}))},oe.walkTokens=function(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(oe,r)),r.type){case"table":for(const e of r.header)n=n.concat(oe.walkTokens(e.tokens,t));for(const e of r.rows)for(const r of e)n=n.concat(oe.walkTokens(r.tokens,t));break;case"list":n=n.concat(oe.walkTokens(r.items,t));break;default:oe.defaults.extensions&&oe.defaults.extensions.childTokens&&oe.defaults.extensions.childTokens[r.type]?oe.defaults.extensions.childTokens[r.type].forEach((function(e){n=n.concat(oe.walkTokens(r[e],t))})):r.tokens&&(n=n.concat(oe.walkTokens(r.tokens,t)))}return n},oe.parseInline=re(q.lexInline,te.parseInline),oe.Parser=te,oe.parser=te.parse,oe.Renderer=Q,oe.TextRenderer=J,oe.Lexer=q,oe.lexer=q.lex,oe.Tokenizer=X,oe.Slugger=ee,oe.Hooks=ne,oe.parse=oe;oe.options,oe.setOptions,oe.use,oe.walkTokens,oe.parseInline,te.parse,q.lex;function ie(){throw new Error(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This should not happen")}function le(e){if(!e)return ie(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Assertion failed")}function ae(e,t){return ie(null!=t?t:"Hell froze over")}var se=Object.prototype.hasOwnProperty;function ce(e,t){let n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&ce(e[r],t[r]););return-1===r}if(!n||"object"===typeof e){for(n in r=0,e){if(se.call(e,n)&&++r&&!se.call(t,n))return!1;if(!(n in t)||!ce(e[n],t[n]))return!1}return Object.keys(t).length===r}}return e!==e&&t!==t}var ue=class extends o.PureComponent{constructor(){super(...arguments),this.wrapperRef=o.createRef(),this.clickOutside=e=>{if((!this.props.isOutsideClick||this.props.isOutsideClick(e))&&null!==this.wrapperRef.current&&!this.wrapperRef.current.contains(e.target)){let t=e.target;for(;null!==t;){if(t.classList.contains("click-outside-ignore"))return;t=t.parentElement}this.props.onClickOutside()}}}componentDidMount(){document.addEventListener("touchend",this.clickOutside,!0),document.addEventListener("mousedown",this.clickOutside,!0),document.addEventListener("contextmenu",this.clickOutside,!0)}componentWillUnmount(){document.removeEventListener("touchend",this.clickOutside,!0),document.removeEventListener("mousedown",this.clickOutside,!0),document.removeEventListener("contextmenu",this.clickOutside,!0)}render(){const{onClickOutside:e,isOutsideClick:t,...n}=this.props;return o.createElement("div",{...n,ref:this.wrapperRef},this.props.children)}};function de(e){var t,n;return{"--gdg-accent-color":e.accentColor,"--gdg-accent-fg":e.accentFg,"--gdg-accent-light":e.accentLight,"--gdg-text-dark":e.textDark,"--gdg-text-medium":e.textMedium,"--gdg-text-light":e.textLight,"--gdg-text-bubble":e.textBubble,"--gdg-bg-icon-header":e.bgIconHeader,"--gdg-fg-icon-header":e.fgIconHeader,"--gdg-text-header":e.textHeader,"--gdg-text-group-header":null!=(t=e.textGroupHeader)?t:e.textHeader,"--gdg-text-header-selected":e.textHeaderSelected,"--gdg-bg-cell":e.bgCell,"--gdg-bg-cell-medium":e.bgCellMedium,"--gdg-bg-header":e.bgHeader,"--gdg-bg-header-has-focus":e.bgHeaderHasFocus,"--gdg-bg-header-hovered":e.bgHeaderHovered,"--gdg-bg-bubble":e.bgBubble,"--gdg-bg-bubble-selected":e.bgBubbleSelected,"--gdg-bg-search-result":e.bgSearchResult,"--gdg-border-color":e.borderColor,"--gdg-horizontal-border-color":null!=(n=e.horizontalBorderColor)?n:e.borderColor,"--gdg-drilldown-border":e.drilldownBorder,"--gdg-link-color":e.linkColor,"--gdg-cell-horizontal-padding":"".concat(e.cellHorizontalPadding,"px"),"--gdg-cell-vertical-padding":"".concat(e.cellVerticalPadding,"px"),"--gdg-header-font-style":e.headerFontStyle,"--gdg-base-font-style":e.baseFontStyle,"--gdg-marker-font-style":e.markerFontStyle,"--gdg-font-family":e.fontFamily,"--gdg-editor-font-size":e.editorFontSize}}var he={accentColor:"#4F5DFF",accentFg:"#FFFFFF",accentLight:"rgba(62, 116, 253, 0.1)",textDark:"#313139",textMedium:"#737383",textLight:"#B2B2C0",textBubble:"#313139",bgIconHeader:"#737383",fgIconHeader:"#FFFFFF",textHeader:"#313139",textGroupHeader:"#313139BB",textHeaderSelected:"#FFFFFF",bgCell:"#FFFFFF",bgCellMedium:"#FAFAFB",bgHeader:"#F7F7F8",bgHeaderHasFocus:"#E9E9EB",bgHeaderHovered:"#EFEFF1",bgBubble:"#EDEDF3",bgBubbleSelected:"#FFFFFF",bgSearchResult:"#fff9e3",borderColor:"rgba(115, 116, 131, 0.16)",drilldownBorder:"rgba(0, 0, 0, 0)",linkColor:"#4F5DFF",cellHorizontalPadding:8,cellVerticalPadding:3,headerIconSize:18,headerFontStyle:"600 13px",baseFontStyle:"13px",markerFontStyle:"9px",fontFamily:"Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif",editorFontSize:"13px",lineHeight:1.4};function pe(){return he}var fe=o.createContext(he);function ge(){return o.useContext(fe)}var me,ve,be,ye,we=null,xe=void 0,ke="header",Se="group-header",Ce="out-of-bounds";(ve=me||(me={})).Uri="uri",ve.Text="text",ve.Image="image",ve.RowID="row-id",ve.Number="number",ve.Bubble="bubble",ve.Boolean="boolean",ve.Loading="loading",ve.Markdown="markdown",ve.Drilldown="drilldown",ve.Protected="protected",ve.Custom="custom",(ye=be||(be={})).HeaderRowID="headerRowID",ye.HeaderCode="headerCode",ye.HeaderNumber="headerNumber",ye.HeaderString="headerString",ye.HeaderBoolean="headerBoolean",ye.HeaderAudioUri="headerAudioUri",ye.HeaderVideoUri="headerVideoUri",ye.HeaderEmoji="headerEmoji",ye.HeaderImage="headerImage",ye.HeaderUri="headerUri",ye.HeaderPhone="headerPhone",ye.HeaderMarkdown="headerMarkdown",ye.HeaderDate="headerDate",ye.HeaderTime="headerTime",ye.HeaderEmail="headerEmail",ye.HeaderReference="headerReference",ye.HeaderIfThenElse="headerIfThenElse",ye.HeaderSingleValue="headerSingleValue",ye.HeaderLookup="headerLookup",ye.HeaderTextTemplate="headerTextTemplate",ye.HeaderMath="headerMath",ye.HeaderRollup="headerRollup",ye.HeaderJoinStrings="headerJoinStrings",ye.HeaderSplitString="headerSplitString",ye.HeaderGeoDistance="headerGeoDistance",ye.HeaderArray="headerArray",ye.RowOwnerOverlay="rowOwnerOverlay",ye.ProtectedColumnOverlay="protectedColumnOverlay";var Me,Ee,Re,Te="___gdg_header_cell_",Ie=Te+"checked",Oe=Te+"unchecked",Pe=Te+"indeterminate";function De(e){return"width"in e&&"number"===typeof e.width}async function He(e){return"object"===typeof e?e:await e()}function Le(e){return e.kind!==me.Loading&&e.kind!==me.Bubble&&e.kind!==me.RowID&&e.kind!==me.Protected&&e.kind!==me.Drilldown}function ze(e){return e.kind===Me.Marker||e.kind===Me.NewRow}function Fe(e){return!(!Le(e)||e.kind===me.Image)&&(e.kind===me.Text||e.kind===me.Number||e.kind===me.Markdown||e.kind===me.Uri||e.kind===me.Custom||e.kind===me.Boolean?!0!==e.readonly:void ae(0,"A cell was passed with an invalid kind"))}function Ae(e){return d(e,"editor")}function _e(e){var t;return!(null!=(t=e.readonly)&&t)}(Ee=Me||(Me={})).NewRow="new-row",Ee.Marker="marker";var Ve=class{constructor(e){this.items=e,this.offset=e=>{if(0===e)return this;const t=this.items.map((t=>[t[0]+e,t[1]+e]));return new Ve(t)},this.add=e=>{const t="number"===typeof e?[e,e+1]:e,n=function(e){if(0===e.length)return[];const t=[...e],n=[];t.sort((function(e,t){return e[0]-t[0]})),n.push([...t[0]]);for(const r of t.slice(1)){const e=n[n.length-1];e[1]<r[0]?n.push([...r]):e[1]<r[1]&&(e[1]=r[1])}return n}([...this.items,t]);return new Ve(n)},this.remove=e=>{const t=[...this.items],n="number"===typeof e?e:e[0],r="number"===typeof e?e+1:e[1];for(const[o,i]of t.entries()){const[e,l]=i;if(e<=r&&n<=l){const i=[];e<n&&i.push([e,n]),r<l&&i.push([r,l]),t.splice(o,1,...i)}}return new Ve(t)},this.first=()=>{if(0!==this.items.length)return this.items[0][0]},this.last=()=>{if(0!==this.items.length)return this.items.slice(-1)[0][1]-1},this.hasIndex=e=>{for(let t=0;t<this.items.length;t++){const[n,r]=this.items[t];if(e>=n&&e<r)return!0}return!1},this.hasAll=e=>{for(let t=e[0];t<e[1];t++)if(!this.hasIndex(t))return!1;return!0},this.some=e=>{for(const t of this)if(e(t))return!0;return!1},this.equals=e=>{if(e===this)return!0;if(e.items.length!==this.items.length)return!1;for(let t=0;t<this.items.length;t++){const n=e.items[t],r=this.items[t];if(n[0]!==r[0]||n[1]!==r[1])return!1}return!0},this.toArray=()=>{const e=[];for(const[t,n]of this.items)for(let r=t;r<n;r++)e.push(r);return e}}get length(){let e=0;for(const[t,n]of this.items)e+=n-t;return e}*[Symbol.iterator](){for(const[e,t]of this.items)for(let n=e;n<t;n++)yield n}},Ne=Ve;Ne.empty=()=>null!=Re?Re:Re=new Ve([]),Ne.fromSingleSelection=e=>Ve.empty().add(e);var Be=(0,r.d)("div")({name:"DataGridOverlayEditorStyle",class:"d1t1th9s",vars:{"d1t1th9s-0":[e=>e.targetY,"px"],"d1t1th9s-1":[e=>e.targetX-1,"px"],"d1t1th9s-2":[e=>e.targetY-1,"px"],"d1t1th9s-3":[e=>e.targetWidth+2,"px"],"d1t1th9s-4":[e=>e.targetHeight+2,"px"],"d1t1th9s-5":[e=>e.targetY+10,"px"],"d1t1th9s-6":[e=>Math.max(0,(e.targetHeight-28)/2),"px"]}});function We(){const[e,t]=function(){const[e,t]=o.useState();return[null!=e?e:void 0,t]}(),[n,r]=o.useState(0),[i,l]=o.useState(!0);o.useLayoutEffect((()=>{if(void 0===e)return;if(!("IntersectionObserver"in window))return;const t=new IntersectionObserver((e=>{0!==e.length&&l(e[0].isIntersecting)}),{threshold:1});return t.observe(e),()=>t.disconnect()}),[e]),o.useEffect((()=>{if(i||void 0===e)return;let t;const n=()=>{const{right:o}=e.getBoundingClientRect();r((e=>Math.min(e+window.innerWidth-o-10,0))),t=requestAnimationFrame(n)};return t=requestAnimationFrame(n),()=>{void 0!==t&&cancelAnimationFrame(t)}}),[e,i]);return{ref:t,style:o.useMemo((()=>({transform:"translateX(".concat(n,"px)")})),[n])}}var je=e=>{const{target:t,content:n,onFinishEditing:r,forceEditMode:i,initialValue:l,imageEditorOverride:a,markdownDivCreateNode:s,highlight:c,className:d,theme:h,id:p,cell:f,validateCell:g,getCellRenderer:m,provideEditor:v,isOutsideClick:b}=e,[y,w]=o.useState(i?n:void 0),x=o.useRef(null!=y?y:n);x.current=null!=y?y:n;const[k,S]=o.useState((()=>void 0===g||!(Le(n)&&!1===(null==g?void 0:g(f,n,x.current))))),C=o.useCallback(((e,t)=>{r(k?e:void 0,t)}),[k,r]),M=o.useCallback((e=>{if(void 0!==g&&void 0!==e&&Le(e)){const t=g(f,e,x.current);!1===t?S(!1):"object"===typeof t?(e=t,S(!0)):S(!0)}w(e)}),[f,g]),E=o.useRef(!1),R=o.useRef(void 0),T=o.useCallback((()=>{C(y,[0,0]),E.current=!0}),[y,C]),I=o.useCallback(((e,t)=>{var n;C(e,null!=(n=null!=t?t:R.current)?n:[0,0]),E.current=!0}),[C]),O=o.useCallback((async e=>{let t=!1;"Escape"===e.key?(e.stopPropagation(),e.preventDefault(),R.current=[0,0]):"Enter"!==e.key||e.shiftKey?"Tab"===e.key&&(e.stopPropagation(),e.preventDefault(),R.current=[e.shiftKey?-1:1,0],t=!0):(e.stopPropagation(),e.preventDefault(),R.current=[0,1],t=!0),window.setTimeout((()=>{E.current||void 0===R.current||(C(t?y:void 0,R.current),E.current=!0)}),0)}),[C,y]),P=null!=y?y:n,[D,H]=o.useMemo((()=>{var e,t;if(ze(n))return[];const r=null==v?void 0:v(n);return void 0!==r?[r,!1]:[null==(t=null==(e=m(n))?void 0:e.provideEditor)?void 0:t.call(e,n),!1]}),[n,m,v]),{ref:L,style:z}=We();let F,A,_=!0,V=!0;if(void 0!==D){_=!0!==D.disablePadding,V=!0!==D.disableStyling;const e=Ae(D);e&&(A=D.styleOverride);const n=e?D.editor:D;F=o.createElement(n,{isHighlighted:c,onChange:M,value:P,initialValue:l,onFinishedEditing:I,validatedSelection:Le(P)?P.selectionRange:void 0,forceEditMode:i,target:t,imageEditorOverride:a,markdownDivCreateNode:s,isValid:k})}A={...A,...z};const N=document.getElementById("portal");if(null===N)return console.error('Cannot open Data Grid overlay editor, because portal not found.  Please add `<div id="portal" />` as the last child of your `<body>`.'),null;let B=V?"gdg-style":"gdg-unstyle";return k||(B+=" invalid"),_&&(B+=" pad"),(0,u.createPortal)(o.createElement(fe.Provider,{value:h},o.createElement(ue,{style:de(h),className:d,onClickOutside:T,isOutsideClick:b},o.createElement(Be,{ref:L,id:p,className:B,style:A,as:!0===H?"label":void 0,targetX:t.x,targetY:t.y,targetWidth:t.width,targetHeight:t.height},o.createElement("div",{className:"clip-region",onKeyDown:O},F)))),N)},Ze=65536,Ue=[];function Xe(e,t){return t*Ze+e}function Ye(e){return e%Ze}function Ke(e,t){return(e-t)/Ze}function $e(e){const t=Ye(e);return[t,Ke(e,t)]}var Ge=class{constructor(){this.imageLoaded=()=>{},this.loadedLocations=[],this.visibleWindow={x:0,y:0,width:0,height:0},this.freezeCols=0,this.isInWindow=e=>{const t=Ye(e),n=Ke(e,t),r=this.visibleWindow;return t<this.freezeCols&&n>=r.y&&n<=r.y+r.height||t>=r.x&&t<=r.x+r.width&&n>=r.y&&n<=r.y+r.height},this.cache={},this.sendLoaded=h((()=>{this.imageLoaded(this.loadedLocations),this.loadedLocations=[]}),20),this.clearOutOfWindow=()=>{const e=Object.keys(this.cache);for(const t of e){const e=this.cache[t];let n=!1;for(let t=0;t<e.cells.length;t++){const r=e.cells[t];if(this.isInWindow(r)){n=!0;break}}n?e.cells=e.cells.filter(this.isInWindow):(e.cancel(),delete this.cache[t])}}}setCallback(e){this.imageLoaded=e}setWindow(e,t){this.visibleWindow.x===e.x&&this.visibleWindow.y===e.y&&this.visibleWindow.width===e.width&&this.visibleWindow.height===e.height&&this.freezeCols===t||(this.visibleWindow=e,this.freezeCols=t,this.clearOutOfWindow())}loadImage(e,t,n,r){var o;let i=!1;const l=null!=(o=Ue.pop())?o:new Image;let a=!1;const s={img:void 0,cells:[Xe(t,n)],url:e,cancel:()=>{a||(a=!0,Ue.length<12?Ue.unshift(l):i||(l.src=""))}},c=new Promise((e=>l.addEventListener("load",(()=>e(null)))));requestAnimationFrame((async()=>{try{l.src=e,await c,await l.decode();const t=this.cache[r];if(void 0!==t&&!a){t.img=l;for(const e of t.cells)this.loadedLocations.push($e(e));i=!0,this.sendLoaded()}}catch(t){s.cancel()}})),this.cache[r]=s}loadOrGetImage(e,t,n){const r=e,o=this.cache[r];if(void 0!==o){const e=Xe(t,n);return o.cells.includes(e)||o.cells.push(e),o.img}this.loadImage(e,t,n,r)}};function qe(e,t,n,r){let i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];const l=o.useRef();l.current=t,o.useEffect((()=>{if(null===n||void 0===n.addEventListener)return;const t=n,o=e=>{var n;null==(n=l.current)||n.call(t,e)};return t.addEventListener(e,o,{passive:r,capture:i}),()=>{t.removeEventListener(e,o,{capture:i})}}),[e,n,r,i])}function Qe(e,t){return void 0===e?void 0:t}var Je=Math.PI;function et(e){return e*Je/180}var tt=(e,t,n)=>({x1:e-n/2,y1:t-n/2,x2:e+n/2,y2:t+n/2}),nt=(e,t,n,r,o)=>{switch(e){case"left":return Math.floor(t)+r+o/2;case"center":return Math.floor(t+n/2);case"right":return Math.floor(t+n)-r-o/2}},rt=(e,t,n)=>Math.min(e,t-2*n),ot=(e,t,n)=>n.x1<=e&&e<=n.x2&&n.y1<=t&&t<=n.y2,it=e=>{var t;const n=null!=(t=e.fgColor)?t:"currentColor";return o.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o.createElement("path",{d:"M12.7073 7.05029C7.87391 11.8837 10.4544 9.30322 6.03024 13.7273C5.77392 13.9836 5.58981 14.3071 5.50189 14.6587L4.52521 18.5655C4.38789 19.1148 4.88543 19.6123 5.43472 19.475L9.34146 18.4983C9.69313 18.4104 10.0143 18.2286 10.2706 17.9722L16.9499 11.2929",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}),o.createElement("path",{d:"M20.4854 4.92901L19.0712 3.5148C18.2901 2.73375 17.0238 2.73375 16.2428 3.5148L14.475 5.28257C15.5326 7.71912 16.4736 8.6278 18.7176 9.52521L20.4854 7.75744C21.2665 6.97639 21.2665 5.71006 20.4854 4.92901Z",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}))},lt=e=>{var t;const n=null!=(t=e.fgColor)?t:"currentColor";return o.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o.createElement("path",{d:"M19 6L10.3802 17L5.34071 11.8758",vectorEffect:"non-scaling-stroke",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))};var at="\u0591-\u07ff\ufb1d-\ufdfd\ufe70-\ufefc",st="A-Za-z\xc0-\xd6\xd8-\xf6\xf8-\u02b8\u0300-\u0590\u0800-\u1fff\u200e\u2c00-\ufb1c\ufe00-\ufe6f\ufefd-\uffff",ct=new RegExp("^[^"+st+"]*["+at+"]"),ut=new RegExp("^[^"+at+"]*["+st+"]");function dt(e){return ct.test(e)?"rtl":ut.test(e)?"ltr":"neutral"}var ht=void 0;var pt=Symbol();function ft(e,t){return(null!=e?e:"")===(null!=t?t:"")}function gt(e,t,n){const r=n.x,o=n.x+n.width-1,i=n.y,l=n.y+n.height-1,[a,s]=e;if(s<i||s>l)return!1;if(void 0===t.span)return a>=r&&a<=o;const[c,u]=t.span;return c>=r&&c<=o||u>=r&&c<=o||c<r&&u>o}function mt(e,t){let n=e;if(void 0!==t){let r=[...e];const o=n[t.src];t.src>t.dest?(r.splice(t.src,1),r.splice(t.dest,0,o)):(r.splice(t.dest+1,0,o),r.splice(t.src,1)),r=r.map(((t,n)=>({...t,sticky:e[n].sticky}))),n=r}return n}function vt(e,t){let n=0;const r=mt(e,t);for(let o=0;o<r.length;o++){const e=r[o];if(!e.sticky)break;n+=e.width}return n}function bt(e,t,n,r,o){const i=mt(e,r),l=[];for(const c of i){if(!c.sticky)break;l.push(c)}if(l.length>0)for(const c of l)n-=c.width;let a=t,s=null!=o?o:0;for(;s<=n&&a<i.length;)s+=i[a].width,a++;for(let c=t;c<a;c++){const e=i[c];e.sticky||l.push(e)}return l}var yt=0,wt={},xt="undefined"===typeof window;function kt(e,t,n){const r=function(e,t,n,r){return"".concat(e,"_").concat(null!=r?r:t.font,"_").concat(n)}(e,t,"middle",n);let o=wt[r];return void 0===o&&(o=t.measureText(e),wt[r]=o,yt++),yt>1e4&&(wt={},yt=0),o}function St(e,t){return"string"!==typeof t&&(t="".concat(t.baseFontStyle," ").concat(t.fontFamily)),function(e,t){for(const i of Mt)if(i.key===t)return i.val;const n=Ct(e,"alphabetic"),r=Ct(e,"middle"),o=-(r.actualBoundingBoxDescent-n.actualBoundingBoxDescent)+n.actualBoundingBoxAscent/2;return Mt.push({key:t,val:o}),o}(e,t)}function Ct(e,t){e.save(),e.textBaseline=t;const n=e.measureText("ABCDEFGHIJKLMNOPQRSTUVWXYZ");return e.restore(),n}!async function(){var e;xt||void 0===(null==(e=null==document?void 0:document.fonts)?void 0:e.ready)||(await document.fonts.ready,yt=0,wt={},p.clear(),g.clear(),f.clear())}();var Mt=[];function Et(e,t,n){const{ctx:r,theme:o}=e,i=null!=t?t:{},l=null!=n?n:o.textDark;return l!==i.fillStyle&&(r.fillStyle=l,i.fillStyle=l),i}function Rt(e,t,n){const{rect:r,ctx:o,theme:i}=e;o.fillStyle=i.textDark,Ot({ctx:o,rect:r,theme:i},t,n)}function Tt(e,t,n,r,o,i,l,a,s){"right"===s?e.fillText(t,n+o-(a.cellHorizontalPadding+.5),r+i/2+l):"center"===s?e.fillText(t,n+o/2,r+i/2+l):e.fillText(t,n+a.cellHorizontalPadding+.5,r+i/2+l)}function It(e,t){const n=kt("ABCi09jgqpy",e,t);return n.actualBoundingBoxAscent+n.actualBoundingBoxDescent}function Ot(e,t,n,r,o){const{ctx:i,rect:l,theme:a}=e,{x:s,y:c,width:u,height:d}=l;if(!(r=null!=r&&r)){t.includes("\n")&&(t=t.split(/\r?\n/)[0]);const e=u/4;t.length>e&&(t=t.slice(0,e))}const h=St(i,a),g="rtl"===dt(t);if(void 0===n&&g&&(n="right"),g&&(i.direction="rtl"),t.length>0){let e=!1;if("right"===n?(i.textAlign="right",e=!0):void 0!==n&&"left"!==n&&(i.textAlign=n,e=!0),r){const e="".concat(a.fontFamily," ").concat(a.baseFontStyle),r=function(e,t,n,r,o,i){let l="".concat(t,"_").concat(n,"_").concat(r,"px"),a=p.get(l);if(void 0!==a)return a;if(r<=0)return[];let s=[],c=t.split("\n"),u=f.get(n),d=void 0===u?t.length:r/u.size*1.5,h=o&&void 0!==u&&u.count>2e4;for(let p of c){let t=m(e,p.slice(0,Math.max(0,d)),n,h),o=Math.min(p.length,d);if(t<=r)s.push(p);else{for(;t>r;){let l=v(e,p,r,n,t,o,h,i),a=p.slice(0,Math.max(0,l));p=p.slice(a.length),s.push(a),t=m(e,p.slice(0,Math.max(0,d)),n,h),o=Math.min(p.length,d)}t>0&&s.push(p)}}return s=s.map(((e,t)=>0===t?e.trimEnd():e.trim())),p.set(l,s),p.size>500&&p.delete(p.keys().next().value),s}(i,t,e,u-2*a.cellHorizontalPadding,null!=o&&o),l=It(i,e),g=a.lineHeight*l,b=l+g*(r.length-1),y=b+a.cellVerticalPadding>d;y&&(i.save(),i.rect(s,c,u,d),i.clip());const w=c+d/2-b/2;let x=Math.max(c+a.cellVerticalPadding,w);for(const t of r)if(Tt(i,t,s,x,u,l,h,a,n),x+=g,x>c+d)break;y&&i.restore()}else Tt(i,t,s,c,u,d,h,a,n);e&&(i.textAlign="start"),g&&(i.direction="inherit")}}function Pt(e,t,n,r,o,i,l,a){let s=arguments.length>8&&void 0!==arguments[8]?arguments[8]:-20,c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:-20,u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:32,d=arguments.length>11&&void 0!==arguments[11]?arguments[11]:"center";const h=Math.floor(o+l/2),p=rt(u,l,t.cellVerticalPadding),f=p/2,g=nt(d,r,i,t.cellHorizontalPadding,p),m=tt(g,h,p),v=ot(r+s,o+c,m);switch(n){case!0:e.beginPath(),Ht(e,g-p/2,h-p/2,p,p,4),e.fillStyle=a?t.accentColor:t.textMedium,e.fill(),e.beginPath(),e.moveTo(g-f+p/4.23,h-f+p/1.97),e.lineTo(g-f+p/2.42,h-f+p/1.44),e.lineTo(g-f+p/1.29,h-f+p/3.25),e.strokeStyle=t.bgCell,e.lineJoin="round",e.lineCap="round",e.lineWidth=1.9,e.stroke();break;case we:case!1:e.beginPath(),Ht(e,g-p/2+.5,h-p/2+.5,p-1,p-1,4),e.lineWidth=1,e.strokeStyle=v?t.textDark:t.textMedium,e.stroke();break;case xe:e.beginPath(),Ht(e,g-p/2,h-p/2,p,p,4),e.fillStyle=v?t.textMedium:t.textLight,e.fill(),e.beginPath(),e.moveTo(g-p/3,h),e.lineTo(g+p/3,h),e.strokeStyle=t.bgCell,e.lineCap="round",e.lineWidth=1.9,e.stroke();break;default:ae()}}function Dt(e){const{ctx:t}=e;t.textAlign="start"}function Ht(e,t,n,r,o,i){"number"===typeof i&&(i={tl:i,tr:i,br:i,bl:i}),i={tl:Math.min(i.tl,o/2,r/2),tr:Math.min(i.tr,o/2,r/2),bl:Math.min(i.bl,o/2,r/2),br:Math.min(i.br,o/2,r/2)},e.moveTo(t+i.tl,n),e.arcTo(t+r,n,t+r,n+i.tr,i.tr),e.arcTo(t+r,n+o,t+r-i.br,n+o,i.br),e.arcTo(t,n+o,t,n+o-i.bl,i.bl),e.arcTo(t,n,t+i.tl,n,i.tl)}var Lt={};function zt(e,t){const{rect:n,theme:r,ctx:o,imageLoader:i,col:l,row:a}=e,{x:s,width:c}=n,u="".concat(r.baseFontStyle," ").concat(r.fontFamily),d=It(o,u),h=Math.min(n.height,Math.max(16,2*Math.ceil(d*r.lineHeight))),p=Math.floor(n.y+(n.height-h)/2),f=h-10;let g=s+r.cellHorizontalPadding;const m=function(e,t,n){const r=Math.ceil(window.devicePixelRatio),o=n-10,i=n*r,l=28*r,a="".concat(e,",").concat(t,",").concat(r,",").concat(n);if(void 0!==Lt[a])return{el:Lt[a],height:i,width:l,middleWidth:4*r,sideWidth:11*r,padding:5*r,dpr:r};const s=document.createElement("canvas"),c=s.getContext("2d");if(null===c)return null;s.width=l,s.height=i,c.scale(r,r),Lt[a]=s;const u=Math.min(6,9,o/2);return c.beginPath(),Ht(c,5,5,18,o,u),c.shadowColor="rgba(24, 25, 34, 0.4)",c.shadowBlur=1,c.fillStyle=e,c.fill(),c.shadowColor="rgba(24, 25, 34, 0.3)",c.shadowOffsetY=1,c.shadowBlur=5,c.fillStyle=e,c.fill(),c.shadowOffsetY=0,c.shadowBlur=0,c.shadowBlur=0,c.beginPath(),Ht(c,5.5,5.5,18,o,u),c.strokeStyle=t,c.lineWidth=1,c.stroke(),{el:s,height:i,width:l,sideWidth:11*r,middleWidth:6*r,padding:5*r,dpr:r}}(r.bgCell,r.drilldownBorder,h),v=[];for(const b of t){if(g>s+c)break;const e=kt(b.text,o,u).width;let t=0;if(void 0!==b.img){void 0!==i.loadOrGetImage(b.img,l,a)&&(t=f-8+4)}const n=e+t+16;v.push({x:g,width:n}),g+=n+4}if(null!==m){const{el:e,height:t,middleWidth:n,sideWidth:r,width:i,dpr:l,padding:a}=m,s=r/l,c=a/l;for(const u of v){const l=Math.floor(u.x),a=Math.floor(u.width),d=a-2*(s-c);o.imageSmoothingEnabled=!1,o.drawImage(e,0,0,r,t,l-c,p,s,h),d>0&&o.drawImage(e,r,0,n,t,l+(s-c),p,d,h),o.drawImage(e,i-r,0,r,t,l+a-(s-c),p,s,h),o.imageSmoothingEnabled=!0}}o.beginPath();for(const[b,y]of v.entries()){const e=t[b];let n=y.x+8;if(void 0!==e.img){const t=i.loadOrGetImage(e.img,l,a);if(void 0!==t){const e=f-8;let r=0,i=0,l=t.width,a=t.height;l>a?(r+=(l-a)/2,l=a):a>l&&(i+=(a-l)/2,a=l),o.beginPath(),Ht(o,n,p+h/2-e/2,e,e,3),o.save(),o.clip(),o.drawImage(t,r,i,l,a,n,p+h/2-e/2,e,e),o.restore(),n+=e+4}}o.beginPath(),o.fillStyle=r.textBubble,o.fillText(e.text,n,p+h/2+St(o,r))}}function Ft(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f){const g={x:0,y:i+c,width:0,height:0},m=i-o;if(e>=d){const t=l>e?-1:1,n=vt(p);g.x+=n+s;for(let r=l;r!==e;r+=t)g.x+=p[1===t?r:r-1].width*t}else for(let v=0;v<e;v++)g.x+=p[v].width;if(g.width=p[e].width+1,-1===t)g.y=o,g.height=m;else if(-2===t){g.y=0,g.height=o;let t=e;const r=p[e].group,i=p[e].sticky;for(;t>0&&ft(p[t-1].group,r)&&p[t-1].sticky===i;){const e=p[t-1];g.x-=e.width,g.width+=e.width,t--}let l=e;for(;l+1<p.length&&ft(p[l+1].group,r)&&p[l+1].sticky===i;){const e=p[l+1];g.width+=e.width,l++}if(!i){const e=vt(p),t=g.x-e;t<0&&(g.x-=t,g.width+=t),g.x+g.width>n&&(g.width=n-g.x)}}else if(h&&t===u-1){const e="number"===typeof f?f:f(t);g.y=r-e,g.height=e}else{const e=a>t?-1:1;if("number"===typeof f){const e=t-a;g.y+=e*f}else for(let n=a;n!==t;n+=e)g.y+=f(n)*e;g.height=("number"===typeof f?f:f(t))+1}return g}var At='<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">',_t=e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n<path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n<path fill-rule="evenodd" clip-rule="evenodd" d="M10.29 4.947a3.368 3.368 0 014.723.04 3.375 3.375 0 01.041 4.729l-.009.009-1.596 1.597a3.367 3.367 0 01-5.081-.364.71.71 0 011.136-.85 1.95 1.95 0 002.942.21l1.591-1.593a1.954 1.954 0 00-.027-2.733 1.95 1.95 0 00-2.732-.027l-.91.907a.709.709 0 11-1.001-1.007l.915-.911.007-.007z" fill="').concat(t,'"/>\n<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55 8.678a3.368 3.368 0 015.082.364.71.71 0 01-1.136.85 1.95 1.95 0 00-2.942-.21l-1.591 1.593a1.954 1.954 0 00.027 2.733 1.95 1.95 0 002.73.028l.906-.906a.709.709 0 111.003 1.004l-.91.91-.008.01a3.368 3.368 0 01-4.724-.042 3.375 3.375 0 01-.041-4.728l.009-.009L6.55 8.678z" fill="').concat(t,'"/>\n</svg>\n  ')},Vt={headerRowID:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'<rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/><path d="M15.75 4h-1.5a.25.25 0 0 0-.177.074L9.308 8.838a3.75 3.75 0 1 0 1.854 1.854l1.155-1.157.967.322a.5.5 0 0 0 .65-.55l-.18-1.208.363-.363.727.331a.5.5 0 0 0 .69-.59l-.254-.904.647-.647A.25.25 0 0 0 16 5.75v-1.5a.25.25 0 0 0-.25-.25zM7.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0z" fill="').concat(t,'"/></svg>')},headerNumber:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M6.52 12.78H5.51V8.74l-1.33.47v-.87l2.29-.83h.05v5.27zm5.2 0H8.15v-.69l1.7-1.83a6.38 6.38 0 0 0 .34-.4c.09-.11.16-.22.22-.32s.1-.19.12-.27a.9.9 0 0 0 0-.56.63.63 0 0 0-.15-.23.58.58 0 0 0-.22-.15.75.75 0 0 0-.29-.05c-.27 0-.48.08-.62.23a.95.95 0 0 0-.2.65H8.03c0-.24.04-.46.13-.67a1.67 1.67 0 0 1 .97-.91c.23-.1.49-.14.77-.14.26 0 .5.04.7.11.21.08.38.18.52.32.14.13.25.3.32.48a1.74 1.74 0 0 1 .03 1.13 2.05 2.05 0 0 1-.24.47 4.16 4.16 0 0 1-.35.47l-.47.5-1 1.05h2.32v.8zm1.8-3.08h.55c.28 0 .48-.06.61-.2a.76.76 0 0 0 .2-.55.8.8 0 0 0-.05-.28.56.56 0 0 0-.13-.22.6.6 0 0 0-.23-.15.93.93 0 0 0-.32-.05.92.92 0 0 0-.29.05.72.72 0 0 0-.23.12.57.57 0 0 0-.21.46H12.4a1.3 1.3 0 0 1 .5-1.04c.15-.13.33-.23.54-.3a2.48 2.48 0 0 1 1.4 0c.2.06.4.15.55.28.15.13.27.28.36.47.08.19.13.4.13.65a1.15 1.15 0 0 1-.2.65 1.36 1.36 0 0 1-.58.49c.15.05.28.12.38.2a1.14 1.14 0 0 1 .43.62c.03.13.05.26.05.4 0 .25-.05.47-.14.66a1.42 1.42 0 0 1-.4.49c-.16.13-.35.23-.58.3a2.51 2.51 0 0 1-.73.1c-.22 0-.44-.03-.65-.09a1.8 1.8 0 0 1-.57-.28 1.43 1.43 0 0 1-.4-.47 1.41 1.41 0 0 1-.15-.66h1a.66.66 0 0 0 .22.5.87.87 0 0 0 .58.2c.25 0 .45-.07.6-.2a.71.71 0 0 0 .21-.56.97.97 0 0 0-.06-.36.61.61 0 0 0-.18-.25.74.74 0 0 0-.28-.15 1.33 1.33 0 0 0-.37-.04h-.55V9.7z" fill="').concat(t,'"/>\n  </svg>')},headerCode:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'<rect x="2" y="2" width="16" height="16" rx="4" fill="').concat(n,'"/><path d="m12.223 13.314 3.052-2.826a.65.65 0 0 0 0-.984l-3.052-2.822c-.27-.25-.634-.242-.865.022-.232.263-.206.636.056.882l2.601 2.41-2.601 2.41c-.262.245-.288.619-.056.882.231.263.595.277.865.026Zm-4.444.005c.266.25.634.241.866-.027.231-.263.206-.636-.06-.882L5.983 10l2.602-2.405c.266-.25.291-.62.06-.887-.232-.263-.596-.272-.866-.022L4.723 9.51a.653.653 0 0 0 0 .983l3.056 2.827Z" fill="').concat(t,'"/></svg>')},headerString:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M8.182 12.4h3.636l.655 1.6H14l-3.454-8H9.455L6 14h1.527l.655-1.6zM10 7.44l1.36 3.651H8.64L10 7.441z" fill="').concat(t,'"/>\n</svg>')},headerBoolean:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n    <path\n        d="M16.2222 2H3.77778C2.8 2 2 2.8 2 3.77778V16.2222C2 17.2 2.8 18 3.77778 18H16.2222C17.2 18 17.9911 17.2 17.9911 16.2222L18 3.77778C18 2.8 17.2 2 16.2222 2Z"\n        fill="').concat(n,'"\n    />\n    <path\n        fill-rule="evenodd"\n        clip-rule="evenodd"\n        d="M7.66667 6.66669C5.73368 6.66669 4.16667 8.15907 4.16667 10C4.16667 11.841 5.73368 13.3334 7.66667 13.3334H12.3333C14.2663 13.3334 15.8333 11.841 15.8333 10C15.8333 8.15907 14.2663 6.66669 12.3333 6.66669H7.66667ZM12.5 12.5C13.8807 12.5 15 11.3807 15 10C15 8.61931 13.8807 7.50002 12.5 7.50002C11.1193 7.50002 10 8.61931 10 10C10 11.3807 11.1193 12.5 12.5 12.5Z"\n        fill="').concat(t,'"\n    />\n</svg>')},headerAudioUri:_t,headerVideoUri:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.138a.5.5 0 00.748.434l5.492-3.138a.5.5 0 000-.868L7.748 6.427A.5.5 0 007 6.862v6.276z" fill="').concat(t,'"/>\n</svg>')},headerEmoji:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <path d="M10 5a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 9.17A4.17 4.17 0 0 1 5.83 10 4.17 4.17 0 0 1 10 5.83 4.17 4.17 0 0 1 14.17 10 4.17 4.17 0 0 1 10 14.17z" fill="').concat(t,'"/>\n    <path d="M8.33 8.21a.83.83 0 1 0-.03 ********** 0 0 0 .03-1.67zm3.34 0a.83.83 0 1 0-.04 ********** 0 0 0 .04-1.67z" fill="').concat(t,'"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.53 13.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="').concat(t,'"/>\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm0 11a5 5 0 1 1 .01-10.01A5 5 0 0 1 10 15z" fill="').concat(t,'"/>\n    <path d="M8 7.86a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2zm4 0a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2z" fill="').concat(t,'"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.53 11.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="').concat(t,'"/>\n  </svg>')},headerImage:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path opacity=".5" fill-rule="evenodd" clip-rule="evenodd" d="M12.499 10.801a.5.5 0 01.835 0l2.698 4.098a.5.5 0 01-.418.775H10.22a.5.5 0 01-.417-.775l2.697-4.098z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.07 8.934a.5.5 0 01.824 0l4.08 5.958a.5.5 0 01-.412.782h-8.16a.5.5 0 01-.413-.782l4.08-5.958zM13.75 8.333a2.083 2.083 0 100-4.166 2.083 2.083 0 000 4.166z" fill="').concat(t,'"/>\n</svg>')},headerUri:_t,headerPhone:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <path fill="').concat(t,'" d="M3 3h14v14H3z"/>\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2zm-7.24 9.78h1.23c.15 0 .27.06.36.18l.98 1.28a.43.43 0 0 1-.05.58l-1.2 1.21a.45.45 0 0 1-.6.04A6.72 6.72 0 0 1 7.33 10c0-.61.1-1.2.25-1.78a6.68 6.68 0 0 1 2.12-********* 0 0 1 .6.04l1.2 1.2c.***********.05.59l-.98 1.29a.43.43 0 0 1-.36.17H8.98A5.38 5.38 0 0 0 8.67 10c0 .62.11 1.23.3 1.79z" fill="').concat(n,'"/>\n  </svg>')},headerMarkdown:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="m13.49 13.15-2.32-3.27h1.4V7h1.86v2.88h1.4l-2.34 3.27zM11 13H9v-3l-1.5 1.92L6 10v3H4V7h2l1.5 2L9 7h2v6z" fill="').concat(t,'"/>\n  </svg>')},headerDate:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M14.8 4.182h-.6V3H13v1.182H7V3H5.8v1.182h-.6c-.66 0-1.2.532-1.2 1.182v9.454C4 15.468 4.54 16 5.2 16h9.6c.66 0 1.2-.532 1.2-1.182V5.364c0-.65-.54-1.182-1.2-1.182zm0 10.636H5.2V7.136h9.6v7.682z" fill="').concat(t,'"/>\n</svg>')},headerTime:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 4a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6zm0 10.8A4.8 4.8 0 0 1 5.2 10a4.8 4.8 0 1 1 4.8 4.8z" fill="').concat(t,'"/>\n    <path d="M10 7H9v3.93L12.5 13l.5-.8-3-1.76V7z" fill="').concat(t,'"/>\n  </svg>')},headerEmail:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 8.643a1.357 1.357 0 100 2.714 1.357 1.357 0 000-2.714zM7.357 10a2.643 2.643 0 115.286 0 2.643 2.643 0 01-5.286 0z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.589 4.898A5.643 5.643 0 0115.643 10v.5a2.143 2.143 0 01-4.286 0V8a.643.643 0 011.286 0v2.5a.857.857 0 001.714 0V10a4.357 4.357 0 10-1.708 3.46.643.643 0 01.782 1.02 5.643 5.643 0 11-5.842-9.582z" fill="').concat(t,'"/>\n</svg>')},headerReference:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <rect x="2" y="8" width="10" height="8" rx="2" fill="').concat(n,'"/>\n    <rect x="8" y="4" width="10" height="8" rx="2" fill="').concat(n,'"/>\n    <path d="M10.68 7.73V6l2.97 3.02-2.97 3.02v-1.77c-2.13 0-3.62.7-4.68 2.2.43-2.15 1.7-4.31 4.68-4.74z" fill="').concat(t,'"/>\n  </svg>')},headerIfThenElse:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <path fill="').concat(t,'" d="M4 3h12v14H4z"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.6 2A1.6 1.6 0 002 3.6v12.8A1.6 1.6 0 003.6 18h12.8a1.6 1.6 0 001.6-1.6V3.6A1.6 1.6 0 0016.4 2H3.6zm11.3 10.8a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7h-1.4a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.6-.693.117.117 0 00.1-.115V10.35a.117.117 0 00-.117-.116h-2.8a.117.117 0 00-.117.116v2.333c0 .***************.117h.117a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7H9.3a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.117a.117.117 0 00.117-.117V10.35a.117.117 0 00-.117-.117h-2.8a.117.117 0 00-.117.117v2.342c0 .*************.115a.7.7 0 01.6.693v1.4a.7.7 0 01-.7.7H5.1a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.35a.116.116 0 00.116-.117v-2.45c0-.515.418-.933.934-.933h2.917a.117.117 0 00.117-.117V6.85a.117.117 0 00-.117-.116h-2.45a.7.7 0 01-.7-.7V5.1a.7.7 0 01.7-.7h6.067a.7.7 0 01.7.7v.934a.7.7 0 01-.7.7h-2.45a.117.117 0 00-.118.116v2.333c0 .***************.117H13.5c.516 0 .934.418.934.934v2.45c0 .***************.116h.35z" fill="').concat(n,'"/>\n</svg>')},headerSingleValue:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M9.98 13.33c.45 0 .74-.3.73-.75l-.01-.1-.16-1.67 1.45 1.05a.81.81 0 0 0 .5.18c.37 0 .72-.32.72-.76 0-.3-.17-.54-.49-.68l-1.63-.77 1.63-.77c.32-.14.49-.37.49-.67 0-.45-.34-.76-.71-.76a.81.81 0 0 0-.5.18l-1.47 1.03.16-1.74.01-.08c.01-.46-.27-.76-.72-.76-.46 0-.76.32-.75.76l.01.08.16 1.74-1.47-1.03a.77.77 0 0 0-.5-.18.74.74 0 0 0-.72.76c0 .3.17.53.49.67l1.63.77-1.62.77c-.32.14-.5.37-.5.68 0 .44.35.75.72.75a.78.78 0 0 0 .5-.17L9.4 10.8l-.16 1.68v.09c-.02.44.28.75.74.75z" fill="').concat(t,'"/>\n  </svg>')},headerLookup:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M8 5.83H5.83a.83.83 0 0 0 0 1.67h1.69A4.55 4.55 0 0 1 8 5.83zm-.33 3.34H5.83a.83.83 0 0 0 0 1.66h2.72a4.57 4.57 0 0 1-.88-1.66zM5.83 12.5a.83.83 0 0 0 0 1.67h7.5a.83.83 0 1 0 0-1.67h-7.5zm8.8-2.9a3.02 3.02 0 0 0 .46-1.6c0-1.66-1.32-3-2.94-3C10.52 5 9.2 6.34 9.2 8s1.31 3 2.93 3c.58 0 1.11-.17 1.56-.47l2.04 2.08.93-.94-2.04-2.08zm-2.48.07c-.9 0-1.63-.75-1.63-1.67s.73-1.67 1.63-1.67c.9 0 1.63.75 1.63 1.67s-.73 1.67-1.63 1.67z" fill="').concat(t,'"/>\n  </svg>')},headerTextTemplate:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path d="M7.676 4.726V3l2.976 3.021-2.976 3.022v-1.77c-2.125 0-3.613.69-4.676 2.201.425-2.158 1.7-4.316 4.676-4.748zM10.182 14.4h3.636l.655 1.6H16l-3.454-8h-1.091L8 16h1.527l.655-1.6zM12 9.44l1.36 3.65h-2.72L12 9.44z" fill="').concat(t,'"/>\n</svg>')},headerMath:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.167 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666H4.167z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.083 4.167a.833.833 0 10-1.666 0v4.166a.833.833 0 101.666 0V4.167zM11.667 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666h-4.166zM5.367 11.688a.833.833 0 00-1.179 1.179l2.947 2.946a.833.833 0 001.178-1.178l-2.946-2.947z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.313 12.867a.833.833 0 10-1.178-1.179l-2.947 2.947a.833.833 0 101.179 1.178l2.946-2.946z" fill="').concat(t,'"/>\n  <path d="M10.833 12.5c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833zM10.833 15c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833z" fill="').concat(t,'"/>\n</svg>')},headerRollup:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 8.84a1.16 1.16 0 1 0 0 2.32 1.16 1.16 0 0 0 0-2.32zm3.02 3.61a3.92 3.92 0 0 0 .78-********** 0 1 0-.95.2c.19.87-.02 1.78-.58 2.47a2.92 2.92 0 1 1-4.13-4.08 2.94 2.94 0 0 1 2.43-.62.49.49 0 1 0 .17-.96 3.89 3.89 0 1 0 2.28 6.27zM10 4.17a5.84 5.84 0 0 0-5.44 ********** 0 1 0 .9-.35 4.86 4.86 0 1 1 2.5 ********** 0 1 0-.4.88c.76.35 1.6.54 2.44.53a5.83 5.83 0 0 0 0-11.66zm3.02 3.5a.7.7 0 1 0-1.4 0 .7.7 0 0 0 1.4 0zm-6.97 5.35a.7.7 0 1 1 0 1.4.7.7 0 0 1 0-1.4z" fill="').concat(t,'"/>\n  </svg>')},headerJoinStrings:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path d="M12.4 13.565c1.865-.545 3.645-2.083 3.645-4.396 0-1.514-.787-2.604-2.071-2.604C12.69 6.565 12 7.63 12 8.939c1.114.072 1.865.726 1.865 1.683 0 .933-.8 1.647-1.84 2.023l.375.92zM4 5h6v2H4zM4 9h5v2H4zM4 13h4v2H4z" fill="').concat(t,'"/>\n</svg>')},headerSplitString:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(At,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M12.4 13.56c1.86-.54 3.65-2.08 3.65-4.4 0-1.5-.8-2.6-2.08-2.6S12 7.64 12 8.95c1.11.07 1.86.73 1.86 1.68 0 .94-.8 1.65-1.83 2.03l.37.91zM4 5h6v2H4zm0 4h5v2H4zm0 4h4v2H4z" fill="').concat(t,'"/>\n  </svg>')},headerGeoDistance:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M10 7a1 1 0 100-2v2zm0 6a1 1 0 100 2v-2zm0-8H7v2h3V5zm-3 6h5V9H7v2zm5 2h-2v2h2v-2zm1-1a1 1 0 01-1 1v2a3 3 0 003-3h-2zm-1-1a1 1 0 011 1h2a3 3 0 00-3-3v2zM4 8a3 3 0 003 3V9a1 1 0 01-1-1H4zm3-3a3 3 0 00-3 3h2a1 1 0 011-1V5z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.856 12.014a.5.5 0 00-.712.702L5.409 14l-1.265 1.284a.5.5 0 00.712.702l1.255-1.274 1.255 1.274a.5.5 0 00.712-.702L6.813 14l1.265-1.284a.5.5 0 00-.712-.702L6.11 13.288l-1.255-1.274zM12.856 4.014a.5.5 0 00-.712.702L13.409 6l-1.265 1.284a.5.5 0 10.712.702l1.255-1.274 1.255 1.274a.5.5 0 10.712-.702L14.813 6l1.265-1.284a.5.5 0 00-.712-.702L14.11 5.288l-1.255-1.274z" fill="').concat(t,'"/>\n</svg>')},headerArray:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(At,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 7.25a.75.75 0 000-1.5h-6.5a.75.75 0 100 1.5h6.5zM15 10a.75.75 0 01-.75.75h-6.5a.75.75 0 010-1.5h6.5A.75.75 0 0115 10zm-.75 4.25a.75.75 0 000-1.5h-6.5a.75.75 0 000 1.5h6.5zm-8.987-7a.75.75 0 100-********* 0 000 1.5zm.75 2.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 4.25a.75.75 0 100-********* 0 000 1.5z" fill="').concat(t,'"/>\n</svg>')},rowOwnerOverlay:e=>{const t=e.fgColor,n=e.bgColor;return'\n    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M2 15v1h14v-2.5c0-.87-.44-1.55-.98-2.04a6.19 6.19 0 0 0-1.9-1.14 12.1 12.1 0 0 0-2.48-.67A4 4 0 1 0 5 6a4 4 0 0 0 2.36 3.65c-.82.13-1.7.36-2.48.67-.69.28-1.37.65-1.9 1.13A2.8 2.8 0 0 0 2 13.5V15z" fill="'.concat(n,'" stroke="').concat(t,'" stroke-width="2"/>\n  </svg>')},protectedColumnOverlay:e=>{const t=e.fgColor,n=e.bgColor;return'\n    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M12.43 6.04v-.18a3.86 3.86 0 0 0-7.72 0v.18A2.15 2.15 0 0 0 3 8.14v5.72C3 15.04 3.96 16 5.14 16H12c1.18 0 2.14-.96 2.14-2.14V8.14c0-1.03-.73-1.9-1.71-2.1zM7.86 6v-.14a.71.71 0 1 1 1.43 0V6H7.86z" fill="'.concat(n,'" stroke="').concat(t,'" stroke-width="2"/>\n  </svg>\n')},renameIcon:e=>{const t=e.bgColor;return"".concat(At,'\n    <path stroke="').concat(t,'" stroke-width="2" d="M12 3v14"/>\n    <path stroke="').concat(t,'" stroke-width="2" stroke-linecap="round" d="M10 4h4m-4 12h4"/>\n    <path d="M11 14h4a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4v2ZM9.5 8H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h4.5v2H5a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h4.5v2Z" fill="').concat(t,'"/>\n  </svg>\n')}};var Nt=class{constructor(e,t){this.onSettled=t,this.spriteMap=new Map,this.inFlight=0,this.headerIcons={...Vt,...e}}drawSprite(e,t,n,r,o,i,l){let a=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1;const[s,c]=function(e,t){return"normal"===e?[t.bgIconHeader,t.fgIconHeader]:"selected"===e?["white",t.accentColor]:[t.accentColor,t.bgHeader]}(t,l),u=i*Math.ceil(window.devicePixelRatio),d="".concat(s,"_").concat(c,"_").concat(u,"_").concat(e);let h=this.spriteMap.get(d);if(void 0===h){const t=this.headerIcons[e];if(void 0===t)return;h=document.createElement("canvas");const n=h.getContext("2d");if(null===n)return;const r=new Image;r.src="data:image/svg+xml;charset=utf-8,".concat(encodeURIComponent(t({fgColor:c,bgColor:s}))),this.spriteMap.set(d,h);const o=r.decode();if(void 0===o)return;this.inFlight++,o.then((()=>{n.drawImage(r,0,0,u,u)})).finally((()=>{this.inFlight--,0===this.inFlight&&this.onSettled()}))}else a<1&&(n.globalAlpha=a),n.drawImage(h,0,0,u,u,r,o,i,i),a<1&&(n.globalAlpha=1)}},Bt={},Wt=null;function jt(e){const t=e.toLowerCase().trim();if(void 0!==Bt[t])return Bt[t];Wt=Wt||function(){const e=document.createElement("div");return e.style.opacity="0",e.style.pointerEvents="none",e.style.position="fixed",document.body.append(e),e}(),Wt.style.color="#000",Wt.style.color=t;const n=getComputedStyle(Wt).color;Wt.style.color="#fff",Wt.style.color=t;const r=getComputedStyle(Wt).color;if(r!==n)return[0,0,0,1];let o=r.replace(/[^\d.,]/g,"").split(",").map(Number.parseFloat);return o.length<4&&o.push(1),o=o.map((t=>{const n=Number.isNaN(t);return n&&console.warn("Could not parse color",e),n?0:t})),Bt[t]=o,o}function Zt(e,t){const[n,r,o]=jt(e);return"rgba(".concat(n,", ").concat(r,", ").concat(o,", ").concat(t,")")}function Ut(e,t){if(void 0===t)return e;const[n,r,o,i]=jt(e);if(1===i)return e;const[l,a,s,c]=jt(t),u=i+c*(1-i),d=(i*r+c*a*(1-i))/u,h=(i*o+c*s*(1-i))/u;return"rgba(".concat((i*n+c*l*(1-i))/u,", ").concat(d,", ").concat(h,", ").concat(u,")")}function Xt(e,t,n){if(n<=0)return e;if(n>=1)return t;const r=[...jt(e)];r[0]=r[0]*r[3],r[1]=r[1]*r[3],r[2]=r[2]*r[3];const o=[...jt(t)];o[0]=o[0]*o[3],o[1]=o[1]*o[3],o[2]=o[2]*o[3];const i=n,l=1-n,a=r[3]*l+o[3]*i,s=Math.floor((r[0]*l+o[0]*i)/a),c=Math.floor((r[1]*l+o[1]*i)/a),u=Math.floor((r[2]*l+o[2]*i)/a);return"rgba(".concat(s,", ").concat(c,", ").concat(u,", ").concat(a,")")}var Yt={kind:me.Loading,allowOverlay:!1};function Kt(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f,g,m,v,b,y){let w,x,k;void 0!==f&&f[0][0]===r&&f[0][1]===t&&(w=f[1][0],x=f[1][1]);const S={ctx:e,theme:c,col:r,row:t,cell:n,rect:{x:o,y:i,width:l,height:a},highlighted:s,hoverAmount:p,hoverX:w,hoverY:x,imageLoader:d,spriteManager:h,hyperWrapping:g,requestAnimationFrame:()=>{C=!0}};let C=!1;const M=function(e,t,n,r,o){const{ctx:i,rect:l,theme:a}=e;let s=Number.MAX_SAFE_INTEGER;if(void 0!==t&&(s=n-t,s<500)){const e=1-s/500;i.globalAlpha=e,i.fillStyle=a.bgSearchResult,i.fillRect(l.x,l.y,l.width,l.height),i.globalAlpha=1,void 0!==r&&(r.fillStyle=a.bgSearchResult)}return o(),s<500}(S,n.lastUpdated,m,v,(()=>{var e,t;if(!(!ze(n)&&!0===(null==u?void 0:u(S)))){const r=y(n);if(void 0!==r){(null==v?void 0:v.renderer)!==r&&(null==(e=null==v?void 0:v.deprep)||e.call(v,S),v=void 0);const o=null==(t=r.drawPrep)?void 0:t.call(r,S,v);r.draw(S,n),k={deprep:null==o?void 0:o.deprep,fillStyle:null==o?void 0:o.fillStyle,font:null==o?void 0:o.font,renderer:r}}}}));return(M||C)&&(null==b||b([r,t])),k}function $t(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f,g,m){let v=arguments.length>17&&void 0!==arguments[17]&&arguments[17];var y,w,x;if(void 0!==s){e.beginPath(),e.save(),e.rect(0,0,i,l);for(const t of s)e.rect(t.x+1,t.y+1,t.width-1,t.height-1);e.clip("evenodd")}const k=null!=(y=m.horizontalBorderColor)?y:m.borderColor,S=m.borderColor;let C=0,M=i,E=0,R=l;if(void 0!==a&&a.length>0){C=Number.MAX_SAFE_INTEGER,E=Number.MAX_SAFE_INTEGER,M=Number.MIN_SAFE_INTEGER,R=Number.MIN_SAFE_INTEGER;for(const e of a)C=Math.min(C,e.x-1),M=Math.max(M,e.x+e.width+1),E=Math.min(E,e.y-1),R=Math.max(R,e.y+e.height+1)}const T=[];e.beginPath();let I=.5;for(let b=0;b<t.length;b++){const e=t[b];if(0===e.width)continue;I+=e.width;const n=e.sticky?I:I+r;n>=C&&n<=M&&p(b+1)&&T.push({x1:n,y1:Math.max(c,E),x2:n,y2:Math.min(l,R),color:S})}const O=d(g-1),P=l-O+.5,D="sticky"===f;if(D&&T.push({x1:C,y1:P,x2:M,y2:P,color:k}),!0!==v){let e=u+.5,t=n;const r=D?l-O:l;for(;e+o<=r;){const n=e+o;if(n>=E&&n<=R-1&&(!D||t!==g-1||Math.abs(n-P)>1)){const e=null==h?void 0:h(t);T.push({x1:C,y1:n,x2:M,y2:n,color:null!=(x=null!=(w=null==e?void 0:e.horizontalBorderColor)?w:null==e?void 0:e.borderColor)?x:k})}e+=d(t),t++}}const H=b(T,(e=>e.color));for(const b of Object.keys(H)){e.strokeStyle=b;for(const t of H[b])e.moveTo(t.x1,t.y1),e.lineTo(t.x2,t.y2);e.stroke(),e.beginPath()}void 0!==s&&e.restore()}function Gt(e,t){const n=[];let r=e.x+e.width-26*t.length;const o=e.y+e.height/2-13;for(let i=0;i<t.length;i++)n.push({x:r,y:o,width:26,height:26}),r+=26;return n}function qt(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}var Qt=30;function Jt(e,t,n,r,o){return o?{x:e,y:t,width:Qt,height:Math.min(Qt,r)}:{x:e+n-Qt,y:Math.max(t,t+r/2-Qt/2),width:Qt,height:Math.min(Qt,r)}}function en(e,t,n,r,o,i,l,a,s,c,u,d,h,p){const f=i.title.startsWith(Te),g="rtl"===dt(i.title),m=Jt(t,n,r,o,g);if(void 0!==h){let p=i;if(f&&(p={...i,title:""}),h({ctx:e,theme:a,rect:{x:t,y:n,width:r,height:o},column:p,columnIndex:p.sourceIndex,isSelected:l,hoverAmount:u,isHovered:s,hasSelectedCell:c,spriteManager:d,menuBounds:m}))return}if(f){let l;return i.title===Ie&&(l=!0),i.title===Oe&&(l=!1),!0!==l&&(e.globalAlpha=u),Pt(e,a,l,t,n,r,o,!1,void 0,void 0,18),void(!0!==l&&(e.globalAlpha=1))}const v=a.cellHorizontalPadding,b=l?a.textHeaderSelected:a.textHeader,y=!0===i.hasMenu&&(s||p&&l),w=g?-1:1;let x=g?t+r-v:t+v;if(void 0!==i.icon){let t=l?"selected":"normal";"highlight"===i.style&&(t=l?"selected":"special");const r=a.headerIconSize;d.drawSprite(i.icon,t,e,g?x-r:x,n+(o-r)/2,r,a),void 0!==i.overlayIcon&&d.drawSprite(i.overlayIcon,l?"selected":"special",e,g?x-r+9:x+9,n+((o-18)/2+6),18,a),x+=Math.ceil(1.3*r)*w}if(y&&!0===i.hasMenu&&r>35){const n=35,o=(g?n:r-n)/r,i=(g?.7*n:r-.7*n)/r,l=e.createLinearGradient(t,0,t+r,0),a=Zt(b,0);l.addColorStop(g?1:0,b),l.addColorStop(o,b),l.addColorStop(i,a),l.addColorStop(g?0:1,a),e.fillStyle=l}else e.fillStyle=b;if(g&&(e.textAlign="right"),e.fillText(i.title,x,n+o/2+St(e,"".concat(a.headerFontStyle," ").concat(a.fontFamily))),g&&(e.textAlign="left"),y&&!0===i.hasMenu){e.beginPath();const t=m.x+m.width/2-5.5,n=m.y+m.height/2-3;!function(e,t,n){const r=function(e,t){const n=t.x-e.x,r=t.y-e.y,o=Math.sqrt(n*n+r*r),i=n/o,l=r/o;return{x:n,y:t.y-e.y,len:o,nx:i,ny:l,ang:Math.atan2(l,i)}};let o;const i=t.length;let l=t[i-1];for(let a=0;a<i;a++){let s=t[a%i];const c=t[(a+1)%i],u=r(s,l),d=r(s,c),h=u.nx*d.ny-u.ny*d.nx,p=u.nx*d.nx-u.ny*-d.ny;let f=Math.asin(h<-1?-1:h>1?1:h),g=1,m=!1;p<0?f<0?f=Math.PI+f:(f=Math.PI-f,g=-1,m=!0):f>0&&(g=-1,m=!0),o=void 0!==s.radius?s.radius:n;const v=f/2;let b,y=Math.abs(Math.cos(v)*o/Math.sin(v));y>Math.min(u.len/2,d.len/2)?(y=Math.min(u.len/2,d.len/2),b=Math.abs(y*Math.sin(v)/Math.cos(v))):b=o;let w=s.x+d.nx*y,x=s.y+d.ny*y;w+=-d.ny*b*g,x+=d.nx*b*g,e.arc(w,x,b,u.ang+Math.PI/2*g,d.ang-Math.PI/2*g,m),l=s,s=c}e.closePath()}(e,[{x:t,y:n},{x:t+11,y:n},{x:t+5.5,y:n+6}],1),e.fillStyle=b,e.fill()}}function tn(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f,g,m,v,b){var y;const w=l+a;if(w<=0)return;e.fillStyle=d.bgHeader,e.fillRect(0,0,o,w);const[x,k]=null!=(y=null==r?void 0:r[0])?y:[],S="".concat(d.headerFontStyle," ").concat(d.fontFamily);e.font=S,un(t,0,i,0,w,((t,r,o,i)=>{var f,y,w;if(void 0!==m&&!m.some((e=>-1===e[1]&&e[0]===t.sourceIndex)))return;const C=Math.max(0,i-r);e.save(),e.beginPath(),e.rect(r+C,a,t.width-C,l),e.clip();const M=g(null!=(f=t.group)?f:"").overrideTheme,E=void 0===t.themeOverride&&void 0===M?d:{...d,...M,...t.themeOverride};E.bgHeader!==d.bgHeader&&(e.fillStyle=E.bgHeader,e.fill());const R="".concat(E.headerFontStyle," ").concat(E.fontFamily);S!==R&&(e.font=R);const T=u.columns.hasIndex(t.sourceIndex),I=void 0!==s||c,O=!I&&-1===k&&x===t.sourceIndex,P=I?0:null!=(w=null==(y=p.find((e=>e.item[0]===t.sourceIndex&&-1===e.item[1])))?void 0:y.hoverAmount)?w:0,D=void 0!==(null==u?void 0:u.current)&&u.current.cell[0]===t.sourceIndex,H=T?E.accentColor:D?E.bgHeaderHasFocus:E.bgHeader,L=n?a:0,z=0===t.sourceIndex?0:1;T?(e.fillStyle=H,e.fillRect(r+z,L,t.width-z,l)):(D||P>0)&&(e.beginPath(),e.rect(r+z,L,t.width-z,l),D&&(e.fillStyle=E.bgHeaderHasFocus,e.fill()),P>0&&(e.globalAlpha=P,e.fillStyle=E.bgHeaderHovered,e.fill(),e.globalAlpha=1)),en(e,r,L,t.width,l,t,T,E,O,D,P,h,v,b),e.restore()})),n&&function(e,t,n,r,o,i,l,a,s,c,u,d){var h;const[p,f]=null!=(h=null==i?void 0:i[0])?h:[];let g=0;dn(t,n,r,o,((t,n,r,s,h,m)=>{var v,b;if(void 0!==d&&!d.some((e=>-2===e[1]&&e[0]>=t[0]&&e[0]<=t[1])))return;e.save(),e.beginPath(),e.rect(r,s,h,m),e.clip();const y=u(n),w=void 0===(null==y?void 0:y.overrideTheme)?l:{...l,...y.overrideTheme},x=-2===f&&void 0!==p&&p>=t[0]&&p<=t[1],k=x?w.bgHeaderHovered:w.bgHeader;if(k!==l.bgHeader&&(e.fillStyle=k,e.fill()),e.fillStyle=null!=(v=w.textGroupHeader)?v:w.textHeader,void 0!==y){let t=r;if(void 0!==y.icon&&(a.drawSprite(y.icon,"normal",e,t+8,(o-20)/2,20,w),t+=26),e.fillText(y.name,t+8,o/2+St(e,"".concat(l.headerFontStyle," ").concat(l.fontFamily))),void 0!==y.actions&&x){const t=Gt({x:r,y:s,width:h,height:m},y.actions);e.beginPath();const n=t[0].x-10,l=r+h-n;e.rect(n,0,l,o);const c=e.createLinearGradient(n,0,n+l,0),u=Zt(k,0);c.addColorStop(0,u),c.addColorStop(10/l,k),c.addColorStop(1,k),e.fillStyle=c,e.fill(),e.globalAlpha=.6;const[d,p]=null!=(b=null==i?void 0:i[1])?b:[-1,-1];for(let o=0;o<y.actions.length;o++){const n=y.actions[o],i=t[o],l=qt(i,d+r,p);l&&(e.globalAlpha=1),a.drawSprite(n.icon,"normal",e,i.x+i.width/2-10,i.y+i.height/2-10,20,w),l&&(e.globalAlpha=.6)}e.globalAlpha=1}}0!==r&&c(t[0])&&(e.beginPath(),e.moveTo(r+.5,0),e.lineTo(r+.5,o),e.strokeStyle=l.borderColor,e.lineWidth=1,e.stroke()),e.restore(),g=r+h})),e.beginPath(),e.moveTo(g+.5,0),e.lineTo(g+.5,o),e.moveTo(0,o+.5),e.lineTo(n,o+.5),e.strokeStyle=l.borderColor,e.lineWidth=1,e.stroke()}(e,t,o,i,a,r,d,h,0,f,g,m)}function nn(e,t,n,r,o,i,l,a){return e<=o+l&&o<=e+n&&t<=i+a&&i<=t+r}function rn(e,t,n,r,o,i,l,a,s,c,u,d,h,p){if(void 0===h||0===h.length)return;const f="sticky"===d?u(c-1):0;e.beginPath(),dn(t,n,l,o,((t,n,r,o,i,l)=>{for(let a=0;a<h.length;a++){const n=h[a];if(-2===n[1]&&n[0]>=t[0]&&n[0]<=t[1]){e.rect(r,o,i,l);break}}})),un(t,s,l,a,i,((t,n,l,a,s)=>{const g=Math.max(0,a-n),m=n+g+1,v=t.width-g-1;for(let r=0;r<h.length;r++){const n=h[r];if(n[0]===t.sourceIndex&&(-1===n[1]||void 0===n[1])){e.rect(m,o,v,i-o);break}}p&&cn(s,l,r,c,u,d,((n,o,i,l)=>{let a=!1;for(let e=0;e<h.length;e++){const n=h[e];if(n[0]===t.sourceIndex&&n[1]===o){a=!0;break}}if(a){const t=n+1,o=(l?t+i-1:Math.min(t+i-1,r-f))-t;o>0&&e.rect(m,t,v,o)}}))})),e.clip()}function on(e,t,n,r,o,i,l){var a,s;const[c,u]=e;let d,h;const p=null!=(s=null==(a=l.find((e=>!e.sticky)))?void 0:a.sourceIndex)?s:0;if(u>p){const e=Math.max(c,p);let a=t,s=r;for(let t=i.sourceIndex-1;t>=e;t--)a-=l[t].width,s+=l[t].width;for(let t=i.sourceIndex+1;t<=u;t++)s+=l[t].width;h={x:a,y:n,width:s,height:o}}if(p>c){const e=Math.min(u,p-1);let a=t,s=r;for(let t=i.sourceIndex-1;t>=c;t--)a-=l[t].width,s+=l[t].width;for(let t=i.sourceIndex+1;t<=e;t++)s+=l[t].width;d={x:a,y:n,width:s,height:o}}return[d,h]}function ln(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f,g,m,v,b,y,w,x,k,S,C,M,E,R,T,I,O){var P;let D=null!=(P=null==b?void 0:b.length)?P:Number.MAX_SAFE_INTEGER;const H=performance.now();let L,z="".concat(T.baseFontStyle," ").concat(T.fontFamily);e.font=z;const F=new Set;return un(t,a,i,l,o,((t,i,l,a,P)=>{var A;const _=Math.max(0,a-i),V=i+_,N=o+1,B=t.width-_,W=r-o-1;if(v.length>0){let e=!1;for(let t=0;t<v.length;t++){const n=v[t];if(nn(V,N,B,W,n.x,n.y,n.width,n.height)){e=!0;break}}if(!e)return}const j=()=>{e.save(),e.beginPath(),e.rect(V,N,B,W),e.clip()},Z=y.columns.hasIndex(t.sourceIndex),U=d(null!=(A=t.group)?A:"").overrideTheme,X=void 0===t.themeOverride&&void 0===U?T:{...T,...U,...t.themeOverride},Y="".concat(X.baseFontStyle," ").concat(X.fontFamily);let K;return Y!==z&&(z=Y,e.font=Y),j(),cn(P,l,r,s,c,m,((r,o,l,c,d)=>{var m,P,A,_;if(o<0)return;if(void 0!==b){let e=!1;for(let n=0;n<b.length;n++){const r=b[n];if(r[0]===t.sourceIndex&&r[1]===o){e=!0;break}}if(!e)return}if(v.length>0){let e=!1;for(let n=0;n<v.length;n++){const o=v[n];if(nn(i,r,t.width,l,o.x,o.y,o.width,o.height)){e=!0;break}}if(!e)return}const V=y.rows.hasIndex(o),N=p.hasIndex(o),B=o<s?u([t.sourceIndex,o]):Yt;let W=i,U=t.width,$=!1,G=!1;if(void 0!==B.span){const[s,c]=B.span,u="".concat(o,",").concat(s,",").concat(c,",").concat(t.sticky);if(F.has(u))return void D--;{const o=on(B.span,i,r,t.width,l,t,n),s=t.sticky?o[0]:o[1];if(t.sticky||void 0===o[0]||(G=!0),void 0!==s){W=s.x,U=s.width,F.add(u),e.restore(),K=void 0,e.save(),e.beginPath();const t=Math.max(0,a-s.x);e.rect(s.x+t,r,s.width-t,l),void 0===L&&(L=[]),L.push({x:s.x+t,y:r,width:s.width-t,height:l}),e.clip(),$=!0}}}const q=null==h?void 0:h(o),Q=d&&void 0!==(null==(m=t.trailingRowOptions)?void 0:m.themeOverride)?null==(P=t.trailingRowOptions)?void 0:P.themeOverride:void 0,J=void 0===B.themeOverride&&void 0===q&&void 0===Q?X:{...X,...q,...Q,...B.themeOverride};e.beginPath();const ee=[t.sourceIndex,o],te=function(e,t,n){if(void 0===(null==n?void 0:n.current))return!1;const[r,o]=n.current.cell,[i,l]=e;return l===o&&(void 0===t.span?r===i:r>=t.span[0]&&r<=t.span[1])}(ee,B,y);let ne=function(e,t,n){let r=0;if(void 0===n.current)return r;gt(e,t,n.current.range)&&r++;for(const o of n.current.rangeStack)gt(e,t,o)&&r++;return r}(ee,B,y);const re=void 0!==B.span&&y.columns.some((e=>void 0!==B.span&&e>=B.span[0]&&e<=B.span[1]));te&&!f&&g?ne=0:te&&(ne=Math.max(ne,1)),re&&ne++,te||(V&&ne++,Z&&!c&&ne++);const oe=B.kind===me.Protected?J.bgCellMedium:J.bgCell;let ie;if((c||oe!==T.bgCell)&&(ie=Ut(oe,ie)),ne>0||N){N&&(ie=Ut(J.bgHeader,ie));for(let e=0;e<ne;e++)ie=Ut(J.accentLight,ie)}else!0===(null==w?void 0:w.some((e=>e[0]===t.sourceIndex&&e[1]===o)))&&(ie=Ut(J.bgSearchResult,ie));if(void 0!==x)for(const e of x){const n=e.range;n.x<=t.sourceIndex&&t.sourceIndex<n.x+n.width&&n.y<=o&&o<n.y+n.height&&(ie=Ut(e.color,ie))}void 0!==ie&&(e.fillStyle=ie,void 0!==K&&(K.fillStyle=ie),e.fillRect(W,r,U,l)),"faded"===B.style&&(e.globalAlpha=.6);const le=M.find((e=>e.item[0]===t.sourceIndex&&e.item[1]===o));if(U>10&&!G){const n="".concat(J.baseFontStyle," ").concat(J.fontFamily);n!==z&&(e.font=n,z=n),K=Kt(e,o,B,t.sourceIndex,W,r,U,l,ne>0,J,k,S,C,null!=(A=null==le?void 0:le.hoverAmount)?A:0,E,R,H,K,I,O)}return"faded"===B.style&&(e.globalAlpha=1),D--,$&&(e.restore(),null==(_=null==K?void 0:K.deprep)||_.call(K,{ctx:e}),K=void 0,j(),z=Y,e.font=Y),D<=0})),e.restore(),D<=0})),L}function an(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f,g){var m;if(void 0===u.current||!l.some((e=>{var t;return e.sourceIndex===(null==(t=u.current)?void 0:t.cell[0])})))return;const[v,b]=u.current.cell,y=h(u.current.cell),w=null!=(m=y.span)?m:[v,v],x="sticky"!==p||"sticky"===p&&b===g-1?0:d(g-1)-1;let k;if(un(l,r,o,i,c,((r,o,i,l,c)=>{if(!(r.sticky&&v>r.sourceIndex)&&!(r.sourceIndex<w[0]||r.sourceIndex>w[1]))return cn(c,i,n,g,d,p,((i,c,u)=>{if(c!==b)return;let d=o,h=r.width;if(void 0!==y.span){const e=on(y.span,o,i,r.width,u,r,a),t=r.sticky?e[0]:e[1];void 0!==t&&(d=t.x,h=t.width)}return k=()=>{var o,a,c,p;l>d&&!r.sticky&&(e.beginPath(),e.rect(l,0,t-l,n),e.clip()),e.beginPath(),e.rect(d+.5,i+.5,h,u),e.strokeStyle=null!=(a=null==(o=r.themeOverride)?void 0:o.accentColor)?a:s.accentColor,e.lineWidth=1,e.stroke(),f&&(e.beginPath(),e.rect(d+h-4,i+u-4,4,4),e.fillStyle=null!=(p=null==(c=r.themeOverride)?void 0:c.accentColor)?p:s.accentColor,e.fill())},!0})),!0})),void 0===k)return;const S=()=>{e.save(),e.beginPath(),e.rect(0,c,t,n-c-x),e.clip(),null==k||k(),e.restore()};return S(),S}function sn(e,t){var n,r;const{canvas:o,headerCanvas:i,width:l,height:a,cellXOffset:s,cellYOffset:c,translateX:u,translateY:d,mappedColumns:h,enableGroups:p,freezeColumns:f,dragAndDropState:g,theme:m,drawFocus:v,headerHeight:b,groupHeaderHeight:y,disabledRows:w,rowHeight:x,verticalBorder:k,isResizing:S,selection:C,fillHandle:M,lastRowSticky:E,rows:R,getCellContent:T,getGroupDetails:I,getRowThemeOverride:O,isFocused:P,drawCustomCell:D,drawHeaderCallback:H,prelightCells:L,highlightRegions:z,imageLoader:F,lastBlitData:A,hoverValues:_,hyperWrapping:V,hoverInfo:N,spriteManager:B,scrolling:W,touchMode:j,enqueue:Z,getCellRenderer:U,renderStrategy:X,bufferA:Y,bufferB:K}=e;let{damage:$}=e;if(0===l||0===a)return;const G="double-buffer"===X,q=W?1:Math.ceil(null!=(n=window.devicePixelRatio)?n:1),Q="direct"!==X&&function(e,t){if(void 0===t)return!1;if(e.width!==t.width||e.height!==t.height||e.theme!==t.theme||e.headerHeight!==t.headerHeight||e.rowHeight!==t.rowHeight||e.rows!==t.rows||e.getRowThemeOverride!==t.getRowThemeOverride||e.isFocused!==t.isFocused||e.isResizing!==t.isResizing||e.verticalBorder!==t.verticalBorder||e.getCellContent!==t.getCellContent||e.highlightRegions!==t.highlightRegions||e.selection!==t.selection||e.dragAndDropState!==t.dragAndDropState||e.prelightCells!==t.prelightCells||e.touchMode!==t.touchMode||e.scrolling!==t.scrolling)return!1;if(e.mappedColumns!==t.mappedColumns){if(e.mappedColumns.length>100||e.mappedColumns.length!==t.mappedColumns.length)return!1;let n;for(let r=0;r<e.mappedColumns.length;r++){const o=e.mappedColumns[r],i=t.mappedColumns[r];if(ce(o,i))continue;if(void 0!==n)return!1;if(o.width===i.width)return!1;const{width:l,...a}=o,{width:s,...c}=i;if(!ce(a,c))return!1;n=r}return void 0===n||n}return!0}(e,t);o.width===l*q&&o.height===a*q||(o.width=l*q,o.height=a*q,o.style.width=l+"px",o.style.height=a+"px");const J=i,ee=p?y+b:b,te=ee+1;J.width===l*q&&J.height===te*q||(J.width=l*q,J.height=te*q,J.style.width=l+"px",J.style.height=te+"px"),!G||Y.width===l*q&&Y.height===a*q||(Y.width=l*q,Y.height=a*q),!G||K.width===l*q&&K.height===a*q||(K.width=l*q,K.height=a*q);const ne=A.current;if(!0===Q&&s===(null==ne?void 0:ne.cellXOffset)&&c===(null==ne?void 0:ne.cellYOffset)&&u===(null==ne?void 0:ne.translateX)&&d===(null==ne?void 0:ne.translateY))return;let re=null;G&&(re=o.getContext("2d",{alpha:!1}));const oe=J.getContext("2d",{alpha:!1});let ie;ie=G?void 0!==$?"b"===(null==ne?void 0:ne.lastBuffer)?K:Y:"b"===(null==ne?void 0:ne.lastBuffer)?Y:K:o;const ae=ie.getContext("2d",{alpha:!1}),se=G?ie===Y?K:Y:o;if(null===oe||null===ae)return;const ue="number"===typeof x?()=>x:x;oe.save(),oe.beginPath(),ae.save(),ae.beginPath(),oe.textBaseline="middle",ae.textBaseline="middle",1!==q&&(oe.scale(q,q),ae.scale(q,q));const de=bt(h,s,l,g,u);let he=[];const pe=v&&(null==(r=C.current)?void 0:r.cell[1])===c&&0===d,fe=()=>{var e,t;tn(oe,de,p,N,l,u,b,y,g,S,C,m,B,_,k,I,$,H,j),$t(oe,de,c,u,d,l,a,void 0,void 0,y,ee,ue,O,k,E,R,m,!0),oe.beginPath(),oe.moveTo(0,te-.5),oe.lineTo(l,te-.5),oe.strokeStyle=Ut(null!=(t=null!=(e=m.headerBottomBorderColor)?e:m.horizontalBorderColor)?t:m.borderColor,m.bgHeader),oe.stroke(),pe&&an(oe,l,a,c,u,d,de,h,m,ee,C,ue,T,E,M,R)};if(void 0!==$){let e=!1;return $=$.filter((t=>(e=e||t[1]<0,t[1]<0||nn(s,c,de.length,300,t[0],t[1],1,1)||nn(0,c,f,300,t[0],t[1],1,1)||E&&nn(s,R-1,de.length,1,t[0],t[1],1,1)))),$.length>0&&(rn(ae,de,l,a,y,ee,u,d,c,R,ue,E,$,!0),ae.fillStyle=m.bgCell,ae.fillRect(0,ee+1,l,a-ee-1),ln(ae,de,h,a,ee,u,d,c,R,ue,T,I,O,w,P,v,E,he,$,C,L,z,D,F,B,_,N,V,m,Z,U),M&&v&&void 0!==C.current&&$.some((e=>{var t,n;return e[0]===(null==(t=C.current)?void 0:t.cell[0])&&e[1]===(null==(n=C.current)?void 0:n.cell[1])}))&&an(ae,l,a,c,u,d,de,h,m,ee,C,ue,T,E,M,R)),e&&(rn(oe,de,l,ee,y,ee,u,d,c,R,ue,E,$,!1),fe()),ae.restore(),oe.restore(),void(null!==re&&(re.fillStyle=m.bgCell,re.fillRect(0,0,l,a),re.drawImage(ae.canvas,0,0)))}if(!0===Q&&s===(null==ne?void 0:ne.cellXOffset)&&u===(null==ne?void 0:ne.translateX)&&pe===(null==ne?void 0:ne.mustDrawFocusOnHeader)||fe(),!0===Q){le(void 0!==se&&void 0!==ne);const{regions:e}=function(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f,g,m){const v=[];let b=!1;e.imageSmoothingEnabled=!1;const y=Math.min(n.cellYOffset,o),w=Math.max(n.cellYOffset,o);let x=0;if("number"===typeof g)x+=(w-y)*g;else for(let I=y;I<w;I++)x+=g(I);o>n.cellYOffset&&(x=-x),x+=l-n.translateY;const k=Math.min(n.cellXOffset,r),S=Math.max(n.cellXOffset,r);let C=0;for(let I=k;I<S;I++)C+=p[I].width;r>n.cellXOffset&&(C=-C),C+=i-n.translateX;let M=vt(f);if(M>0&&M++,0!==C&&0!==x)return{regions:[],yOnly:!1};const E=a?"number"===typeof g?g:g(u-1):0,R=s-M-Math.abs(C),T=c-d-E-Math.abs(x)-1;if(R>150&&T>150){b=0===C;const n={sx:0,sy:0,sw:s*h,sh:c*h,dx:0,dy:0,dw:s*h,dh:c*h};x>0?(n.sy=(d+1)*h,n.sh=T*h,n.dy=(x+d+1)*h,n.dh=T*h,v.push({x:0,y:d,width:s,height:x+1})):x<0&&(n.sy=(-x+d+1)*h,n.sh=T*h,n.dy=(d+1)*h,n.dh=T*h,v.push({x:0,y:c+x-E,width:s,height:-x+E})),C>0?(n.sx=M*h,n.sw=R*h,n.dx=(C+M)*h,n.dw=R*h,v.push({x:M-1,y:0,width:C+2,height:c})):C<0&&(n.sx=(M-C)*h,n.sw=R*h,n.dx=M*h,n.dw=R*h,v.push({x:s+C,y:0,width:-C,height:c})),e.setTransform(1,0,0,1,0,0),M>0&&0!==C&&0===x&&m&&e.drawImage(t,0,0,M*h,c*h,0,0,M*h,c*h),e.drawImage(t,n.sx,n.sy,n.sw,n.sh,n.dx,n.dy,n.dw,n.dh),e.scale(h,h)}return e.imageSmoothingEnabled=!0,{regions:v,yOnly:b}}(ae,se,ne,s,c,u,d,"sticky"===E,l,a,R,ee,q,h,de,x,G);he=e}else if(!1!==Q){le(void 0!==ne);he=function(e,t,n,r,o,i,l,a,s,c){const u=[];return t!==e.cellXOffset||n!==e.cellYOffset||r!==e.translateX||o!==e.translateY||un(s,n,r,o,a,((e,t,n,r)=>{if(e.sourceIndex===c){const e=Math.max(t,r)+1;return u.push({x:e,y:0,width:i-e,height:l}),!0}})),u}(ne,s,c,u,d,l,a,ee,de,Q)}!function(e,t,n,r,o,i,l,a,s){var c;let u=!1;for(const f of t)if(!f.sticky){u=l(f.sourceIndex);break}const d=null!=(c=s.horizontalBorderColor)?c:s.borderColor,h=s.borderColor,p=u?vt(t):0;if(0!==p&&(e.beginPath(),e.moveTo(p+.5,0),e.lineTo(p+.5,r),e.strokeStyle=Ut(h,s.bgCell),e.stroke()),o){const t=a(i-1);e.beginPath(),e.moveTo(0,r-t+.5),e.lineTo(n,r-t+.5),e.strokeStyle=Ut(d,s.bgCell),e.stroke()}}(ae,de,l,a,"sticky"===E,R,k,ue,m);const ge=v?an(ae,l,a,c,u,d,de,h,m,ee,C,ue,T,E,M,R):void 0,me=function(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f){const g=null==f?void 0:f.filter((e=>"no-outline"!==e.style));if(void 0===g||0===g.length)return;const m=g.map((e=>{var f,g,m,v,b;const y=e.range,w=Ft(y.x,y.y,t,n,u,c+u,r,o,i,l,p,s,h,a,d);if(1===y.width&&1===y.height)return y.x<s?[{color:e.color,style:null!=(f=e.style)?f:"dashed",rect:w},void 0]:[void 0,{color:e.color,style:null!=(g=e.style)?g:"dashed",rect:w}];const x=Ft(y.x+y.width-1,y.y+y.height-1,t,n,u,c+u,r,o,i,l,p,s,h,a,d);if(y.x<s&&y.x+y.width>=s){const f=Ft(s-1,y.y+y.height-1,t,n,u,c+u,r,o,i,l,p,s,h,a,d),g=Ft(s,y.y+y.height-1,t,n,u,c+u,r,o,i,l,p,s,h,a,d);return[{color:e.color,style:null!=(m=e.style)?m:"dashed",rect:{x:w.x,y:w.y,width:f.x+f.width-w.x,height:f.y+f.height-w.y}},{color:e.color,style:null!=(v=e.style)?v:"dashed",rect:{x:g.x,y:g.y,width:x.x+x.width-g.x,height:x.y+x.height-g.y}}]}return[void 0,{color:e.color,style:null!=(b=e.style)?b:"dashed",rect:{x:w.x,y:w.y,width:x.x+x.width-w.x,height:x.y+x.height-w.y}}]})),v=vt(a),b=()=>{e.beginPath(),e.save();let r=!1;const o=t=>{r!==t&&(e.setLineDash(t?[5,3]:[]),r=t)};e.lineWidth=1;for(const l of m){const[r]=l;void 0!==r&&nn(0,0,t,n,r.rect.x,r.rect.y,r.rect.width,r.rect.height)&&(o("dashed"===r.style),e.strokeStyle=Zt(r.color,1),e.strokeRect(r.rect.x+1,r.rect.y+1,r.rect.width-2,r.rect.height-2))}let i=!1;for(const l of m){const[,r]=l;void 0!==r&&nn(0,0,t,n,r.rect.x,r.rect.y,r.rect.width,r.rect.height)&&(o("dashed"===r.style),!i&&r.rect.x<v&&(e.rect(v,0,t,n),e.clip(),i=!0),e.strokeStyle=Zt(r.color,1),e.strokeRect(r.rect.x+1,r.rect.y+1,r.rect.width-2,r.rect.height-2))}e.restore()};return b(),b}(ae,l,a,s,c,u,d,h,f,b,y,x,"sticky"===E,R,z);if(ae.fillStyle=m.bgCell,he.length>0){ae.beginPath();for(const e of he)ae.rect(e.x,e.y,e.width,e.height);ae.clip(),ae.fill(),ae.beginPath()}else ae.fillRect(0,0,l,a);const ve=ln(ae,de,h,a,ee,u,d,c,R,ue,T,I,O,w,P,v,E,he,$,C,L,z,D,F,B,_,N,V,m,Z,U);!function(e,t,n,r,o,i,l,a,s,c,u,d,h,p,f,g,m,v){void 0===m&&t[t.length-1]===n[t.length-1]&&un(t,s,l,a,i,((n,l,a,s,m)=>{if(n!==t[t.length-1])return;l+=n.width;const b=Math.max(l,s);b>r||(e.save(),e.beginPath(),e.rect(b,i+1,1e4,o-i-1),e.clip(),cn(m,a,o,c,u,f,((t,n,r,o)=>{if(!o&&g.length>0&&!g.some((e=>nn(l,t,1e4,r,e.x,e.y,e.width,e.height))))return;const i=h.hasIndex(n),a=p.hasIndex(n);e.beginPath();const s=null==d?void 0:d(n),c=void 0===s?v:{...v,...s};c.bgCell!==v.bgCell&&(e.fillStyle=c.bgCell,e.fillRect(l,t,1e4,r)),a&&(e.fillStyle=c.bgHeader,e.fillRect(l,t,1e4,r)),i&&(e.fillStyle=c.accentLight,e.fillRect(l,t,1e4,r))})),e.restore())}))}(ae,de,h,l,a,ee,u,d,c,R,ue,O,C.rows,w,E,he,$,m),$t(ae,de,c,u,d,l,a,he,ve,y,ee,ue,O,k,E,R,m),null==ge||ge(),null==me||me(),null!==re&&(re.fillStyle=m.bgCell,re.fillRect(0,0,l,a),re.drawImage(ae.canvas,0,0));const be=function(e,t,n,r,o,i,l,a,s){let c=0;return un(e,i,r,o,n,((e,n,r,o,i)=>(cn(i,r,t,l,a,s,((e,t,n,r)=>{r||(c=Math.max(t,c))})),!0))),c}(de,a,ee,u,d,c,R,ue,E);null==F||F.setWindow({x:s,y:c,width:de.length,height:be-c},f),A.current={cellXOffset:s,cellYOffset:c,translateX:u,translateY:d,mustDrawFocusOnHeader:pe,lastBuffer:G?ie===Y?"a":"b":void 0},ae.restore(),oe.restore()}function cn(e,t,n,r,o,i,l){let a=t,s=e,c="sticky"===i;for(;a<n||c;){const e=c&&a>=n;e&&(c=!1,s=r-1);const t=o(s);e&&(a=n-t);if(!(c&&s===r-1)&&!0===l(a,s,t,e,"none"!==i&&s===r-1))break;if(e)break;a+=t,s++}}function un(e,t,n,r,o,i){let l=0,a=0;const s=o+r;for(const c of e){if(!0===i(c,c.sticky?a:l+n,s,a,t))break;l+=c.width,a+=c.sticky?c.width:0}}function dn(e,t,n,r,o){var i;let l=0,a=0;for(let s=0;s<e.length;s++){const c=e[s];let u=s+1,d=c.width;for(c.sticky&&(a+=d);u<e.length&&ft(e[u].group,c.group)&&e[u].sticky===e[s].sticky;){const t=e[u];d+=t.width,u++,s++,t.sticky&&(a+=t.width)}const h=l+(c.sticky?0:n),p=c.sticky?0:Math.max(0,a-h),f=Math.min(d-p,t-(h+p));o([c.sourceIndex,e[u-1].sourceIndex],null!=(i=c.group)?i:"",h+p,0,f,r),l+=d}}function hn(e){const t=e-1;return t*t*t+1}var pn=class{constructor(e){this.callback=e,this.currentHoveredItem=void 0,this.leavingItems=[],this.areSameItems=(e,t)=>(null==e?void 0:e[0])===(null==t?void 0:t[0])&&(null==e?void 0:e[1])===(null==t?void 0:t[1]),this.addToLeavingItems=e=>{this.leavingItems.some((t=>this.areSameItems(t.item,e.item)))||this.leavingItems.push(e)},this.removeFromLeavingItems=e=>{var t;const n=this.leavingItems.find((t=>this.areSameItems(t.item,e)));return this.leavingItems=this.leavingItems.filter((e=>e!==n)),null!=(t=null==n?void 0:n.hoverAmount)?t:0},this.cleanUpLeavingElements=()=>{this.leavingItems=this.leavingItems.filter((e=>e.hoverAmount>0))},this.shouldStep=()=>{const e=this.leavingItems.length>0,t=void 0!==this.currentHoveredItem&&this.currentHoveredItem.hoverAmount<1;return e||t},this.getAnimatingItems=()=>void 0!==this.currentHoveredItem?[...this.leavingItems,this.currentHoveredItem]:this.leavingItems.map((e=>({...e,hoverAmount:hn(e.hoverAmount)}))),this.step=e=>{if(void 0===this.lastAnimationTime)this.lastAnimationTime=e;else{const t=(e-this.lastAnimationTime)/80;for(const e of this.leavingItems)e.hoverAmount=i(e.hoverAmount-t,0,1);void 0!==this.currentHoveredItem&&(this.currentHoveredItem.hoverAmount=i(this.currentHoveredItem.hoverAmount+t,0,1));const n=this.getAnimatingItems();this.callback(n),this.cleanUpLeavingElements()}this.shouldStep()?(this.lastAnimationTime=e,window.requestAnimationFrame(this.step)):this.lastAnimationTime=void 0},this.setHovered=e=>{var t;if(!this.areSameItems(null==(t=this.currentHoveredItem)?void 0:t.item,e)){if(void 0!==this.currentHoveredItem&&this.addToLeavingItems(this.currentHoveredItem),void 0!==e){const t=this.removeFromLeavingItems(e);this.currentHoveredItem={item:e,hoverAmount:t}}else this.currentHoveredItem=void 0;void 0===this.lastAnimationTime&&window.requestAnimationFrame(this.step)}}}},fn=class{constructor(e){this.fn=e}get value(){var e;return null!=(e=this.val)?e:this.val=this.fn()}};function gn(e){return new fn(e)}var mn=gn((()=>window.navigator.userAgent.includes("Firefox"))),vn=gn((()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")&&!window.navigator.userAgent.includes("Chrome"))),bn=gn((()=>window.navigator.platform.toLowerCase().startsWith("mac")));var yn=o.memo(o.forwardRef(((e,t)=>{var n,r,l,a,u,d,h,p;const{width:f,height:g,accessibilityHeight:m,columns:v,cellXOffset:b,cellYOffset:y,headerHeight:w,fillHandle:x=!1,groupHeaderHeight:k,rowHeight:S,rows:C,getCellContent:M,getRowThemeOverride:E,onHeaderMenuClick:R,enableGroups:T,isFilling:I,onCanvasFocused:O,onCanvasBlur:P,isFocused:D,selection:H,freezeColumns:L,onContextMenu:z,trailingRowType:F,fixedShadowX:A=!0,fixedShadowY:_=!0,drawFocusRing:V=!0,onMouseDown:N,onMouseUp:B,onMouseMoveRaw:W,onMouseMove:j,onItemHovered:Z,dragAndDropState:U,firstColAccessible:X,onKeyDown:Y,onKeyUp:K,highlightRegions:$,canvasRef:G,onDragStart:q,onDragEnd:Q,eventTargetRef:J,isResizing:ee,isDragging:te,isDraggable:ne=!1,allowResize:re,disabledRows:oe,getGroupDetails:ie,theme:ae,prelightCells:se,headerIcons:ce,verticalBorder:ue,drawHeader:de,drawCustomCell:he,onCellFocused:pe,onDragOverCell:fe,onDrop:ge,onDragLeave:ve,imageWindowLoader:be,smoothScrollX:ye=!1,smoothScrollY:we=!1,experimental:xe,getCellRenderer:Ee}=e,Re=null!=(n=e.translateX)?n:0,Te=null!=(r=e.translateY)?r:0,Ie=Math.max(L,Math.min(v.length-1,b)),Oe=o.useRef(null),Pe=o.useMemo((()=>new Ge),[]),De=null!=be?be:Pe,He=o.useRef(),[Le,Ae]=o.useState(!1),Ve=o.useRef([]),Be=o.useRef(),[We,je]=o.useState(),[Ze,Ue]=o.useState(),Xe=o.useRef(null),[Ye,Ke]=o.useState(!1),$e=o.useRef(Ye);$e.current=Ye;const Qe=o.useMemo((()=>new Nt(ce,(()=>{ut.current=void 0,pt.current()}))),[ce]),Je=T?k+w:w,et=o.useRef(-1),tt=!0!==(null==xe?void 0:xe.enableFirefoxRescaling);o.useLayoutEffect((()=>{mn.value&&1!==window.devicePixelRatio&&!tt&&(-1!==et.current&&Ae(!0),window.clearTimeout(et.current),et.current=window.setTimeout((()=>{Ae(!1),et.current=-1}),200))}),[y,Ie,Re,Te,tt]);const nt=function(e,t){return o.useMemo((()=>e.map(((e,n)=>({...e,sourceIndex:n,sticky:n<t})))),[e,t])}(v,L),rt=o.useCallback(((e,t,n)=>{const r=e.getBoundingClientRect();if(t>=nt.length||n>=C)return;const o=r.width/f,i=Ft(t,n,f,g,k,Je,Ie,y,Re,Te,C,L,"sticky"===F,nt,S);return 1!==o&&(i.x*=o,i.y*=o,i.width*=o,i.height*=o),i.x+=r.x,i.y+=r.y,i}),[f,g,k,Je,Ie,y,Re,Te,C,L,F,nt,S]),ot=o.useCallback(((e,t,n,r)=>{var o,i;const l=e.getBoundingClientRect(),a=l.width/f,s=(t-l.left)/a,c=(n-l.top)/a,u=bt(nt,Ie,f,void 0,Re);let d=0;r instanceof MouseEvent&&(d=r.button);const h=function(e,t,n){let r=0;for(const o of t){if(e<=(o.sticky?r:r+(null!=n?n:0))+o.width)return o.sourceIndex;r+=o.width}return-1}(s,u,Re),p=function(e,t,n,r,o,i,l,a,s,c){const u=r+o;if(n&&e<=o)return-2;if(e<=u)return-1;const d="number"===typeof l?l:l(i-1);if(c&&e>t-d)return i-1;const h=i-(c?1:0),p=e-(null!=s?s:0);if("number"===typeof l){const e=Math.floor((p-u)/l)+a;if(e>=h)return;return e}{let e=u;for(let t=a;t<h;t++){const n=l(t);if(p<=e+n)return t;e+=n}return}}(c,g,T,w,k,C,S,y,Te,"sticky"===F),m=!0===(null==r?void 0:r.shiftKey),v=!0===(null==r?void 0:r.ctrlKey),b=!0===(null==r?void 0:r.metaKey),M=void 0!==r&&!(r instanceof MouseEvent)||"touch"===(null==r?void 0:r.pointerType),E=[Math.abs(s)<20?-1:Math.abs(l.width-s)<20?1:0,Math.abs(c)<20?-1:Math.abs(l.height-c)<20?1:0];let R;if(-1===h||c<0||s<0||void 0===p||s>f||c>g){const n=s>f?-1:s<0?1:0,r=c>g?1:c<0?-1:0;let o=!1;if(-1===h&&-1===p){const n=rt(e,nt.length-1,-1);le(void 0!==n),o=t<n.x+n.width+5}R={kind:Ce,location:[-1!==h?h:s<0?0:nt.length-1,null!=p?p:C-1],direction:[n,r],shiftKey:m,ctrlKey:v,metaKey:b,isEdge:o,isTouch:M,button:d,scrollEdge:E}}else if(p<=-1){let r=rt(e,h,p);le(void 0!==r);let l=void 0!==r&&r.x+r.width-t<=5;const a=h-1;t-r.x<=5&&a>=0?(l=!0,r=rt(e,a,p),le(void 0!==r),R={kind:T&&-2===p?Se:ke,location:[a,p],bounds:r,group:null!=(o=nt[a].group)?o:"",isEdge:l,shiftKey:m,ctrlKey:v,metaKey:b,isTouch:M,localEventX:t-r.x,localEventY:n-r.y,button:d,scrollEdge:E}):R={kind:T&&-2===p?Se:ke,group:null!=(i=nt[h].group)?i:"",location:[h,p],bounds:r,isEdge:l,shiftKey:m,ctrlKey:v,metaKey:b,isTouch:M,localEventX:t-r.x,localEventY:n-r.y,button:d,scrollEdge:E}}else{const r=rt(e,h,p);le(void 0!==r);R={kind:"cell",location:[h,p],bounds:r,isEdge:void 0!==r&&r.x+r.width-t<5,shiftKey:m,ctrlKey:v,isFillHandle:x&&void 0!==r&&r.x+r.width-t<6&&r.y+r.height-n<6,metaKey:b,isTouch:M,localEventX:t-r.x,localEventY:n-r.y,button:d,scrollEdge:E}}return R}),[nt,Ie,f,Re,g,T,w,k,C,S,y,Te,F,rt,x]);const[it]=null!=We?We:[],lt=o.useRef((e=>{})),at=o.useRef(We);at.current=We;const[st,ct]=o.useMemo((()=>{const e=document.createElement("canvas"),t=document.createElement("canvas");return e.style.display="none",e.style.opacity="0",e.style.position="fixed",t.style.display="none",t.style.opacity="0",t.style.position="fixed",[e,t]}),[]);o.useLayoutEffect((()=>(document.documentElement.append(st),document.documentElement.append(ct),()=>{st.remove(),ct.remove()})),[st,ct]);const ut=o.useRef(),ht=o.useCallback((()=>{var e,t;const n=Oe.current,r=Xe.current;if(null===n||null===r)return;const o=ut.current,i={canvas:n,bufferA:st,bufferB:ct,headerCanvas:r,width:f,height:g,cellXOffset:Ie,cellYOffset:y,translateX:Math.round(Re),translateY:Math.round(Te),mappedColumns:nt,enableGroups:T,freezeColumns:L,dragAndDropState:U,theme:ae,headerHeight:w,groupHeaderHeight:k,disabledRows:null!=oe?oe:Ne.empty(),rowHeight:S,verticalBorder:ue,isResizing:ee,isFocused:D,selection:H,fillHandle:x,lastRowSticky:F,rows:C,drawFocus:V,getCellContent:M,getGroupDetails:null!=ie?ie:e=>({name:e}),getRowThemeOverride:E,drawCustomCell:he,drawHeaderCallback:de,prelightCells:se,highlightRegions:$,imageLoader:De,lastBlitData:Be,damage:He.current,hoverValues:Ve.current,hoverInfo:at.current,spriteManager:Qe,scrolling:Le,hyperWrapping:null!=(e=null==xe?void 0:xe.hyperWrapping)&&e,touchMode:Ye,enqueue:lt.current,renderStrategy:null!=(t=null==xe?void 0:xe.renderStrategy)?t:vn.value?"double-buffer":"single-buffer",getCellRenderer:Ee};void 0===i.damage?(ut.current=i,sn(i,o)):sn(i,void 0)}),[st,ct,f,g,Ie,y,Re,Te,nt,T,L,U,ae,w,k,oe,S,ue,ee,D,H,x,F,C,V,M,ie,E,he,de,se,$,De,Qe,Le,null==xe?void 0:xe.hyperWrapping,null==xe?void 0:xe.renderStrategy,Ye,Ee]),pt=o.useRef(ht);o.useLayoutEffect((()=>{ht(),pt.current=ht}),[ht]),o.useLayoutEffect((()=>{(async()=>{var e;void 0!==(null==(e=null==document?void 0:document.fonts)?void 0:e.ready)&&(await document.fonts.ready,ut.current=void 0,pt.current())})()}),[]);const ft=o.useCallback((e=>{He.current=e,pt.current(),He.current=void 0}),[]),gt=function(e){const t=o.useRef([]),n=o.useRef(0),r=o.useRef(e);r.current=e;const i=o.useCallback((()=>{const e=()=>{const e=t.current;t.current=[],r.current(e),t.current.length>0?n.current++:n.current=0};window.requestAnimationFrame(n.current>600?()=>window.requestAnimationFrame(e):e)}),[]);return o.useCallback((e=>{(function(e,t){for(const n of e)if(n[0]===t[0]&&n[1]===t[1])return!0;return!1})(t.current,e)||(0===t.current.length&&i(),t.current.push(e))}),[i])}(ft);lt.current=gt;const mt=o.useCallback((e=>{ft(e.map((e=>e.cell)))}),[ft]);De.setCallback(ft);const[yt,wt]=o.useState(!1),[xt,kt]=null!=it?it:[],St=void 0!==xt&&-1===kt,Ct=void 0!==xt&&-2===kt;let Mt,Et=!1,Rt=!1;if(void 0!==xt&&void 0!==kt&&kt>-1){const e=M([xt,kt],!0);Et=e.kind===Me.NewRow||e.kind===Me.Marker&&"number"!==e.markerKind,Rt=e.kind===me.Boolean&&_e(e),Mt=e.cursor}const Tt=te?"grabbing":null!=Ze&&Ze||ee?"col-resize":yt||I?"crosshair":void 0!==Mt?Mt:St||Et||Rt||Ct?"pointer":"default",It=o.useMemo((()=>({contain:"strict",display:"block",cursor:Tt})),[Tt]),Ot=o.useRef("default"),Pt=null==J?void 0:J.current;null!==Pt&&void 0!==Pt&&Ot.current!==It.cursor&&(Pt.style.cursor=Ot.current=It.cursor);const Dt=o.useCallback(((e,t,n,r)=>{if(void 0===ie)return;const o=ie(e);if(void 0!==o.actions){const e=Gt(t,o.actions);for(const[i,l]of e.entries())if(qt(l,n+t.x,r+l.y))return o.actions[i]}}),[ie]),Ht=o.useCallback(((e,t,n,r)=>{const o=v[t];if(!te&&!ee&&!0===o.hasMenu&&(null==Ze||!Ze)){const i=rt(e,t,-1);le(void 0!==i);const l=Jt(i.x,i.y,i.width,i.height,"rtl"===dt(o.title));if(n>l.x&&n<l.x+l.width&&r>l.y&&r<l.y+l.height)return i}}),[v,rt,Ze,te,ee]),Lt=o.useRef(0),zt=o.useRef(),At=o.useRef(!1),_t=o.useCallback((e=>{const t=Oe.current,n=null==J?void 0:J.current;if(null===t||e.target!==t&&e.target!==n)return;let r,o;if(At.current=!0,e instanceof MouseEvent?(r=e.clientX,o=e.clientY):(r=e.touches[0].clientX,o=e.touches[0].clientY),e.target===n&&null!==n){const e=n.getBoundingClientRect();if(r>e.right||o>e.bottom)return}const i=ot(t,r,o,e);if(zt.current=i.location,i.isTouch&&(Lt.current=Date.now()),$e.current!==i.isTouch&&Ke(i.isTouch),i.kind!==ke||void 0===Ht(t,i.location[0],r,o)){if(i.kind===Se){if(void 0!==Dt(i.group,i.bounds,i.localEventX,i.localEventY))return}null==N||N(i),i.isTouch||!0===ne||ne===i.kind||e.preventDefault()}}),[J,ne,ot,Dt,Ht,N]);qe("touchstart",_t,window,!1),qe("mousedown",_t,window,!1);const Vt=o.useCallback((e=>{var t,n;const r=Oe.current;if(At.current=!1,void 0===B||null===r)return;const o=null==J?void 0:J.current,i=e.target!==r&&e.target!==o;let l,a;if(e instanceof MouseEvent){if(l=e.clientX,a=e.clientY,"touch"===e.pointerType)return}else l=e.changedTouches[0].clientX,a=e.changedTouches[0].clientY;let s=ot(r,l,a,e);s.isTouch&&0!==Lt.current&&Date.now()-Lt.current>500&&(s={...s,isLongTouch:!0}),$e.current!==s.isTouch&&Ke(s.isTouch),!i&&e.cancelable&&e.preventDefault();const[c]=s.location,u=Ht(r,c,l,a);if(s.kind!==ke||void 0===u){if(s.kind===Se){const e=Dt(s.group,s.bounds,s.localEventX,s.localEventY);if(void 0!==e)return void(0===s.button&&e.onClick(s))}B(s,i)}else 0===s.button&&(null==(t=zt.current)?void 0:t[0])===c&&-1===(null==(n=zt.current)?void 0:n[1])||B(s,!0)}),[B,J,ot,Ht,Dt]);qe("mouseup",Vt,window,!1),qe("touchend",Vt,window,!1);qe("click",o.useCallback((e=>{var t,n;const r=Oe.current;if(null===r)return;const o=null==J?void 0:J.current,i=e.target!==r&&e.target!==o;let l,a;e instanceof MouseEvent?(l=e.clientX,a=e.clientY):(l=e.changedTouches[0].clientX,a=e.changedTouches[0].clientY);const s=ot(r,l,a,e);$e.current!==s.isTouch&&Ke(s.isTouch),!i&&e.cancelable&&e.preventDefault();const[c]=s.location,u=Ht(r,c,l,a);if(s.kind===ke&&void 0!==u)0===s.button&&(null==(t=zt.current)?void 0:t[0])===c&&-1===(null==(n=zt.current)?void 0:n[1])&&(null==R||R(c,u));else if(s.kind===Se){const e=Dt(s.group,s.bounds,s.localEventX,s.localEventY);void 0!==e&&0===s.button&&e.onClick(s)}}),[J,ot,Ht,R,Dt]),window,!1);qe("contextmenu",o.useCallback((e=>{const t=Oe.current;if(null===t||void 0===z)return;const n=ot(t,e.clientX,e.clientY,e);z(n,(()=>{e.cancelable&&e.preventDefault()}))}),[ot,z]),null!=(l=null==J?void 0:J.current)?l:null,!1);const Bt=o.useCallback((e=>{He.current=e.map((e=>e.item)),Ve.current=e,pt.current(),He.current=void 0}),[]),Wt=o.useMemo((()=>new pn(Bt)),[Bt]),jt=o.useRef(Wt);jt.current=Wt,o.useLayoutEffect((()=>{const e=jt.current;if(void 0===it||it[1]<0)return void e.setHovered(it);const t=M(it),n=Ee(t);e.setHovered(void 0===n&&t.kind===me.Custom||!0===(null==n?void 0:n.needsHover)?it:void 0)}),[M,Ee,it]);const Zt=o.useRef();qe("mousemove",o.useCallback((e=>{var t;const n=Oe.current;if(null===n)return;const r=null==J?void 0:J.current,o=e.target!==n&&e.target!==r,i=ot(n,e.clientX,e.clientY,e);if("out-of-bounds"!==i.kind&&o&&!At.current&&!i.isTouch)return;if(l=i,a=Zt.current,l===a||(null==l?void 0:l.kind)===(null==a?void 0:a.kind)&&(null==l?void 0:l.location[0])===(null==a?void 0:a.location[0])&&(null==l?void 0:l.location[1])===(null==a?void 0:a.location[1])){if("cell"===i.kind||i.kind===ke||i.kind===Se){const e=[i.location,[i.localEventX,i.localEventY]];if(je(e),at.current=e,"cell"===i.kind){const e=M(i.location);e.kind!==me.Custom&&!0!==(null==(t=Ee(e))?void 0:t.needsHoverPosition)||ft([i.location])}else i.kind===Se&&ft([i.location])}}else null==Z||Z(i),je(i.kind===Ce?void 0:[i.location,[i.localEventX,i.localEventY]]),Zt.current=i;var l,a;const s=i.location[0]>=(X?0:1);if(Ue(i.kind===ke&&i.isEdge&&s&&!0===re),x&&void 0!==H.current){const[t,r]=H.current.cell,o=rt(n,t,r),i=e.clientX,l=e.clientY;le(void 0!==o),wt(i>=o.x+o.width-6&&i<=o.x+o.width&&l>=o.y+o.height-6&&l<=o.y+o.height)}else wt(!1);null==W||W(e),j(i)}),[J,ot,X,re,x,H,W,j,Z,M,Ee,ft,rt]),window,!0);const Ut=o.useCallback((e=>{const t=Oe.current;if(null===t)return;let n,r;void 0!==H.current&&(n=rt(t,H.current.cell[0],H.current.cell[1]),r=H.current.cell),null==Y||Y({bounds:n,stopPropagation:()=>e.stopPropagation(),preventDefault:()=>e.preventDefault(),cancel:()=>{},ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,altKey:e.altKey,key:e.key,keyCode:e.keyCode,rawEvent:e,location:r})}),[Y,H,rt]),Xt=o.useCallback((e=>{const t=Oe.current;if(null===t)return;let n,r;void 0!==H.current&&(n=rt(t,H.current.cell[0],H.current.cell[1]),r=H.current.cell),null==K||K({bounds:n,stopPropagation:()=>e.stopPropagation(),preventDefault:()=>e.preventDefault(),cancel:()=>{},ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,altKey:e.altKey,key:e.key,keyCode:e.keyCode,rawEvent:e,location:r})}),[K,H,rt]),Yt=o.useCallback((e=>{Oe.current=e,void 0!==G&&(G.current=e)}),[G]);qe("dragstart",o.useCallback((e=>{const t=Oe.current;if(null===t||!1===ne||ee)return void e.preventDefault();let n,r;const o=ot(t,e.clientX,e.clientY);if(!0!==ne&&o.kind!==ne)return void e.preventDefault();let i,l,a;let s=!1;if(null==q||q({...o,setData:(e,t)=>{n=e,r=t},setDragImage:(e,t,n)=>{i=e,l=t,a=n},preventDefault:()=>s=!0,defaultPrevented:()=>s}),s||void 0===n||void 0===r||null===e.dataTransfer)e.preventDefault();else if(e.dataTransfer.setData(n,r),e.dataTransfer.effectAllowed="copyLink",void 0!==i&&void 0!==l&&void 0!==a)e.dataTransfer.setDragImage(i,l,a);else{const[n,r]=o.location;if(void 0!==r){const o=document.createElement("canvas"),i=rt(t,n,r);le(void 0!==i),o.width=i.width,o.height=i.height;const l=o.getContext("2d");null!==l&&(l.textBaseline="middle",-1===r?(l.font="".concat(ae.headerFontStyle," ").concat(ae.fontFamily),l.fillStyle=ae.bgHeader,l.fillRect(0,0,o.width,o.height),en(l,0,0,i.width,i.height,nt[n],!1,ae,!1,!1,0,Qe,de,!1)):(l.font="".concat(ae.baseFontStyle," ").concat(ae.fontFamily),l.fillStyle=ae.bgCell,l.fillRect(0,0,o.width,o.height),Kt(l,r,M([n,r]),0,0,0,i.width,i.height,!1,ae,he,De,Qe,1,void 0,!1,0,void 0,void 0,Ee))),o.style.left="-100%",o.style.position="absolute",document.body.append(o),e.dataTransfer.setDragImage(o,i.width/2,i.height/2),window.setTimeout((()=>{o.remove()}),0)}}}),[ne,ee,ot,q,rt,ae,nt,Qe,de,M,he,De,Ee]),null!=(a=null==J?void 0:J.current)?a:null,!1,!1);const $t=o.useRef();qe("dragover",o.useCallback((e=>{var t;const n=Oe.current;if(void 0!==ge&&e.preventDefault(),null===n||void 0===fe)return;const r=ot(n,e.clientX,e.clientY),[o,i]=r.location,l=o-(X?0:1),[a,s]=null!=(t=$t.current)?t:[];a===l&&s===i||($t.current=[l,i],fe([l,i],e.dataTransfer))}),[X,ot,fe,ge]),null!=(u=null==J?void 0:J.current)?u:null,!1,!1);qe("dragend",o.useCallback((()=>{$t.current=void 0,null==Q||Q()}),[Q]),null!=(d=null==J?void 0:J.current)?d:null,!1,!1);qe("drop",o.useCallback((e=>{const t=Oe.current;if(null===t||void 0===ge)return;e.preventDefault();const n=ot(t,e.clientX,e.clientY),[r,o]=n.location;ge([r-(X?0:1),o],e.dataTransfer)}),[X,ot,ge]),null!=(h=null==J?void 0:J.current)?h:null,!1,!1);qe("dragleave",o.useCallback((()=>{null==ve||ve()}),[ve]),null!=(p=null==J?void 0:J.current)?p:null,!1,!1);const Qt=o.useRef(H);Qt.current=H;const tn=o.useRef(null),nn=o.useCallback((e=>{var t;null!==Oe.current&&Oe.current.contains(document.activeElement)&&(null===e&&void 0!==Qt.current.current?null==(t=null==G?void 0:G.current)||t.focus({preventScroll:!0}):null!==e&&e.focus({preventScroll:!0}),tn.current=e)}),[G]);o.useImperativeHandle(t,(()=>({focus:()=>{var e;const t=tn.current;null!==t&&document.contains(t)?t.focus({preventScroll:!0}):null==(e=null==G?void 0:G.current)||e.focus({preventScroll:!0})},getBounds:(e,t)=>{if(void 0!==G&&null!==G.current)return rt(G.current,e,null!=t?t:-1)},damage:mt})),[G,mt,rt]);const rn=o.useRef(),on=function(e,t,n){const[r,i]=o.useState(e),l=o.useRef(!0);o.useEffect((()=>()=>{l.current=!1}),[]);const a=o.useRef(c((e=>{l.current&&i(e)}),n));return o.useLayoutEffect((()=>{l.current&&a.current((()=>e()))}),t),r}((()=>{var e,t,n,r;if(f<50)return null;let i=bt(nt,Ie,f,U,Re);const l=X?0:-1;X||0!==(null==(e=i[0])?void 0:e.sourceIndex)||(i=i.slice(1));const[a,c]=null!=(n=null==(t=H.current)?void 0:t.cell)?n:[],u=null==(r=H.current)?void 0:r.range,d=i.map((e=>e.sourceIndex)),h=s(y,Math.min(C,y+m));return void 0===a||void 0===c||d.includes(a)&&h.includes(c)||nn(null),o.createElement("table",{key:"access-tree",role:"grid","aria-rowcount":C+1,"aria-multiselectable":"true","aria-colcount":nt.length+l},o.createElement("thead",{role:"rowgroup"},o.createElement("tr",{role:"row","aria-rowindex":1},i.map((e=>o.createElement("th",{role:"columnheader","aria-selected":H.columns.hasIndex(e.sourceIndex),"aria-colindex":e.sourceIndex+1+l,tabIndex:-1,onFocus:t=>{if(t.target!==tn.current)return null==pe?void 0:pe([e.sourceIndex,-1])},key:e.sourceIndex},e.title))))),o.createElement("tbody",{role:"rowgroup"},h.map((e=>o.createElement("tr",{role:"row","aria-selected":H.rows.hasIndex(e),key:e,"aria-rowindex":e+2},i.map((t=>{const n=t.sourceIndex,r="".concat(n,",").concat(e),i=a===n&&c===e,s=void 0!==u&&n>=u.x&&n<u.x+u.width&&e>=u.y&&e<u.y+u.height,d="glide-cell-".concat(n,"-").concat(e),h=[n,e],p=M(h,!0);return o.createElement("td",{key:r,role:"gridcell","aria-colindex":n+1+l,"aria-selected":s,"aria-readonly":ze(p)||!Fe(p),id:d,"data-testid":d,onClick:()=>{const t=null==G?void 0:G.current;if(null!==t&&void 0!==t)return null==Y?void 0:Y({bounds:rt(t,n,e),cancel:()=>{},preventDefault:()=>{},stopPropagation:()=>{},ctrlKey:!1,key:"Enter",keyCode:13,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:h})},onFocusCapture:t=>{var r,o;if(t.target!==tn.current&&((null==(r=rn.current)?void 0:r[0])!==n||(null==(o=rn.current)?void 0:o[1])!==e))return rn.current=h,null==pe?void 0:pe(h)},ref:i?nn:void 0,tabIndex:-1},((e,t)=>{var n;if(e.kind===me.Custom)return e.copyData;const r=null==t?void 0:t(e);return null!=(n=null==r?void 0:r.getAccessibilityString(e))?n:""})(p,Ee))})))))))}),[f,nt,Ie,U,Re,C,y,m,H,nn,M,G,Y,rt,pe],200),ln=A?vt(nt,U):0,an=0!==L&&A?Ie>L?1:i(-Re/100,0,1):0,cn=_?i(-(32*-y+Te)/100,0,1):0,un=o.useMemo((()=>{if(!an&&!cn)return null;const e={position:"absolute",top:0,left:ln,width:f-ln,height:g,opacity:an,pointerEvents:"none",transition:ye?void 0:"opacity 0.2s",boxShadow:"inset 13px 0 10px -13px rgba(0, 0, 0, 0.2)"},t={position:"absolute",top:Je,left:0,width:f,height:g,opacity:cn,pointerEvents:"none",transition:we?void 0:"opacity 0.2s",boxShadow:"inset 0 13px 10px -13px rgba(0, 0, 0, 0.2)"};return o.createElement(o.Fragment,null,an>0&&o.createElement("div",{id:"shadow-x",style:e}),cn>0&&o.createElement("div",{id:"shadow-y",style:t}))}),[an,cn,ln,f,ye,Je,g,we]),dn=o.useMemo((()=>({position:"absolute",top:0,left:0})),[]);return o.createElement(o.Fragment,null,o.createElement("canvas",{"data-testid":"data-grid-canvas",tabIndex:0,onKeyDown:Ut,onKeyUp:Xt,onFocus:O,onBlur:P,ref:Yt,style:It},on),o.createElement("canvas",{ref:Xe,style:dn}),un)})));function wn(e,t,n,r){var o;return i(Math.round(t-(null!=(o=e.growOffset)?o:0)),Math.ceil(n),Math.floor(r))}var xn=e=>{var t;const[n,r]=o.useState(),[i,l]=o.useState(),[a,s]=o.useState(),[c,u]=o.useState(),[d,h]=o.useState(!1),[p,f]=o.useState(),[g,m]=o.useState(),[v,b]=o.useState(),[y,w]=o.useState(!1),[x,k]=o.useState(),{onHeaderMenuClick:S,getCellContent:C,onColumnMoved:M,onColumnResize:E,onColumnResizeStart:R,onColumnResizeEnd:T,gridRef:I,maxColumnWidth:O,minColumnWidth:P,onRowMoved:D,lockColumns:H,onMouseDown:L,onMouseUp:z,onItemHovered:F,onDragStart:A,canvasRef:_}=e,V=void 0!==(null!=(t=null!=E?E:T)?t:R),{columns:N,selection:B}=e,W=B.columns,j=o.useCallback((e=>{const[t,n]=e.location;void 0!==a&&c!==t&&t>=H?(h(!0),u(t)):void 0!==g&&void 0!==n?(w(!0),b(Math.max(0,n))):null==F||F(e)}),[a,g,c,F,H]),Z=void 0!==M,U=o.useCallback((e=>{var t,n;if(0===e.button){const[o,i]=e.location;if("out-of-bounds"===e.kind&&e.isEdge&&V){const e=null==(t=null==I?void 0:I.current)?void 0:t.getBounds(N.length-1,-1);void 0!==e&&(r(e.x),l(N.length-1))}else if("header"===e.kind&&o>=H){const t=null==_?void 0:_.current;if(e.isEdge&&V&&t){r(e.bounds.x),l(o);const i=t.getBoundingClientRect().width/t.offsetWidth,a=e.bounds.width/i;null==R||R(N[o],a,o,a+(null!=(n=N[o].growOffset)?n:0))}else"header"===e.kind&&Z&&(f(e.bounds.x),s(o))}else"cell"===e.kind&&H>0&&0===o&&void 0!==i&&void 0!==D&&(k(e.bounds.y),m(i))}null==L||L(e)}),[L,V,H,D,I,N,Z,R,_]),X=o.useCallback(((e,t)=>{d||y||null==S||S(e,t)}),[d,y,S]),Y=o.useRef(-1),K=o.useCallback((()=>{Y.current=-1,m(void 0),b(void 0),k(void 0),w(!1),s(void 0),u(void 0),f(void 0),h(!1),l(void 0),r(void 0)}),[]),$=o.useCallback(((e,t)=>{var n,r,o;if(0===e.button){if(void 0!==i){if(!0===(null==W?void 0:W.hasIndex(i)))for(const t of W){if(t===i)continue;const e=N[t],r=wn(e,Y.current,P,O);null==E||E(e,r,t,r+(null!=(n=e.growOffset)?n:0))}const e=wn(N[i],Y.current,P,O);if(null==T||T(N[i],e,i,e+(null!=(r=N[i].growOffset)?r:0)),W.hasIndex(i))for(const t of W){if(t===i)continue;const e=N[t],n=wn(e,Y.current,P,O);null==T||T(e,n,t,n+(null!=(o=e.growOffset)?o:0))}}K(),void 0!==a&&void 0!==c&&(null==M||M(a,c)),void 0!==g&&void 0!==v&&(null==D||D(g,v))}null==z||z(e,t)}),[z,i,a,c,g,v,W,T,N,P,O,E,M,D,K]),G=o.useMemo((()=>{if(void 0!==a&&void 0!==c&&a!==c)return{src:a,dest:c}}),[a,c]),q=o.useCallback((e=>{var t,r;const o=null==_?void 0:_.current;if(void 0!==a&&void 0!==p){Math.abs(e.clientX-p)>20&&h(!0)}else if(void 0!==g&&void 0!==x){Math.abs(e.clientY-x)>20&&w(!0)}else if(void 0!==i&&void 0!==n&&o){const l=o.getBoundingClientRect().width/o.offsetWidth,a=(e.clientX-n)/l,s=N[i],c=wn(s,a,P,O);if(null==E||E(s,c,i,c+(null!=(t=s.growOffset)?t:0)),Y.current=a,(null==W?void 0:W.first())===i)for(const e of W){if(e===i)continue;const t=N[e],n=wn(t,Y.current,P,O);null==E||E(t,n,e,n+(null!=(r=t.growOffset)?r:0))}}}),[a,p,g,x,i,n,N,P,O,E,W,_]),Q=o.useCallback(((e,t)=>{if(void 0===g||void 0===v)return C(e,t);let[n,r]=e;return r===v?r=g:(r>v&&(r-=1),r>=g&&(r+=1)),C([n,r],t)}),[g,v,C]),J=o.useCallback((e=>{null==A||A(e),e.defaultPrevented()||K()}),[K,A]);return o.createElement(yn,{accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,drawCustomCell:e.drawCustomCell,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,enableGroups:e.enableGroups,eventTargetRef:e.eventTargetRef,experimental:e.experimental,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,headerIcons:e.headerIcons,height:e.height,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isDraggable:e.isDraggable,isFilling:e.isFilling,isFocused:e.isFocused,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDrop:e.onDrop,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,theme:e.theme,trailingRowType:e.trailingRowType,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,width:e.width,getCellContent:Q,isResizing:void 0!==i,onHeaderMenuClick:X,isDragging:d,onItemHovered:j,onDragStart:J,onMouseDown:U,allowResize:V,onMouseUp:$,dragAndDropState:G,onMouseMoveRaw:q,ref:I})};var kn=(0,r.d)("div")({name:"ScrollRegionStyle",class:"s1jz82f8",vars:{"s1jz82f8-0":[e=>e.isSafari?"scroll":"auto"]}});function Sn(e){e.stopPropagation()}var Cn=e=>{var t,n,r,i;const{children:l,clientHeight:a,scrollHeight:s,scrollWidth:c,update:u,draggable:d,className:h,preventDiagonalScrolling:p=!1,paddingBottom:f=0,paddingRight:g=0,rightElement:m,rightElementProps:v,scrollRef:b,scrollToEnd:y,initialSize:w,minimap:x}=e,k=[],S=null!=(t=null==v?void 0:v.sticky)&&t,C=null!=(n=null==v?void 0:v.fill)&&n,M=o.useRef(0),E=o.useRef(0),R=o.useRef(null),T=window.devicePixelRatio;o.useEffect((()=>{const e=R.current;null!==e&&!0===y&&(e.scrollLeft=e.scrollWidth-e.clientWidth)}),[y]);const I=o.useRef({scrollLeft:0,scrollTop:0,lockDirection:void 0}),O=o.useRef(null),P=function(e){const[t,n]=o.useState(!1),r=o.useRef(0);return qe("touchstart",o.useCallback((()=>{window.clearTimeout(r.current),n(!0)}),[]),window,!0,!1),qe("touchend",o.useCallback((t=>{0===t.touches.length&&(r.current=window.setTimeout((()=>n(!1)),e))}),[e]),window,!0,!1),t}(200),[D,H]=o.useState(!0),L=o.useRef(0);o.useEffect((()=>{if(!D||P||void 0===I.current.lockDirection)return;const e=R.current;if(null===e)return;const[t,n]=I.current.lockDirection;void 0!==t?e.scrollLeft=t:void 0!==n&&(e.scrollTop=n),I.current.lockDirection=void 0}),[P,D]);const z=o.useCallback((()=>{var e,t,n,r;const o=R.current;if(null===o)return;let i=o.scrollTop,l=o.scrollLeft;const a=I.current.scrollTop,c=I.current.scrollLeft,d=l-c,h=i-a;P&&0!==d&&0!==h&&(Math.abs(d)>3||Math.abs(h)>3)&&p&&void 0===I.current.lockDirection&&(I.current.lockDirection=Math.abs(d)<Math.abs(h)?[c,void 0]:[void 0,a]);const m=I.current.lockDirection;l=null!=(e=null==m?void 0:m[0])?e:l,i=null!=(t=null==m?void 0:m[1])?t:i,I.current.scrollLeft=l,I.current.scrollTop=i;const v=i,b=E.current-v,y=o.scrollHeight-o.clientHeight;if(E.current=v,y>0&&(Math.abs(b)>2e3||0===v||v===y)&&s>o.scrollHeight+5){const e=v/y,t=(s-o.clientHeight)*e;M.current=t-v}void 0!==m&&(window.clearTimeout(L.current),H(!1),L.current=window.setTimeout((()=>H(!0)),200)),u({x:l,y:v+M.current,width:o.clientWidth-g,height:o.clientHeight-f,paddingRight:null!=(r=null==(n=O.current)?void 0:n.clientWidth)?r:0})}),[f,g,s,u,p,P]),F=o.useRef(z);F.current=z;const A=o.useRef(),_=o.useRef(!1);o.useEffect((()=>{_.current?z():_.current=!0}),[z,f,g]);const V=o.useCallback((e=>{R.current=e,void 0!==b&&(b.current=e)}),[b]);let N=0,B=0;for(k.push(o.createElement("div",{key:N++,style:{width:c,height:0}}));B<s;){const e=Math.min(5e6,s-B);k.push(o.createElement("div",{key:N++,style:{width:0,height:e}})),B+=e}const{ref:W,width:j,height:Z}=function(e){const t=(0,o.useRef)(null),[n,r]=(0,o.useState)({width:null==e?void 0:e[0],height:null==e?void 0:e[1]});return(0,o.useLayoutEffect)((()=>{const e=new window.ResizeObserver((e=>{for(const t of e){const{width:e,height:n}=t&&t.contentRect||{};r((t=>t.width===e&&t.height===n?t:{width:e,height:n}))}}));return t.current&&e.observe(t.current,void 0),()=>{e.disconnect()}}),[t.current]),{ref:t,...n}}(w);return(null==(r=A.current)?void 0:r.height)===Z&&(null==(i=A.current)?void 0:i.width)===j||(window.setTimeout((()=>F.current()),0),A.current={width:j,height:Z}),0===(null!=j?j:0)||0===(null!=Z?Z:0)?o.createElement("div",{ref:W}):o.createElement("div",{ref:W},o.createElement(kn,{isSafari:vn.value},x,o.createElement("div",{className:"dvn-underlay"},l),o.createElement("div",{ref:V,style:A.current,draggable:d,onDragStart:e=>{d||(e.stopPropagation(),e.preventDefault())},className:"dvn-scroller "+(null!=h?h:""),onScroll:z},o.createElement("div",{className:"dvn-scroll-inner"+(void 0===m?" hidden":"")},o.createElement("div",{className:"dvn-stack"},k),void 0!==m&&o.createElement(o.Fragment,null,!C&&o.createElement("div",{className:"dvn-spacer"}),o.createElement("div",{ref:O,onMouseDown:Sn,onMouseUp:Sn,onMouseMove:Sn,style:{height:Z,maxHeight:a-Math.ceil(T%1),position:"sticky",top:0,paddingLeft:1,marginBottom:-40,marginRight:g,flexGrow:C?1:void 0,right:S?null!=g?g:0:void 0,pointerEvents:"auto"}},m))))))},Mn=(0,r.d)("div")({name:"MinimapStyle",class:"m15w2ly5"}),En=e=>{var t,n,r;const{columns:l,rows:a,rowHeight:s,headerHeight:c,groupHeaderHeight:u,enableGroups:d,freezeColumns:h,experimental:p,clientSize:f,className:g,onVisibleRegionChanged:m,scrollToEnd:v,scrollRef:b,preventDiagonalScrolling:y,rightElement:w,rightElementProps:x,overscrollX:k,overscrollY:S,showMinimap:C=!1,initialSize:M,smoothScrollX:E=!1,smoothScrollY:R=!1,isDraggable:T}=e,{paddingRight:I,paddingBottom:O}=null!=p?p:{},[P,D]=f,H=o.useRef(),L=o.useRef(),z=o.useRef(),F=o.useRef(),A=o.useMemo((()=>{let e=Math.max(0,null!=k?k:0);for(const t of l)e+=t.width;return e}),[l,k]);let _=d?c+u:c;if("number"===typeof s)_+=a*s;else for(let o=0;o<a;o++)_+=s(o);void 0!==S&&(_+=S);const V=o.useRef(),N=o.useCallback((()=>{var e,t,n;if(void 0===V.current)return;const r={...V.current};let o=0,i=r.x<0?-r.x:0,c=0,u=0;r.x=r.x<0?0:r.x;let d=0;for(let a=0;a<h;a++)d+=l[a].width;for(const a of l){const e=o-d;if(r.x>=e+a.width)o+=a.width,u++,c++;else if(r.x>e)o+=a.width,E?i+=e-r.x:u++,c++;else{if(!(r.x+r.width>e))break;o+=a.width,c++}}let p=0,f=0,g=0;if("number"===typeof s)R?(f=Math.floor(r.y/s),p=f*s-r.y):f=Math.ceil(r.y/s),g=Math.ceil(r.height/s)+f,p<0&&g++;else{let e=0;for(let t=0;t<a;t++){const n=s(t),o=e+(R?0:n/2);if(r.y>=e+n)e+=n,f++,g++;else if(r.y>o)e+=n,R?p+=o-r.y:f++,g++;else{if(!(r.y+r.height>n/2+e))break;e+=n,g++}}}const v={x:u,y:f,width:c-u,height:g-f},b=H.current;void 0!==b&&b.y===v.y&&b.x===v.x&&b.height===v.height&&b.width===v.width&&L.current===i&&z.current===p&&r.width===(null==(e=F.current)?void 0:e[0])&&r.height===(null==(t=F.current)?void 0:t[1])||(null==m||m({x:u,y:f,width:c-u,height:g-f},r.width,r.height,null!=(n=r.paddingRight)?n:0,i,p),H.current=v,L.current=i,z.current=p,F.current=[r.width,r.height])}),[l,s,a,m,h,E,R]),B=o.useCallback((e=>{V.current=e,N()}),[N]);o.useEffect((()=>{N()}),[N]);const W=null!=(t=null==b?void 0:b.current)?t:void 0,j=i(A/_,2/3,1.5),Z=200,U=j>1?Z:Math.ceil(Z*j),X=j>1?Math.ceil(Z/j):Z,Y=U/A,K=X/_,$=Math.min(P*Math.max(Y,.01),U),G=Math.min(D*Math.max(K,.01),X),q=(null!=(n=null==W?void 0:W.scrollLeft)?n:0)/(A-P)*(U-$),Q=(null!=(r=null==W?void 0:W.scrollTop)?r:0)/(_-D)*(X-G),J=o.useMemo((()=>{if(!C||0===$||0===G)return;const e=e=>{if(void 0===W)return;const t=e.currentTarget.getBoundingClientRect(),n=e.clientX-t.x-$/2,r=e.clientY-t.y-G/2,o=(A-W.clientWidth)*(n/(U-$)),i=(_-W.clientHeight)*(r/(X-G));W.scrollTo({left:o,top:i,behavior:"mousemove"===e.type?"auto":"smooth"})};return o.createElement(Mn,{style:{width:U,height:X},"data-testid":"minimap-container",onMouseMove:t=>{1===t.buttons&&e(t)},onClick:e},o.createElement("div",{className:"header"}),o.createElement("div",{className:"locationMarker",onDragStart:e=>e.preventDefault(),style:{left:q,top:Q,width:$,height:G,borderRadius:Math.min($,.2*G,9)}}))}),[X,_,q,W,C,Q,G,$,U,A]);return o.createElement(Cn,{scrollRef:b,minimap:J,className:g,preventDiagonalScrolling:y,draggable:!0===T||"string"===typeof T,scrollWidth:A+(null!=I?I:0),scrollHeight:_+(null!=O?O:0),clientHeight:D,rightElement:w,paddingBottom:O,paddingRight:I,rightElementProps:x,update:B,initialSize:M,scrollToEnd:v},o.createElement(xn,{eventTargetRef:b,width:P,height:D,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,theme:e.theme,trailingRowType:e.trailingRowType,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,drawCustomCell:e.drawCustomCell,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY}))},Rn=(0,r.d)("div")({name:"SearchWrapper",class:"sxep88s",vars:{"sxep88s-0":[e=>e.showSearch?0:400,"px"]}}),Tn=o.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},o.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 244l144-144 144 144M256 120v292"})),In=o.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},o.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 268l144 144 144-144M256 392V100"})),On=o.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},o.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M368 368L144 144M368 144L144 368"})),Pn=e=>{const{canvasRef:t,cellYOffset:n,rows:r,columns:i,searchInputRef:l,searchValue:a,searchResults:s,onSearchValueChange:c,getCellsForSelection:u,onSearchResultsChanged:d,showSearch:h=!1,onSearchClose:p}=e,[f]=o.useState((()=>"search-box-"+Math.round(1e3*Math.random()))),[g,m]=o.useState(""),v=null!=a?a:g,b=o.useCallback((e=>{m(e),null==c||c(e)}),[c]),[y,w]=o.useState(),x=o.useRef(y);x.current=y,o.useEffect((()=>{void 0!==s&&(s.length>0?w((e=>{var t;return{rowsSearched:r,results:s.length,selectedIndex:null!=(t=null==e?void 0:e.selectedIndex)?t:-1}})):w(void 0))}),[r,s]);const k=o.useRef(new AbortController),S=o.useRef(),[C,M]=o.useState([]),E=null!=s?s:C,R=o.useCallback((()=>{void 0!==S.current&&(window.cancelAnimationFrame(S.current),S.current=void 0,k.current.abort())}),[]),T=o.useRef(n);T.current=n;const I=o.useCallback((e=>{const t=new RegExp(e.replace(/([$()*+.?[\\\]^{|}-])/g,"\\$1"),"i");let n=T.current,o=Math.min(10,r),l=0;w(void 0),M([]);const a=[],s=async()=>{var e,c;if(void 0===u)return;const h=performance.now(),p=r-l;let f=u({x:0,y:n,width:i.length,height:Math.min(o,p,r-n)},k.current.signal);"function"===typeof f&&(f=await f());let g=!1;for(const[r,o]of f.entries())for(const[e,i]of o.entries()){let o;switch(i.kind){case me.Text:case me.Number:o=i.displayData;break;case me.Uri:case me.Markdown:o=i.data;break;case me.Boolean:o="boolean"===typeof i.data?i.data.toString():void 0;break;case me.Image:case me.Bubble:o=i.data.join("\ud83d\udc33");break;case me.Custom:o=i.copyData}void 0!==o&&t.test(o)&&(a.push([e,r+n]),g=!0)}const m=performance.now();g&&M([...a]),l+=f.length,le(l<=r);const v=null!=(c=null==(e=x.current)?void 0:e.selectedIndex)?c:-1;w({results:a.length,rowsSearched:l,selectedIndex:v}),null==d||d(a,v),n+o>=r?n=0:n+=o;const b=m-h,y=10/Math.max(b,1);o=Math.ceil(o*y),l<r&&a.length<1e3&&(S.current=window.requestAnimationFrame(s))};R(),S.current=window.requestAnimationFrame(s)}),[R,i.length,u,d,r]),O=o.useCallback((()=>{var e;null==p||p(),w(void 0),M([]),null==d||d([],-1),R(),null==(e=null==t?void 0:t.current)||e.focus()}),[R,t,p,d]),P=o.useCallback((e=>{b(e.target.value),void 0===s&&(""===e.target.value?(w(void 0),M([]),R()):I(e.target.value))}),[I,R,b,s]);o.useEffect((()=>{h&&null!==l.current&&(b(""),l.current.focus({preventScroll:!0}))}),[h,l,b]);const D=o.useCallback((e=>{var t;if(null==(t=null==e?void 0:e.stopPropagation)||t.call(e),void 0===y)return;const n=(y.selectedIndex+1)%y.results;w({...y,selectedIndex:n}),null==d||d(E,n)}),[y,d,E]),H=o.useCallback((e=>{var t;if(null==(t=null==e?void 0:e.stopPropagation)||t.call(e),void 0===y)return;let n=(y.selectedIndex-1)%y.results;n<0&&(n+=y.results),w({...y,selectedIndex:n}),null==d||d(E,n)}),[d,E,y]),L=o.useCallback((e=>{(e.ctrlKey||e.metaKey)&&"KeyF"===e.nativeEvent.code||"Escape"===e.key?(O(),e.stopPropagation(),e.preventDefault()):"Enter"===e.key&&(e.shiftKey?H():D())}),[O,D,H]);o.useEffect((()=>()=>{R()}),[R]);const z=o.useMemo((()=>{var e,t,n;let i;void 0!==y&&(i=y.results>=1e3?"over 1000":"".concat(y.results," result").concat(1!==y.results?"s":""),y.selectedIndex>=0&&(i="".concat(y.selectedIndex+1," of ").concat(i)));const a=e=>{e.stopPropagation()},s=Math.floor((null!=(e=null==y?void 0:y.rowsSearched)?e:0)/r*100),c={width:"".concat(s,"%")};return o.createElement(Rn,{showSearch:h,onMouseDown:a,onMouseMove:a,onMouseUp:a,onClick:a},o.createElement("div",{className:"search-bar-inner"},o.createElement("input",{id:f,"aria-hidden":!h,"data-testid":"search-input",ref:l,onChange:P,value:v,tabIndex:h?void 0:-1,onKeyDownCapture:L}),o.createElement("button",{"aria-label":"Previous Result","aria-hidden":!h,tabIndex:h?void 0:-1,onClick:H,disabled:0===(null!=(t=null==y?void 0:y.results)?t:0)},Tn),o.createElement("button",{"aria-label":"Next Result","aria-hidden":!h,tabIndex:h?void 0:-1,onClick:D,disabled:0===(null!=(n=null==y?void 0:y.results)?n:0)},In),void 0!==p&&o.createElement("button",{"aria-label":"Close Search","aria-hidden":!h,"data-testid":"search-close-button",tabIndex:h?void 0:-1,onClick:O},On)),void 0!==y?o.createElement(o.Fragment,null,o.createElement("div",{className:"search-status"},o.createElement("div",{"data-testid":"search-result-area"},i)),o.createElement("div",{className:"search-progress",style:c})):o.createElement("div",{className:"search-status"},o.createElement("label",{htmlFor:f},"Type to search")))}),[O,D,H,P,p,L,r,y,v,h,f,l]);return o.createElement(o.Fragment,null,o.createElement(En,{prelightCells:E,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,className:e.className,clientSize:e.clientSize,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,initialSize:e.initialSize,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onMouseMove:e.onMouseMove,onVisibleRegionChanged:e.onVisibleRegionChanged,overscrollX:e.overscrollX,overscrollY:e.overscrollY,preventDiagonalScrolling:e.preventDiagonalScrolling,rightElement:e.rightElement,rightElementProps:e.rightElementProps,rowHeight:e.rowHeight,rows:e.rows,scrollRef:e.scrollRef,selection:e.selection,showMinimap:e.showMinimap,theme:e.theme,trailingRowType:e.trailingRowType,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,drawCustomCell:e.drawCustomCell,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,scrollToEnd:e.scrollToEnd}),z)},Dn=(0,r.d)("input")({name:"RenameInput",class:"r1kzy40b",vars:{"r1kzy40b-0":[e=>Math.max(16,e.targetHeight-10),"px"]}}),Hn=e=>{const{bounds:t,group:n,onClose:r,canvasBounds:i,onFinish:l}=e,[a,s]=o.useState(n);return o.createElement(ue,{style:{position:"absolute",left:t.x-i.left+1,top:t.y-i.top,width:t.width-2,height:t.height},className:"c1sqdbw3",onClickOutside:r},o.createElement(Dn,{targetHeight:t.height,"data-testid":"group-rename-input",value:a,onBlur:r,onFocus:e=>e.target.setSelectionRange(0,a.length),onChange:e=>s(e.target.value),onKeyDown:e=>{"Enter"===e.key?l(a):"Escape"===e.key&&r()},autoFocus:!0}))},Ln=150;function zn(e,t,n,r,o,i,l,a,s){let c=[];if(void 0!==o&&c.push(...o.map((e=>e[r])).map((n=>function(e,t,n,r){var o,i;const l=r(t);return null!=(i=null==(o=null==l?void 0:l.measure)?void 0:o.call(l,e,t,n))?i:Ln}(e,n,t,s)))),c.length>5&&a){const e=c.reduce(((e,t)=>e+t))/c.length;c=c.filter((t=>t<2*e))}c.push(e.measureText(n.title).width+16+(void 0===n.icon?0:28));const u=Math.max(...c),d=Math.max(Math.ceil(i),Math.min(Math.floor(l),Math.ceil(u)));return{...n,width:d}}function Fn(e,t){if(0===e.length)return!1;let n=!1,r=!1,o=!1,i=!1;const l=e.split("+");if(!function(e,t){if(void 0===e)return!1;if(e.length>1&&e.startsWith("_")){if(Number.parseInt(e.slice(1))!==t.keyCode)return!1}else if(e!==t.key)return!1;return!0}(l.pop(),t))return!1;for(const a of l)switch(a){case"ctrl":n=!0;break;case"shift":r=!0;break;case"alt":o=!0;break;case"meta":i=!0;break;case"primary":bn.value?i=!0:n=!0}return t.altKey===o&&t.ctrlKey===n&&t.shiftKey===r&&t.metaKey===i}function An(e){return e.startsWith('"')&&e.endsWith('"')&&(e=e.slice(1,-1).replace(/""/g,'"')),e}function _n(e){var t,n;(n=t||(t={}))[n.None=0]="None",n[n.inString=1]="inString",n[n.inStringPostQuote=2]="inStringPostQuote";const r=[];let o=[],i=0,l=0;e=e.replace(/\r\n/g,"\n");let a=0;for(const s of e){switch(l){case 0:"\t"===s||"\n"===s?(o.push(e.slice(i,a)),i=a+1,"\n"===s&&(r.push(o),o=[])):'"'===s&&(l=1);break;case 1:'"'===s&&(l=2);break;case 2:'"'===s?l=1:"\t"===s||"\n"===s?(o.push(An(e.slice(i,a))),i=a+1,"\n"===s&&(r.push(o),o=[]),l=0):l=0}a++}return i<e.length&&o.push(An(e.slice(i,e.length))),r.push(o),r}function Vn(e){var t,n;const r=[e],o=[];let i;for(;r.length>0;){const e=r.pop();if(void 0===e)break;e instanceof HTMLTableElement||"TBODY"===e.nodeName?r.push(...[...e.children].reverse()):e instanceof HTMLTableRowElement?(void 0!==i&&o.push(i),i=[],r.push(...[...e.children].reverse())):e instanceof HTMLTableCellElement&&(null==i||i.push(null!=(n=null!=(t=e.innerText)?t:e.textContent)?n:""))}return void 0!==i&&o.push(i),o}function Nn(e){return/[\t\n",]/.test(e)&&(e='"'.concat(e.replace(/"/g,'""'),'"')),e}var Bn=e=>{switch(e){case!0:return"TRUE";case!1:return"FALSE";case xe:return"INDETERMINATE";case we:return"";default:ae(0,"A boolean was formated with invalid type: ".concat(typeof e))}};function Wn(e,t,n,r){var o,i;const l=r[t];if(void 0!==e.span&&e.span[0]!==l)return"";if(void 0!==e.copyData)return Nn(e.copyData);switch(e.kind){case me.Text:case me.Number:return Nn(n?null!=(i=null==(o=e.data)?void 0:o.toString())?i:"":e.displayData);case me.Markdown:case me.RowID:case me.Uri:return Nn(e.data);case me.Image:case me.Bubble:return 0===e.data.length?"":e.data.reduce(((e,t)=>"".concat(Nn(e),",").concat(Nn(t))));case me.Boolean:return Bn(e.data);case me.Loading:return n?"":"#LOADING";case me.Protected:return n?"":"************";case me.Drilldown:return 0===e.data.length?"":e.data.map((e=>e.text)).reduce(((e,t)=>"".concat(Nn(e),",").concat(Nn(t))));case me.Custom:return Nn(e.copyData);default:ae(0,"A cell was passed with an invalid kind: ".concat(e.kind))}}function jn(e,t,n){var r,o,i,l;const a=function(e,t){return e.map((e=>e.map(((e,n)=>Wn(e,n,!1,t))).join("\t"))).join("\n")}(e,t);if(void 0!==(null==(r=window.navigator.clipboard)?void 0:r.write)||void 0!==n){const r=document.createElement("tbody");for(const n of e){const e=document.createElement("tr");for(const[r,o]of n.entries()){const n=document.createElement("td");if(o.kind===me.Uri){const e=document.createElement("a");e.href=o.data,e.innerText=o.data,n.append(e)}else n.innerText=Wn(o,r,!0,t);e.append(n)}r.append(e)}if(void 0!==(null==(o=window.navigator.clipboard)?void 0:o.write))window.navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([a],{type:"text/plain"}),"text/html":new Blob(["<table>".concat(r.outerHTML,"</table>")],{type:"text/html"})})]);else if(void 0!==n&&null!==(null==n?void 0:n.clipboardData))try{n.clipboardData.setData("text/plain",a),n.clipboardData.setData("text/html","<table>".concat(r.outerHTML,"</table>"))}catch(s){null==(i=window.navigator.clipboard)||i.writeText(a)}}else null==(l=window.navigator.clipboard)||l.writeText(a);null==n||n.preventDefault()}function Zn(e){return"string"===typeof e?e:"".concat(e,"px")}var Un=(0,r.d)("div")({name:"Wrapper",class:"wzg2m5k",vars:{"wzg2m5k-0":[e=>e.innerWidth],"wzg2m5k-1":[e=>e.innerHeight]}}),Xn=e=>{const{inWidth:t,inHeight:n,children:r,...i}=e;return o.createElement(Un,{innerHeight:Zn(n),innerWidth:Zn(t),...i},r)};function Yn(e){return!0!==e}var Kn={getAccessibilityString:e=>{var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:"false"},kind:me.Boolean,needsHover:!0,useLabel:!1,needsHoverPosition:!0,measure:()=>50,draw:e=>{var t;return function(e,t,n,r){if(!n&&t===we)return;const{ctx:o,hoverAmount:i,theme:l,rect:a,highlighted:s,hoverX:c,hoverY:u,cell:{contentAlign:d}}=e,{x:h,y:p,width:f,height:g}=a;let m=n?.65+.35*i:.4;t===we&&(m*=i),0!==m&&(o.globalAlpha=m,Pt(o,l,t,h,p,f,g,s,c,u,r,d),o.globalAlpha=1)}(e,e.cell.data,_e(e.cell),null!=(t=e.cell.maxSize)?t:20)},onDelete:e=>({...e,data:!1}),onClick:e=>{var t,n;const{cell:r,posX:o,posY:i,bounds:l,theme:a}=e,{width:s,height:c,x:u,y:d}=l,h=null!=(t=r.maxSize)?t:20,p=Math.floor(l.y+c/2),f=rt(h,c,a.cellVerticalPadding),g=nt(null!=(n=r.contentAlign)?n:"center",u,s,a.cellHorizontalPadding,f),m=tt(g,p,f),v=ot(u+o,d+i,m);if(_e(r)&&v)return{...r,data:Yn(r.data)}},onPaste:(e,t)=>{let n=we;return"true"===e.toLowerCase()?n=!0:"false"===e.toLowerCase()?n=!1:"indeterminate"===e.toLowerCase()&&(n=xe),n===t.data?void 0:{...t,data:n}}};var $n=(0,r.d)("div")({name:"BubblesOverlayEditorStyle",class:"b1bqsp5z"}),Gn=e=>{const{bubbles:t}=e;return o.createElement($n,null,t.map(((e,t)=>o.createElement("div",{key:t,className:"boe-bubble"},e))),o.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},qn={getAccessibilityString:e=>{var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:me.Bubble,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce(((t,n)=>e.measureText(n).width+t+20),0)+2*n.cellHorizontalPadding-4,draw:e=>function(e,t){const{rect:n,theme:r,ctx:o,highlighted:i}=e,{x:l,y:a,width:s,height:c}=n;let u=l+r.cellHorizontalPadding;const d=[];for(const h of t){if(u>l+s)break;const e=kt(h,o,"".concat(r.baseFontStyle," ").concat(r.fontFamily)).width;d.push({x:u,width:e}),u+=e+16+4}o.beginPath();for(const h of d)Ht(o,h.x,a+(c-20)/2,h.width+16,20,10);o.fillStyle=i?r.bgBubbleSelected:r.bgBubble,o.fill();for(const[h,p]of d.entries())o.beginPath(),o.fillStyle=r.textBubble,o.fillText(t[h],p.x+8,a+c/2+St(o,r))}(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return o.createElement(Gn,{bubbles:t.data})},onPaste:()=>{}},Qn=(0,r.d)("div")({name:"DrilldownOverlayEditorStyle",class:"df2kt4a"}),Jn=e=>{const{drilldowns:t}=e;return o.createElement(Qn,null,t.map(((e,t)=>o.createElement("div",{key:t,className:"doe-bubble"},void 0!==e.img&&o.createElement("img",{src:e.img}),o.createElement("div",null,e.text)))))},er={getAccessibilityString:e=>e.data.map((e=>e.text)).join(", "),kind:me.Drilldown,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce(((t,n)=>e.measureText(n.text).width+t+20+(void 0!==n.img?18:0)),0)+2*n.cellHorizontalPadding-4,draw:e=>zt(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return o.createElement(Jn,{drilldowns:t.data})},onPaste:()=>{}},tr=(0,r.d)("div")({name:"ImageOverlayEditorStyle",class:"i1eozt10"}),nr=e=>{const{urls:t,canWrite:n,onEditClick:r,renderImage:i}=e,l=t.filter((e=>""!==e));if(0===l.length)return null;const a=l.length>1;return o.createElement(tr,{"data-testid":"GDG-default-image-overlay-editor"},o.createElement(y.lr,{showArrows:a,showThumbs:!1,swipeable:a,emulateTouch:a,infiniteLoop:a},l.map((e=>{var t;const n=null!=(t=null==i?void 0:i(e))?t:o.createElement("img",{draggable:!1,src:e});return o.createElement("div",{className:"centering-container",key:e},n)}))),n&&r&&o.createElement("button",{className:"edit-icon",onClick:r},o.createElement(it,null)))},rr={getAccessibilityString:e=>e.data.join(", "),kind:me.Image,needsHover:!1,useLabel:!1,needsHoverPosition:!1,draw:e=>{var t;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:4,r=arguments.length>3?arguments[3]:void 0;const{rect:o,col:i,row:l,theme:a,ctx:s,imageLoader:c}=e,{x:u,y:d,height:h,width:p}=o,f=h-2*a.cellVerticalPadding,g=[];let m=0;for(let b=0;b<t.length;b++){const e=t[b];if(0===e.length)continue;const n=c.loadOrGetImage(e,i,l);void 0!==n&&(g[b]=n,m+=n.width*(f/n.height)+4)}if(0===m)return;m-=4;let v=u+a.cellHorizontalPadding;"right"===r?v=Math.floor(u+p-a.cellHorizontalPadding-m):"center"===r&&(v=Math.floor(u+p/2-m/2));for(const b of g){if(void 0===b)continue;const e=b.width*(f/b.height);n>0&&(Ht(s,v,d+a.cellVerticalPadding,e,f,n),s.save(),s.clip()),s.drawImage(b,v,d+a.cellVerticalPadding,e,f),n>0&&s.restore(),v+=e+4}}(e,null!=(t=e.cell.displayData)?t:e.cell.data,e.cell.rounding,e.cell.contentAlign)},measure:(e,t)=>50*t.data.length,onDelete:e=>({...e,data:[]}),provideEditor:()=>e=>{const{value:t,onFinishedEditing:n,imageEditorOverride:r}=e,i=null!=r?r:nr;return o.createElement(i,{urls:t.data,canWrite:t.allowAdd,onCancel:n,onChange:e=>{n({...t,data:[e]})}})},onPaste:(e,t)=>{const n=(e=e.trim()).split(",").map((e=>{try{return new URL(e),e}catch(t){return}})).filter((e=>void 0!==e));if(n.length!==t.data.length||!n.every(((e,n)=>e===t.data[n])))return{...t,data:n}}},or={getAccessibilityString:()=>"",kind:me.Loading,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:()=>120,draw:()=>{},onPaste:()=>{}},ir=(0,r.d)("div")({name:"MarkdownContainer",class:"mlbeo71"}),lr=class extends o.PureComponent{constructor(){super(...arguments),this.targetElement=null,this.containerRefHook=e=>{this.targetElement=e,this.renderMarkdownIntoDiv()}}renderMarkdownIntoDiv(){const{targetElement:e,props:t}=this;if(null===e)return;const{contents:n,createNode:r}=t,o=oe(n),i=document.createRange();i.selectNodeContents(e),i.deleteContents();let l=null==r?void 0:r(o);if(void 0===l){const e=document.createElement("template");e.innerHTML=o,l=e.content}e.append(l);const a=e.getElementsByTagName("a");for(const s of a)s.target="_blank",s.rel="noreferrer noopener"}render(){return this.renderMarkdownIntoDiv(),o.createElement(ir,{ref:this.containerRefHook})}},ar=(0,r.d)("textarea")({name:"InputBox",class:"ijuk0po"}),sr=(0,r.d)("div")({name:"ShadowBox",class:"saq3p5l"}),cr=(0,r.d)("div")({name:"GrowingEntryStyle",class:"gf8vzix"}),ur=0,dr=e=>{const{placeholder:t,value:n,onKeyDown:r,highlight:i,altNewline:l,validatedSelection:a,...s}=e,{onChange:c,className:u}=s,d=o.useRef(null),h=null!=n?n:"";le(void 0!==c,"GrowingEntry must be a controlled input area");const[p]=o.useState((()=>"input-box-"+(ur=(ur+1)%1e7)));o.useEffect((()=>{const e=d.current;if(null===e)return;if(e.disabled)return;const t=h.toString().length;e.focus(),e.setSelectionRange(i?0:t,t)}),[]),o.useLayoutEffect((()=>{var e;if(void 0!==a){const t="number"===typeof a?[a,null]:a;null==(e=d.current)||e.setSelectionRange(t[0],t[1])}}),[a]);const f=o.useCallback((e=>{"Enter"===e.key&&e.shiftKey&&!0===l||null==r||r(e)}),[l,r]);return o.createElement(cr,{className:"gdg-growing-entry"},o.createElement(sr,{className:u},h+"\n"),o.createElement(ar,{...s,className:(null!=u?u:"")+" gdg-input",id:p,ref:d,onKeyDown:f,value:h,placeholder:t,dir:"auto"}))},hr=(0,r.d)("div")({name:"MarkdownOverlayEditorStyle",class:"mdwzdl1",vars:{"mdwzdl1-0":[e=>e.targetWidth,"px"]}}),pr=e=>{const{value:t,onChange:n,forceEditMode:r,createNode:i,targetRect:l,onFinish:a,validatedSelection:s}=e,c=t.data,u=!0===t.readonly,[d,h]=o.useState(""===c||r),p=o.useCallback((()=>{h((e=>!e))}),[]),f=c?"ml-6":"";return d?o.createElement(hr,{targetWidth:l.width-20},o.createElement(dr,{autoFocus:!0,highlight:!1,validatedSelection:s,value:c,onKeyDown:e=>{"Enter"===e.key&&e.stopPropagation()},onChange:n}),o.createElement("div",{className:"edit-icon checkmark-hover ".concat(f),onClick:()=>a(t)},o.createElement(lt,null))):o.createElement(hr,{targetWidth:l.width},o.createElement(lr,{contents:c,createNode:i}),!u&&o.createElement(o.Fragment,null,o.createElement("div",{className:"spacer"}),o.createElement("div",{className:"edit-icon edit-hover ".concat(f),onClick:p},o.createElement(it,null))),o.createElement("textarea",{className:"md-edit-textarea gdg-input",autoFocus:!0}))},fr={getAccessibilityString:e=>{var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:me.Markdown,needsHover:!1,needsHoverPosition:!1,drawPrep:Et,measure:(e,t,n)=>{const r=t.data.split("\n")[0];return e.measureText(r).width+2*n.cellHorizontalPadding},draw:e=>Ot(e,e.cell.data,e.cell.contentAlign),onDelete:e=>({...e,data:""}),provideEditor:()=>e=>{const{onChange:t,value:n,target:r,onFinishedEditing:i,markdownDivCreateNode:l,forceEditMode:a,validatedSelection:s}=e;return o.createElement(pr,{onFinish:i,targetRect:r,value:n,validatedSelection:s,onChange:e=>t({...n,data:e.target.value}),forceEditMode:a,createNode:l})},onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},gr={getAccessibilityString:e=>e.row.toString(),kind:Me.Marker,needsHover:!0,needsHoverPosition:!1,drawPrep:function(e,t){const{ctx:n,theme:r}=e,o="9px ".concat(r.fontFamily),i=null!=t?t:{};return(null==i?void 0:i.font)!==o&&(n.font=o,i.font=o),i.deprep=Dt,n.textAlign="center",i},measure:()=>44,draw:e=>function(e,t,n,r,o){const{ctx:i,rect:l,hoverAmount:a,theme:s}=e,{x:c,y:u,width:d,height:h}=l,p=n?1:"checkbox-visible"===r?.6+.4*a:a;if("number"!==r&&p>0){i.globalAlpha=p;const e=7*(n?a:1);if(Pt(i,s,n,o?c+e:c,u,o?d-e:d,h,!0,void 0,void 0,18),o){i.globalAlpha=a,i.beginPath();for(const e of[3,6])for(const t of[-5,-1,3])i.rect(c+e,u+h/2+t,2,2);i.fillStyle=s.textLight,i.fill(),i.beginPath()}i.globalAlpha=1}if("number"===r||"both"===r&&!n){const e=t.toString(),n="".concat(s.markerFontStyle," ").concat(s.fontFamily),o=c+d/2;"both"===r&&0!==a&&(i.globalAlpha=1-a),i.fillStyle=s.textLight,i.font=n,i.fillText(e,o,u+h/2+St(i,n)),0!==a&&(i.globalAlpha=1)}}(e,e.cell.row,e.cell.checked,e.cell.markerKind,e.cell.drawHandle),onClick:e=>{const{bounds:t,cell:n,posX:r,posY:o}=e,{width:i,height:l}=t,a=n.drawHandle?7+(i-7)/2:i/2,s=l/2;if(Math.abs(r-a)<=10&&Math.abs(o-s)<=10)return{...n,checked:!n.checked}},onPaste:()=>{}},mr={getAccessibilityString:()=>"",kind:Me.NewRow,needsHover:!0,needsHoverPosition:!1,measure:()=>200,draw:e=>function(e,t,n){const{ctx:r,rect:o,hoverAmount:i,theme:l,spriteManager:a}=e,{x:s,y:c,width:u,height:d}=o;r.beginPath(),r.globalAlpha=i,r.rect(s,c,u,d),r.fillStyle=l.bgHeaderHovered,r.fill(),r.globalAlpha=1,r.beginPath();const h=""!==t;let p=0;if(void 0!==n){const e=8,t=d-e,o=s+e/2,u=c+e/2;a.drawSprite(n,"normal",r,o,u,t,l,h?1:i),p=t}else{p=24;const e=12,t=h?e:i*e,n=h?0:(1-i)*e*.5,o=l.cellHorizontalPadding+4;t>0&&(r.moveTo(s+o+n,c+d/2),r.lineTo(s+o+n+t,c+d/2),r.moveTo(s+o+n+.5*t,c+d/2-.5*t),r.lineTo(s+o+n+.5*t,c+d/2+.5*t),r.lineWidth=2,r.strokeStyle=l.bgIconHeader,r.lineCap="round",r.stroke())}r.fillStyle=l.textMedium,r.fillText(t,p+s+l.cellHorizontalPadding+.5,c+d/2+St(r,l)),r.beginPath()}(e,e.cell.hint,e.cell.icon),onPaste:()=>{}},vr=o.lazy((async()=>await n.e(1011).then(n.bind(n,41011)))),br={getAccessibilityString:e=>{var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:me.Number,needsHover:!1,needsHoverPosition:!1,useLabel:!0,drawPrep:Et,draw:e=>Ot(e,e.cell.displayData,e.cell.contentAlign),measure:(e,t)=>e.measureText(t.displayData).width+16,onDelete:e=>({...e,data:void 0}),provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return o.createElement(o.Suspense,{fallback:null},o.createElement(vr,{highlight:t,disabled:!0===r.readonly,value:r.data,fixedDecimals:r.fixedDecimals,allowNegative:r.allowNegative,thousandSeparator:r.thousandSeparator,decimalSeparator:r.decimalSeparator,validatedSelection:i,onChange:e=>{var t;return n({...r,data:Number.isNaN(null!=(t=e.floatValue)?t:0)?0:e.floatValue})}}))},onPaste:(e,t)=>{const n=Number.parseFloat(e);if(!Number.isNaN(n)&&t.data!==n)return{...t,data:n}}},yr={getAccessibilityString:()=>"",measure:()=>108,kind:me.Protected,needsHover:!1,needsHoverPosition:!1,draw:function(e){const{ctx:t,theme:n,rect:r}=e,{x:o,y:i,height:l}=r;t.beginPath();const a=2.5;let s=o+n.cellHorizontalPadding+a;const c=i+l/2,u=Math.cos(et(30))*a,d=Math.sin(et(30))*a;for(let h=0;h<12;h++)t.moveTo(s,c-a),t.lineTo(s,c+a),t.moveTo(s+u,c-d),t.lineTo(s-u,c+d),t.moveTo(s-u,c-d),t.lineTo(s+u,c+d),s+=8;t.lineWidth=1.1,t.lineCap="square",t.strokeStyle=n.textLight,t.stroke()},onPaste:()=>{}},wr={getAccessibilityString:e=>{var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:me.RowID,needsHover:!1,needsHoverPosition:!1,drawPrep:(e,t)=>Et(e,t,e.theme.textLight),draw:e=>Ot(e,e.cell.data,e.cell.contentAlign),measure:(e,t)=>e.measureText(t.data).width+16,provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return o.createElement(dr,{highlight:t,autoFocus:!0!==r.readonly,disabled:!1!==r.readonly,value:r.data,validatedSelection:i,onChange:e=>n({...r,data:e.target.value})})},onPaste:()=>{}},xr={getAccessibilityString:e=>{var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:me.Text,needsHover:!1,needsHoverPosition:!1,drawPrep:Et,useLabel:!0,draw:e=>(Ot(e,e.cell.displayData,e.cell.contentAlign,e.cell.allowWrapping,e.hyperWrapping),!0),measure:(e,t,n)=>{const r=t.displayData.split("\n").slice(0,!0===t.allowWrapping?void 0:1);return Math.max(...r.map((t=>e.measureText(t).width+2*n.cellHorizontalPadding)))},onDelete:e=>({...e,data:""}),provideEditor:e=>({disablePadding:!0===e.allowWrapping,editor:t=>{const{isHighlighted:n,onChange:r,value:i,validatedSelection:l}=t;return o.createElement(dr,{style:!0===e.allowWrapping?{padding:"3px 8.5px"}:void 0,highlight:n,autoFocus:!0!==i.readonly,disabled:!0===i.readonly,altNewline:!0,value:i.data,validatedSelection:l,onChange:e=>r({...i,data:e.target.value})})}}),onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},kr=(0,r.d)("div")({name:"UriOverlayEditorStyle",class:"uf0sjo8"}),Sr=e=>{const{uri:t,onChange:n,forceEditMode:r,readonly:i,validatedSelection:l,preview:a}=e,[s,c]=o.useState(""===t||r),u=o.useCallback((()=>{c(!0)}),[]);return s?o.createElement(dr,{validatedSelection:l,highlight:!0,autoFocus:!0,value:t,onChange:n}):o.createElement(kr,null,o.createElement("a",{className:"link-area",href:t,target:"_blank",rel:"noopener noreferrer"},a),!i&&o.createElement("div",{className:"edit-icon",onClick:u},o.createElement(it,null)),o.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},Cr={getAccessibilityString:e=>{var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:me.Uri,needsHover:!1,needsHoverPosition:!1,useLabel:!0,drawPrep:Et,draw:e=>Ot(e,e.cell.data,e.cell.contentAlign),measure:(e,t,n)=>e.measureText(t.data).width+2*n.cellHorizontalPadding,onDelete:e=>({...e,data:""}),provideEditor:()=>e=>{var t;const{onChange:n,value:r,forceEditMode:i,validatedSelection:l}=e;return o.createElement(Sr,{forceEditMode:i,uri:r.data,preview:null!=(t=r.displayData)?t:r.data,validatedSelection:l,readonly:!0===r.readonly,onChange:e=>n({...r,data:e.target.value})})},onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},Mr={[Me.Marker]:gr,[Me.NewRow]:mr,[me.Boolean]:Kn,[me.Bubble]:qn,[me.Drilldown]:er,[me.Image]:rr,[me.Loading]:or,[me.Markdown]:fr,[me.Number]:br,[me.Protected]:yr,[me.RowID]:wr,[me.Text]:xr,[me.Uri]:Cr},Er=0;function Rr(e,t){return void 0===e||0===t||0===e.columns.length&&void 0===e.current?e:{current:void 0===e.current?void 0:{cell:[e.current.cell[0]+t,e.current.cell[1]],range:{...e.current.range,x:e.current.range.x+t},rangeStack:e.current.rangeStack.map((e=>({...e,x:e.x+t})))},rows:e.rows,columns:e.columns.offset(t)}}var Tr={selectAll:!0,selectRow:!0,selectColumn:!0,downFill:!1,rightFill:!1,pageUp:!1,pageDown:!1,clear:!0,copy:!0,paste:!0,search:!1,first:!0,last:!0},Ir={kind:me.Loading,allowOverlay:!1},Or={columns:Ne.empty(),rows:Ne.empty(),current:void 0},Pr=o.forwardRef(((e,t)=>{var n,r,u,d,h;const[p,f]=o.useState(Or),[g,m]=o.useState(),v=o.useRef(null),b=o.useRef(null),[y,w]=o.useState(),x=o.useRef(null),k=o.useRef(),{rowMarkers:S="none",rowMarkerWidth:C,imageEditorOverride:M,getRowThemeOverride:E,markdownDivCreateNode:R,width:T,height:I,columns:O,rows:P,getCellContent:D,onCellClicked:H,onCellActivated:L,onFinishedEditing:z,coercePasteValue:F,drawHeader:A,onHeaderClicked:_,spanRangeBehavior:V="default",onGroupHeaderClicked:N,onCellContextMenu:B,className:W,onHeaderContextMenu:j,getCellsForSelection:Z,onGroupHeaderContextMenu:U,onGroupHeaderRenamed:X,onCellEdited:Y,onCellsEdited:K,onSearchResultsChanged:$,searchResults:G,onSearchValueChange:q,searchValue:Q,onKeyDown:J,onKeyUp:ee,keybindings:te,onRowAppended:ne,onColumnMoved:re,validateCell:oe,highlightRegions:ie,drawCell:se,rangeSelect:ce="rect",columnSelect:ue="multi",rowSelect:he="multi",rangeSelectionBlending:ge="exclusive",columnSelectionBlending:ve="exclusive",rowSelectionBlending:be="exclusive",onDelete:ye,onDragStart:we,onMouseMove:xe,onPaste:Ee,copyHeaders:Re=!1,freezeColumns:Te=0,rowSelectionMode:_e="auto",rowMarkerStartIndex:Ve=1,rowMarkerTheme:Be,onHeaderMenuClick:We,getGroupDetails:Ze,onSearchClose:Ue,onItemHovered:Xe,onSelectionCleared:Ye,showSearch:Ke,onVisibleRegionChanged:$e,gridSelection:Ge,onGridSelectionChange:Je,minColumnWidth:et=50,maxColumnWidth:tt=500,maxColumnAutoWidth:nt,provideEditor:rt,trailingRowOptions:ot,scrollOffsetX:it,scrollOffsetY:lt,verticalBorder:at,onDragOverCell:st,onDrop:ct,onColumnResize:ut,onColumnResizeEnd:dt,onColumnResizeStart:gt,customRenderers:mt,fillHandle:vt,drawFocusRing:bt,experimental:yt,fixedShadowX:wt,fixedShadowY:xt,headerIcons:kt,imageWindowLoader:St,initialSize:Ct,isDraggable:Mt,onDragLeave:Et,onRowMoved:Rt,overscrollX:Tt,overscrollY:It,preventDiagonalScrolling:Ot,rightElement:Pt,rightElementProps:Dt,showMinimap:Ht,smoothScrollX:Lt,smoothScrollY:zt,scrollToEnd:Ft,scaleToRem:At=!1,rowHeight:_t=34,headerHeight:Vt=36,groupHeaderHeight:Nt=Vt,theme:Bt,isOutsideClick:Wt}=e,jt=Math.max(et,20),Zt=Math.max(tt,jt),Ut=Math.max(null!=nt?nt:Zt,jt),Xt=o.useMemo((()=>"undefined"===typeof window?{fontSize:"16px"}:window.getComputedStyle(document.documentElement)),[]).fontSize,Yt=o.useMemo((()=>Number.parseFloat(Xt)),[Xt]),[Kt,$t,Gt,qt,Qt,Jt]=o.useMemo((()=>{var e,t,n;if(!At||16===Yt)return[_t,Vt,Nt,Bt,Tt,It];const r=Yt/16,o=_t,i=pe();return["number"===typeof o?o*r:e=>Math.ceil(o(e)*r),Math.ceil(Vt*r),Math.ceil(Nt*r),{...Bt,headerIconSize:(null!=(e=null==Bt?void 0:Bt.headerIconSize)?e:i.headerIconSize)*r,cellHorizontalPadding:(null!=(t=null==Bt?void 0:Bt.cellHorizontalPadding)?t:i.cellHorizontalPadding)*r,cellVerticalPadding:(null!=(n=null==Bt?void 0:Bt.cellVerticalPadding)?n:i.cellVerticalPadding)*r},Math.ceil((null!=Tt?Tt:0)*r),Math.ceil((null!=It?It:0)*r)]}),[Nt,Vt,Tt,It,Yt,_t,At,Bt]),en=o.useMemo((()=>void 0===te?Tr:{...Tr,...te}),[te]),tn=null!=C?C:P>1e4?48:P>1e3?44:P>100?36:32,nn="none"!==S,rn=nn?1:0,on=void 0!==ne,ln=!0===(null==ot?void 0:ot.sticky),[an,sn]=o.useState(!1),cn=null!=Ke?Ke:an,un=o.useCallback((()=>{void 0!==Ue?Ue():sn(!1)}),[Ue]),dn=o.useMemo((()=>void 0===Ge?void 0:Rr(Ge,rn)),[Ge,rn]),hn=null!=dn?dn:p,pn=o.useRef(new AbortController);o.useEffect((()=>()=>{null==pn||pn.current.abort()}),[]);const[fn,gn]=function(e,t,n,r,i){const l=o.useCallback((n=>{var o;if(!0===e){const e=[];for(let r=n.y;r<n.y+n.height;r++){const o=[];for(let e=n.x;e<n.x+n.width;e++)e<0||r>=i?o.push({kind:me.Loading,allowOverlay:!1}):o.push(t([e,r]));e.push(o)}return e}return null!=(o=null==e?void 0:e(n,r.signal))?o:[]}),[r.signal,t,e,i]),a=void 0!==e?l:void 0,s=o.useCallback((e=>{if(void 0===a)return[];const t={...e,x:e.x-n};if(t.x<0){t.x=0,t.width--;const e=a(t,r.signal);return"function"===typeof e?async()=>(await e()).map((e=>[{kind:me.Loading,allowOverlay:!1},...e])):e.map((e=>[{kind:me.Loading,allowOverlay:!1},...e]))}return a(t,r.signal)}),[r.signal,a,n]);return[void 0!==e?s:void 0,a]}(Z,D,rn,pn.current,P),mn=o.useCallback(((e,t,n)=>{if(void 0===oe)return!0;const r=[e[0]-rn,e[1]];return null==oe?void 0:oe(r,t,n)}),[rn,oe]),vn=o.useRef(Ge),yn=o.useCallback(((e,t)=>{t&&(e=function(e,t,n,r,o){var i,l;const a=e;if("allowPartial"===r||void 0===e.current)return e;if(void 0!==t){let r=!1;do{if(void 0===(null==e?void 0:e.current))break;const s=null==(i=e.current)?void 0:i.range,c=[];if(s.width>2){const e=t({x:s.x,y:s.y,width:1,height:s.height},o.signal);if("function"===typeof e)return a;c.push(...e);const n=t({x:s.x+s.width-1,y:s.y,width:1,height:s.height},o.signal);if("function"===typeof n)return a;c.push(...n)}else{const e=t({x:s.x,y:s.y,width:s.width,height:s.height},o.signal);if("function"===typeof e)return a;c.push(...e)}let u=s.x-n,d=s.x+s.width-1-n;for(const e of c)for(const t of e)void 0!==t.span&&(u=Math.min(t.span[0],u),d=Math.max(t.span[1],d));u===s.x-n&&d===s.x+s.width-1-n?r=!0:e={current:{cell:null!=(l=e.current.cell)?l:[0,0],range:{x:u+n,y:s.y,width:d-u+1,height:s.height},rangeStack:e.current.rangeStack},columns:e.columns,rows:e.rows}}while(!r)}return e}(e,fn,rn,V,pn.current)),void 0!==Je?(vn.current=Rr(e,-rn),Je(vn.current)):f(e)}),[Je,fn,rn,V]),wn=Qe(ut,o.useCallback(((e,t,n,r)=>{null==ut||ut(O[n-rn],t,n-rn,r)}),[ut,rn,O])),xn=Qe(dt,o.useCallback(((e,t,n,r)=>{null==dt||dt(O[n-rn],t,n-rn,r)}),[dt,rn,O])),kn=Qe(gt,o.useCallback(((e,t,n,r)=>{null==gt||gt(O[n-rn],t,n-rn,r)}),[gt,rn,O])),Sn=Qe(A,o.useCallback((e=>{var t;return null!=(t=null==A?void 0:A({...e,columnIndex:e.columnIndex-rn}))&&t}),[A,rn])),Cn=o.useCallback((e=>{if(void 0!==ye){const t=ye(Rr(e,-rn));return"boolean"===typeof t?t:Rr(t,rn)}return!0}),[ye,rn]),[Mn,En,Rn]=function(e,t,n,r,i,l){return[o.useCallback(((o,a,s,c)=>{var u,d;"cell"!==l&&"multi-cell"!==l||void 0===o||(o={...o,range:{x:o.cell[0],y:o.cell[1],width:1,height:1}});const h="mixed"===n&&(s||"drag"===c),p="mixed"===r&&h,f="mixed"===i&&h;let g={current:void 0===o?void 0:{...o,rangeStack:"drag"===c&&null!=(d=null==(u=e.current)?void 0:u.rangeStack)?d:[]},columns:p?e.columns:Ne.empty(),rows:f?e.rows:Ne.empty()};s&&("multi-rect"===l||"multi-cell"===l)&&void 0!==g.current&&void 0!==e.current&&(g={...g,current:{...g.current,rangeStack:[...e.current.rangeStack,e.current.range]}}),t(g,a)}),[r,e,n,l,i,t]),o.useCallback(((o,l,a)=>{let s;if(o=null!=o?o:e.rows,void 0!==l&&(o=o.add(l)),"exclusive"===i&&o.length>0)s={current:void 0,columns:Ne.empty(),rows:o};else{const t=a&&"mixed"===r;s={current:a&&"mixed"===n?e.current:void 0,columns:t?e.columns:Ne.empty(),rows:o}}t(s,!1)}),[r,e,n,i,t]),o.useCallback(((o,l,a)=>{let s;if(o=null!=o?o:e.columns,void 0!==l&&(o=o.add(l)),"exclusive"===r&&o.length>0)s={current:void 0,rows:Ne.empty(),columns:o};else{const t=a&&"mixed"===i;s={current:a&&"mixed"===n?e.current:void 0,rows:t?e.rows:Ne.empty(),columns:o}}t(s,!1)}),[r,e,n,i,t])]}(hn,yn,ge,ve,be,ce),Tn=o.useMemo((()=>({...pe(),...qt})),[qt]),[In,On]=o.useState([10,10,0]),Dn=o.useCallback((e=>e.kind!==me.Custom?Mr[e.kind]:null==mt?void 0:mt.find((t=>t.isMatch(e)))),[mt]),An=function(e,t,n,r,i,l,a,s,c){const u=o.useRef(t),d=o.useRef(n),h=o.useRef(a);u.current=t,d.current=n,h.current=a;const[p,f]=o.useMemo((()=>{if("undefined"===typeof window)return[null,null];const e=document.createElement("canvas");return e.style.display="none",e.style.opacity="0",e.style.position="fixed",[e,e.getContext("2d",{alpha:!1})]}),[]);o.useLayoutEffect((()=>(p&&document.documentElement.append(p),()=>{null==p||p.remove()})),[p]);const g=o.useRef({}),m=o.useRef(),[v,b]=o.useState();return o.useLayoutEffect((()=>{const t=d.current;if(void 0===t||e.every(De))return;let n=Math.max(1,10-Math.floor(e.length/1e4)),r=0;n<u.current&&n>1&&(n--,r=1);const o={x:0,y:0,width:e.length,height:Math.min(u.current,n)},i={x:0,y:u.current-1,width:e.length,height:1};(async()=>{const n=t(o,c.signal),l=r>0?t(i,c.signal):void 0;let a;a="object"===typeof n?n:await He(n),void 0!==l&&(a="object"===typeof l?[...a,...l]:[...a,...await He(l)]),m.current=e,b(a)})()}),[c.signal,e]),o.useMemo((()=>{var t;let n=e.every(De)?e:null===f?e.map((e=>De(e)?e:{...e,width:Ln})):(f.font="".concat(h.current.baseFontStyle," ").concat(h.current.fontFamily),e.map(((t,n)=>{if(De(t))return t;if(void 0!==g.current[t.id])return{...t,width:g.current[t.id]};if(void 0===v||m.current!==e||void 0===t.id)return{...t,width:Ln};const r=zn(f,a,t,n,v,i,l,!0,s);return g.current[t.id]=r.width,r}))),o=0,c=0;const u=[];for(const[e,r]of n.entries())o+=r.width,void 0!==r.grow&&r.grow>0&&(c+=r.grow,u.push(e));if(o<r&&u.length>0){const e=[...n],i=r-o;let l=i;for(let r=0;r<u.length;r++){const o=u[r],a=(null!=(t=n[o].grow)?t:0)/c,s=r===u.length-1?l:Math.min(l,Math.floor(i*a));e[o]={...n[o],growOffset:s,width:n[o].width+s},l-=s}n=e}return n}),[r,e,f,v,a,i,l,s])}(O,P,gn,In[0]-(0===rn?0:tn)-In[2],jt,Ut,Tn,Dn,pn.current),Nn=o.useMemo((()=>An.some((e=>void 0!==e.group))),[An]),Bn=Nn?$t+Gt:$t,Wn=hn.rows.length,Zn="none"===S?"":0===Wn?Oe:Wn===P?Ie:Pe,Un=o.useMemo((()=>"none"===S?An:[{title:Zn,width:tn,icon:void 0,hasMenu:!1,style:"normal",themeOverride:Be},...An]),[An,tn,S,Zn,Be]),[Kn,$n]=o.useMemo((()=>[void 0!==lt&&"number"===typeof Kt?Math.floor(lt/Kt):0,void 0!==lt&&"number"===typeof Kt?-lt%Kt:0]),[lt,Kt]),Gn=o.useRef({height:1,width:1,x:0,y:0}),qn=o.useMemo((()=>{var e,t;return{x:Gn.current.x,y:Kn,width:null!=(e=Gn.current.width)?e:1,height:null!=(t=Gn.current.height)?t:1,ty:$n}}),[$n,Kn]),Qn=o.useRef(!1),[Jn,er,tr]=function(e){const t=o.useRef([pt,e]);t.current[1]!==e&&(t.current[0]=e),t.current[1]=e;const[n,r]=o.useState(e),[,i]=o.useState(),l=o.useCallback((e=>{const n=t.current[0];n!==pt&&(e="function"===typeof e?e(n):e)===n||(n!==pt&&i({}),r((t=>"function"===typeof e?e(n===pt?t:n):e)),t.current[0]=pt)}),[]),a=o.useCallback((()=>{t.current[0]=pt,i({})}),[]);return[t.current[0]===pt?n:t.current[0],l,a]}(qn),nr=(null!=(n=Jn.height)?n:1)>1;o.useLayoutEffect((()=>{if(void 0!==lt&&null!==x.current&&nr){if(x.current.scrollTop===lt)return;x.current.scrollTop=lt,x.current.scrollTop!==lt&&tr(),Qn.current=!0}}),[lt,nr,tr]);const rr=(null!=(r=Jn.width)?r:1)>1;o.useLayoutEffect((()=>{if(void 0!==it&&null!==x.current&&rr){if(x.current.scrollLeft===it)return;x.current.scrollLeft=it,x.current.scrollLeft!==it&&tr(),Qn.current=!0}}),[it,rr,tr]);const or=Jn.x+rn,ir=Jn.y,lr=o.useRef(null),ar=o.useCallback((e=>{var t;!0===e?null==(t=lr.current)||t.focus():window.requestAnimationFrame((()=>{var e;null==(e=lr.current)||e.focus()}))}),[]),sr=on?P+1:P,cr=o.useCallback((e=>{const t=0===rn?e:e.map((e=>({...e,location:[e.location[0]-rn,e.location[1]]}))),n=null==K?void 0:K(t);if(!0!==n)for(const r of t)null==Y||Y(r.location,r.value);return n}),[Y,K,rn]),ur=o.useMemo((()=>{if(void 0!==ie)return 0===rn?ie:ie.map((e=>{const t=Un.length-e.range.x-rn;if(!(t<=0))return{color:e.color,range:{...e.range,x:e.range.x+rn,width:Math.min(t,e.range.width)},style:e.style}})).filter((e=>void 0!==e))}),[ie,Un.length,rn]),dr=o.useRef(Un);dr.current=Un;const hr=o.useCallback((function(e){let[t,n]=e,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var o,i,l,a,s,c,u,d,h,p;const f=on&&n===sr-1;if(0===t&&nn)return f?Ir:{kind:Me.Marker,allowOverlay:!1,checked:!0===(null==hn?void 0:hn.rows.hasIndex(n)),markerKind:"clickable-number"===S?"number":S,row:Ve+n,drawHandle:void 0!==Rt,cursor:"clickable-number"===S?"pointer":void 0};if(f){const e=t===rn&&null!=(o=null==ot?void 0:ot.hint)?o:"",n=dr.current[t];if(!0===(null==(i=null==n?void 0:n.trailingRowOptions)?void 0:i.disabled))return Ir;{const t=null!=(a=null==(l=null==n?void 0:n.trailingRowOptions)?void 0:l.hint)?a:e,r=null!=(c=null==(s=null==n?void 0:n.trailingRowOptions)?void 0:s.addIcon)?c:null==ot?void 0:ot.addIcon;return{kind:Me.NewRow,hint:t,allowOverlay:!1,icon:r}}}{const e=t-rn;if(r||!0===(null==yt?void 0:yt.strict)){const t=Gn.current,r=t.x>e||e>t.x+t.width||t.y>n||n>t.y+t.height,o=e===(null==(d=null==(u=t.extras)?void 0:u.selected)?void 0:d[0])&&n===(null==(h=t.extras)?void 0:h.selected[1]),i=void 0===(null==(p=t.extras)?void 0:p.freezeRegion)||t.extras.freezeRegion.x>e||e>t.extras.freezeRegion.x+t.extras.freezeRegion.width||t.extras.freezeRegion.y>n||n>t.extras.freezeRegion.y+t.extras.freezeRegion.height;if(r&&!o&&i)return{kind:me.Loading,allowOverlay:!1}}let o=D([e,n]);return 0!==rn&&void 0!==o.span&&(o={...o,span:[o.span[0]+rn,o.span[1]+rn]}),o}}),[on,sr,nn,null==hn?void 0:hn.rows,Rt,S,rn,null==ot?void 0:ot.hint,null==ot?void 0:ot.addIcon,null==yt?void 0:yt.strict,D,Ve]),pr=o.useCallback((e=>{var t,n;let r=null!=(t=null==Ze?void 0:Ze(e))?t:{name:e};return void 0!==X&&""!==e&&(r={icon:r.icon,name:r.name,overrideTheme:r.overrideTheme,actions:[...null!=(n=r.actions)?n:[],{title:"Rename",icon:"renameIcon",onClick:e=>Vr({group:r.name,bounds:e.bounds})}]}),r}),[Ze,X]),fr=o.useCallback((e=>{var t;const[n,r]=e.cell,o=Un[n],i=void 0!==(null==o?void 0:o.group)?null==(t=pr(o.group))?void 0:t.overrideTheme:void 0,l=null==o?void 0:o.themeOverride,a=null==E?void 0:E(r);m({...e,theme:{...Tn,...i,...l,...a,...e.content.themeOverride}})}),[E,Un,pr,Tn]),gr=o.useCallback(((e,t,n)=>{var r;if(void 0===hn.current)return;const[o,i]=hn.current.cell,l=hr([o,i]);if(l.kind!==me.Boolean&&l.allowOverlay){let t=l;if(void 0!==n)switch(t.kind){case me.Number:{const e=function(e,t){try{return e()}catch(n){return t}}((()=>"-"===n?-0:Number.parseFloat(n)),0);t={...t,data:Number.isNaN(e)?0:e};break}case me.Text:case me.Markdown:case me.Uri:t={...t,data:n}}fr({target:e,content:t,initialValue:n,cell:[o,i],highlight:void 0===n,forceEditMode:void 0!==n})}else l.kind===me.Boolean&&t&&!0!==l.readonly&&(cr([{location:hn.current.cell,value:{...l,data:Yn(l.data)}}]),null==(r=lr.current)||r.damage([{cell:hn.current.cell}]))}),[hr,hn,cr,fr]),mr=o.useCallback(((e,t)=>{var n;const r=null==(n=lr.current)?void 0:n.getBounds(e,t);if(void 0===r||null===x.current)return;const o=hr([e,t]);o.allowOverlay&&fr({target:r,content:o,initialValue:void 0,highlight:!0,cell:[e,t],forceEditMode:!0})}),[hr,fr]),vr=o.useCallback((function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"both",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;var l;if(null!==x.current){const a=lr.current,s=b.current,c="number"!==typeof e?"cell"===e.unit?e.amount:void 0:e,u="number"!==typeof t?"cell"===t.unit?t.amount:void 0:t,d="number"!==typeof e&&"px"===e.unit?e.amount:void 0,h="number"!==typeof t&&"px"===t.unit?t.amount:void 0;if(null!==a&&null!==s){let t={x:0,y:0,width:0,height:0},p=0,f=0;if((void 0!==c||void 0!==u)&&(t=null!=(l=a.getBounds((null!=c?c:0)+rn,null!=u?u:0))?l:t,0===t.width||0===t.height))return;const g=s.getBoundingClientRect(),m=g.width/s.offsetWidth;if(void 0!==d&&(t={...t,x:d-g.left-x.current.scrollLeft,width:1}),void 0!==h&&(t={...t,y:h+g.top-x.current.scrollTop,height:1}),void 0!==t){const l={x:t.x-r,y:t.y-o,width:t.width+2*r,height:t.height+2*o};let a=0;for(let e=0;e<Te;e++)a+=An[e].width;let s=0;ln&&(s="number"===typeof Kt?Kt:Kt(P));let c=a*m+g.left+rn*tn*m,u=g.right,d=g.top+Bn*m,h=g.bottom-s*m;const v=t.width+2*r;switch(null==i?void 0:i.hAlign){case"start":u=c+v;break;case"end":c=u-v;break;case"center":c=Math.floor((c+u)/2)-v/2,u=c+v}const b=t.height+2*o;switch(null==i?void 0:i.vAlign){case"start":h=d+b;break;case"end":d=h-b;break;case"center":d=Math.floor((d+h)/2)-b/2,h=d+b}c>l.x?p=l.x-c:u<l.x+l.width&&(p=l.x+l.width-u),d>l.y?f=l.y-d:h<l.y+l.height&&(f=l.y+l.height-h),"vertical"===n||"number"===typeof e&&e<Te?p=0:"horizontal"===n&&(f=0),0===p&&0===f||(1!==m&&(p/=m,f/=m),x.current.scrollTo(p+x.current.scrollLeft,f+x.current.scrollTop))}}}}),[rn,tn,Bn,ln,Te,An,Kt,P]),br=o.useRef(mr),yr=o.useRef(D),wr=o.useRef(P);br.current=mr,yr.current=D,wr.current=P;const xr=o.useCallback((async function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var n;const r=Un[e];if(!0===(null==(n=null==r?void 0:r.trailingRowOptions)?void 0:n.disabled))return;const o=null==ne?void 0:ne();let i,l=!0;void 0!==o&&(i=await o,"top"===i&&(l=!1),"number"===typeof i&&(l=!1));let a=0;const s=()=>{if(wr.current<=P)return a<500&&window.setTimeout(s,a),void(a=50+2*a);const n="number"===typeof i?i:l?P:0;vr(e-rn,n),Mn({cell:[e,n],range:{x:e,y:n,width:1,height:1}},!1,!1,"edit");const r=yr.current([e-rn,n]);r.allowOverlay&&Fe(r)&&!0!==r.readonly&&t&&window.setTimeout((()=>{br.current(e,n)}),0)};s()}),[Un,ne,rn,P,vr,Mn]),kr=o.useCallback((e=>{var t,n,r;const o=null!=(r=null==(n=null==(t=An[e])?void 0:t.trailingRowOptions)?void 0:n.targetColumn)?r:null==ot?void 0:ot.targetColumn;if("number"===typeof o){return o+(nn?1:0)}if("object"===typeof o){const e=O.indexOf(o);if(e>=0){return e+(nn?1:0)}}}),[An,O,nn,null==ot?void 0:ot.targetColumn]),Sr=o.useRef(),Cr=o.useRef(),Pr=o.useCallback(((e,t)=>{var n;const[r,o]=t;return{...Tn,...null==(n=Un[r])?void 0:n.themeOverride,...null==E?void 0:E(o),...e.themeOverride}}),[E,Un,Tn]),Dr=o.useCallback((e=>{var t,n,r,o;const i=bn.value?e.metaKey:e.ctrlKey,l=i&&"multi"===he,a=i&&"multi"===ue,[s,c]=e.location,u=hn.columns,d=hn.rows,[h,p]=null!=(n=null==(t=hn.current)?void 0:t.cell)?n:[];if("cell"===e.kind){if(Cr.current=void 0,Lr.current=[s,c],0===s&&nn){if(!0===on&&c===P||"number"===S||"none"===he)return;const t=hr(e.location);if(t.kind!==Me.Marker)return;if(void 0!==Rt){const n=Dn(t);le((null==n?void 0:n.kind)===Me.Marker);const o=null==(r=null==n?void 0:n.onClick)?void 0:r.call(n,{...e,cell:t,posX:e.localEventX,posY:e.localEventY,bounds:e.bounds,theme:Pr(t,e.location),preventDefault:()=>{}});if(void 0===o||o.checked===t.checked)return}m(void 0),ar();const n=d.hasIndex(c),o=Sr.current;if("multi"===he&&(e.shiftKey||!0===e.isLongTouch)&&void 0!==o&&d.hasIndex(o)){const e=[Math.min(o,c),Math.max(o,c)+1];l||"multi"===_e?En(void 0,e,!0):En(Ne.fromSingleSelection(e),void 0,l)}else l||e.isTouch||"multi"===_e?n?En(d.remove(c),void 0,!0):(En(void 0,c,!0),Sr.current=c):n&&1===d.length?En(Ne.empty(),void 0,i):(En(Ne.fromSingleSelection(c),void 0,i),Sr.current=c)}else if(s>=rn&&on&&c===P){const e=kr(s);xr(null!=e?e:s)}else if(h!==s||p!==c){const t=hr(e.location),n=Dn(t);if(void 0!==(null==n?void 0:n.onSelect)){let r=!1;if(n.onSelect({...e,cell:t,posX:e.localEventX,posY:e.localEventY,bounds:e.bounds,preventDefault:()=>r=!0,theme:Pr(t,e.location)}),r)return}const r=ln&&c===P,l=ln&&void 0!==hn&&(null==(o=hn.current)?void 0:o.cell[1])===P;if(!e.shiftKey&&!0!==e.isLongTouch||void 0===h||void 0===p||void 0===hn.current||l)Mn({cell:[s,c],range:{x:s,y:c,width:1,height:1}},!0,i,"click"),Sr.current=void 0,m(void 0),ar();else{if(r)return;const e=Math.min(s,h),t=Math.max(s,h),n=Math.min(c,p),o=Math.max(c,p);Mn({...hn.current,range:{x:e,y:n,width:t-e+1,height:o-n+1}},!0,i,"click"),Sr.current=void 0,ar()}}}else if("header"===e.kind)if(Lr.current=[s,c],m(void 0),nn&&0===s)Sr.current=void 0,Cr.current=void 0,"multi"===he&&(d.length!==P?En(Ne.fromSingleSelection([0,P]),void 0,i):En(Ne.empty(),void 0,i),ar());else{const t=Cr.current;if("multi"===ue&&(e.shiftKey||!0===e.isLongTouch)&&void 0!==t&&u.hasIndex(t)){const e=[Math.min(t,s),Math.max(t,s)+1];a?Rn(void 0,e,i):Rn(Ne.fromSingleSelection(e),void 0,i)}else a?(u.hasIndex(s)?Rn(u.remove(s),void 0,i):Rn(void 0,s,i),Cr.current=s):"none"!==ue&&(Rn(Ne.fromSingleSelection(s),void 0,i),Cr.current=s);Sr.current=void 0,ar()}else e.kind===Se?Lr.current=[s,c]:e.kind===Ce&&(yn(Or,!1),m(void 0),ar(),null==Ye||Ye(),Sr.current=void 0,Cr.current=void 0)}),[xr,ue,ar,Dn,kr,hr,hn,nn,ln,Ye,Rt,rn,S,he,_e,P,Mn,yn,Rn,En,on,Pr]),Hr=o.useRef(!1),Lr=o.useRef(),zr=o.useRef(Jn),Fr=o.useRef(),Ar=o.useCallback((e=>{var t,n;if(Wr.current=!1,zr.current=Gn.current,0!==e.button&&1!==e.button)return void(Fr.current=void 0);const r=performance.now(),o=r-(null!=(n=null==(t=Fr.current)?void 0:t.time)?n:-1e3)<250;Fr.current={wasDoubleClick:o,button:e.button,time:r,location:e.location},"header"===(null==e?void 0:e.kind)&&(Hr.current=!0);const i="cell"===e.kind&&e.isFillHandle;!i&&"cell"!==e.kind&&e.isEdge||(w({previousSelection:hn,fillHandle:i}),Lr.current=void 0,e.isTouch||0!==e.button?e.isTouch||1!==e.button||(Lr.current=e.location):Dr(e))}),[hn,Dr]),[_r,Vr]=o.useState(),Nr=o.useCallback((e=>{if(e.kind!==Se||"multi"!==ue)return;const t=bn.value?e.metaKey:e.ctrlKey,[n]=e.location,r=hn.columns;if(n<rn)return;const o=Un[n];let i=n,l=n;for(let a=n-1;a>=rn&&ft(o.group,Un[a].group);a--)i--;for(let a=n+1;a<Un.length&&ft(o.group,Un[a].group);a++)l++;if(ar(),t)if(r.hasAll([i,l+1])){let e=r;for(let t=i;t<=l;t++)e=e.remove(t);Rn(e,void 0,t)}else Rn(void 0,[i,l+1],t);else Rn(Ne.fromSingleSelection([i,l+1]),void 0,t)}),[ue,ar,hn.columns,Un,rn,Rn]),Br=o.useCallback((e=>{var t;if(void 0===hn.current)return;const n=[],r=hn.current.range;for(let o=0;o<r.width;o++){const t=o+r.x,i=hr([t,e?r.y+r.height-1:r.y]);if(!ze(i)&&Fe(i))for(let o=1;o<r.height;o++){const l=[t,e?r.y+r.height-(o+1):o+r.y];n.push({location:l,value:{...i}})}}cr(n),null==(t=lr.current)||t.damage(n.map((e=>({cell:e.location}))))}),[hr,hn,cr]),Wr=o.useRef(!1),jr=o.useCallback((async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var n;if((!0===(null==(n=Fr.current)?void 0:n.wasDoubleClick)||t)&&void 0!==fn&&void 0!==wn){const t=Gn.current.y,n=Gn.current.height;let r=fn({x:e,y:t,width:1,height:Math.min(n,P-t)},pn.current.signal);"object"!==typeof r&&(r=await r());const o=An[e-rn],i=document.createElement("canvas").getContext("2d",{alpha:!1});if(null!==i){i.font="".concat(Tn.baseFontStyle," ").concat(Tn.fontFamily);const t=zn(i,Tn,o,0,r,jt,Zt,!1,Dn);null==wn||wn(o,t.width,e,t.width)}}}),[An,fn,Zt,Tn,jt,wn,rn,P,Dn]),[Zr,Ur]=o.useState(),Xr=o.useCallback(((e,t)=>{var n,r,o;const i=y;if(w(void 0),Ur(void 0),Hr.current=!1,t)return;if(!0===(null==i?void 0:i.fillHandle)&&void 0!==hn.current)return void Br(hn.current.cell[1]!==hn.current.range.y);const[l,a]=e.location,[s,c]=null!=(n=Lr.current)?n:[],u=()=>{Wr.current=!0},d=t=>{var n,r,o;if((t.isTouch||s===l&&c===a)&&(null==H||H([l-rn,a],{...t,preventDefault:u})),1===t.button)return!Wr.current;if(!Wr.current){const s=hr(e.location),c=Dn(s);if(void 0!==c&&void 0!==c.onClick){const r=c.onClick({...t,cell:s,posX:t.localEventX,posY:t.localEventY,bounds:t.bounds,theme:Pr(s,e.location),preventDefault:u});void 0!==r&&!ze(r)&&Le(r)&&(cr([{location:t.location,value:r}]),null==(n=lr.current)||n.damage([{cell:t.location}]))}if(!Wr.current&&void 0!==(null==(o=null==(r=null==i?void 0:i.previousSelection)?void 0:r.current)?void 0:o.cell)&&void 0!==hn.current){const[e,n]=hn.current.cell,[r,o]=i.previousSelection.current.cell;if(l===e&&l===r&&a===n&&a===o)return null==L||L([l-rn,a]),gr(t.bounds,!1),!0}}return!1},h=e.location[0]-rn;if(e.isTouch){const t=Gn.current,n=zr.current;if(t.x!==n.x||t.y!==n.y)return;if(!0===e.isLongTouch){if("cell"===e.kind&&(null==(r=null==hn?void 0:hn.current)?void 0:r.cell[0])===l&&(null==(o=null==hn?void 0:hn.current)?void 0:o.cell[1])===a)return void(null==B||B([h,e.location[1]],{...e,preventDefault:u}));if("header"===e.kind&&hn.columns.hasIndex(l))return void(null==j||j(h,{...e,preventDefault:u}));if(e.kind===Se){if(h<0)return;return void(null==U||U(h,{...e,preventDefault:u}))}}"cell"===e.kind?d(e)||Dr(e):e.kind===Se?null==N||N(h,{...e,preventDefault:u}):(e.kind===ke&&(null==_||_(h,{...e,preventDefault:u})),Dr(e))}else{if("header"===e.kind){if(h<0)return;e.isEdge?jr(l):0===e.button&&l===s&&a===c&&(null==_||_(h,{...e,preventDefault:u}))}if(e.kind===Se){if(h<0)return;0===e.button&&l===s&&a===c&&(null==N||N(h,{...e,preventDefault:u}),Wr.current||Nr(e))}"cell"!==e.kind||0!==e.button&&1!==e.button||d(e),Lr.current=void 0}}),[y,rn,hn,H,Br,hr,Dn,Pr,cr,L,gr,B,j,U,Dr,N,jr,_,Nr]),Yr=o.useCallback((e=>{const t={...e,location:[e.location[0]-rn,e.location[1]]};null==xe||xe(t),Ur((t=>{var n,r;return Hr.current?[e.scrollEdge[0],0]:e.scrollEdge[0]===(null==t?void 0:t[0])&&e.scrollEdge[1]===t[1]?t:void 0===y||(null!=(r=null==(n=Fr.current)?void 0:n.location[0])?r:0)<rn?void 0:e.scrollEdge}))}),[y,xe,rn]);!function(e,t){const n=o.useRef(0),[r,i]=null!=e?e:[0,0];o.useEffect((()=>{if(0===r&&0===i)return void(n.current=0);let e=0;const o=a=>{var s;if(0===e)e=a;else{const o=a-e;n.current=Math.min(1,n.current+o/1300);const l=n.current**1.618*o*2;null==(s=t.current)||s.scrollBy(r*l,i*l),e=a}l=window.requestAnimationFrame(o)};let l=window.requestAnimationFrame(o);return()=>window.cancelAnimationFrame(l)}),[t,r,i])}(Zr,x);const Kr=o.useCallback(((e,t)=>{null==We||We(e-rn,t)}),[We,rn]),$r=null==(u=null==hn?void 0:hn.current)?void 0:u.cell,Gr=o.useCallback(((e,t,n,r,o,i)=>{Qn.current=!1;let l=$r;void 0!==l&&(l=[l[0]-rn,l[1]]);const a={x:e.x-rn,y:e.y,width:e.width,height:on&&e.y+e.height>=P?e.height-1:e.height,tx:o,ty:i,extras:{selected:l,freezeRegion:0===Te?void 0:{x:0,y:e.y,width:Te,height:e.height}}};Gn.current=a,er(a),On([t,n,r]),null==$e||$e(a,a.tx,a.ty,a.extras)}),[$r,rn,on,P,Te,er,$e]),qr=Qe(re,o.useCallback(((e,t)=>{null==re||re(e-rn,t-rn),"none"!==ue&&Rn(Ne.fromSingleSelection(t),void 0,!0)}),[ue,re,rn,Rn])),Qr=o.useRef(!1),Jr=o.useCallback((e=>{0===e.location[0]&&rn>0?e.preventDefault():(null==we||we({...e,location:[e.location[0]-rn,e.location[1]]}),e.defaultPrevented()||(Qr.current=!0),w(void 0))}),[we,rn]),eo=o.useCallback((()=>{Qr.current=!1}),[]),to=o.useCallback((e=>{var t,n;if(!(void 0!==(null==(t=null==Fr?void 0:Fr.current)?void 0:t.button)&&Fr.current.button>=1)){if(void 0!==y&&0===(null==(n=Fr.current)?void 0:n.location[0])&&0===e.location[0]&&1===rn&&"multi"===he&&y.previousSelection&&!y.previousSelection.rows.hasIndex(Fr.current.location[1])&&hn.rows.hasIndex(Fr.current.location[1])){const t=Math.min(Fr.current.location[1],e.location[1]),n=Math.max(Fr.current.location[1],e.location[1])+1;En(Ne.fromSingleSelection([t,n]),void 0,!1)}if(void 0!==y&&void 0!==hn.current&&!Qr.current&&("rect"===ce||"multi-rect"===ce)){const[t,n]=hn.current.cell;let[r,o]=e.location;o<0&&(o=Gn.current.y);if(ln&&n===P)return;if(ln&&o===P){if(e.kind!==Ce)return;o--}r=Math.max(r,rn);const i=r-t,l=o-n,a={x:i>=0?t:r,y:l>=0?n:o,width:Math.abs(i)+1,height:Math.abs(l)+1};Mn({...hn.current,range:a},!0,!1,"drag")}null==Xe||Xe({...e,location:[e.location[0]-rn,e.location[1]]})}}),[y,rn,he,hn,ce,Xe,En,ln,P,Mn]),no=o.useCallback((e=>{if(void 0===hn.current)return;const[t,n]=e,[r,o]=hn.current.cell,i=hn.current.range;let c=i.x,u=i.x+i.width,d=i.y,h=i.y+i.height;if(0!==n)switch(n){case 2:h=P,d=o,vr(0,h,"vertical");break;case-2:d=0,h=o+1,vr(0,d,"vertical");break;case 1:d<o?(d++,vr(0,d,"vertical")):(h=Math.min(P,h+1),vr(0,h,"vertical"));break;case-1:h>o+1?(h--,vr(0,h,"vertical")):(d=Math.max(0,d-1),vr(0,d,"vertical"));break;default:ae()}if(0!==t)if(2===t)u=Un.length,c=r,vr(u-1-rn,0,"horizontal");else if(-2===t)c=rn,u=r+1,vr(c-rn,0,"horizontal");else{let e=[];if(void 0!==fn){const t=fn({x:c,y:d,width:u-c-rn,height:h-d},pn.current.signal);"object"===typeof t&&(e=function(e){return l(a(a(e).filter((e=>void 0!==e.span)).map((e=>{var t,n,r,o;return s((null!=(n=null==(t=e.span)?void 0:t[0])?n:0)+1,(null!=(o=null==(r=e.span)?void 0:r[1])?o:0)+1)}))))}(t))}if(1===t){let t=!1;if(c<r){if(e.length>0){const n=s(c+1,r+1).find((t=>!e.includes(t-rn)));void 0!==n&&(c=n,t=!0)}else c++,t=!0;t&&vr(c,0,"horizontal")}t||(u=Math.min(Un.length,u+1),vr(u-1-rn,0,"horizontal"))}else if(-1===t){let t=!1;if(u>r+1){if(e.length>0){const n=s(u-1,r,-1).find((t=>!e.includes(t-rn)));void 0!==n&&(u=n,t=!0)}else u--,t=!0;t&&vr(u-rn,0,"horizontal")}t||(c=Math.max(rn,c-1),vr(c-rn,0,"horizontal"))}else ae()}Mn({cell:hn.current.cell,range:{x:c,y:d,width:u-c,height:h-d}},!0,!1,"keyboard-select")}),[fn,hn,Un.length,rn,P,vr,Mn]),ro=o.useCallback(((e,t,n,r)=>{const o=sr-(n?0:1);if(e=i(e,rn,An.length-1+rn),t=i(t,0,o),e===(null==$r?void 0:$r[0])&&t===(null==$r?void 0:$r[1]))return!1;if(r&&void 0!==hn.current){const n=[...hn.current.rangeStack];(hn.current.range.width>1||hn.current.range.height>1)&&n.push(hn.current.range),yn({...hn,current:{cell:[e,t],range:{x:e,y:t,width:1,height:1},rangeStack:n}},!0)}else Mn({cell:[e,t],range:{x:e,y:t,width:1,height:1}},!0,!1,"keyboard-nav");return void 0!==k.current&&k.current[0]===e&&k.current[1]===t&&(k.current=void 0),vr(e-rn,t),!0}),[sr,rn,An.length,$r,hn,vr,yn,Mn]),oo=o.useCallback(((e,t)=>{void 0!==(null==g?void 0:g.cell)&&void 0!==e&&Le(e)&&(cr([{location:g.cell,value:e}]),window.requestAnimationFrame((()=>{var e;null==(e=lr.current)||e.damage([{cell:g.cell}])}))),ar(!0),m(void 0);const[n,r]=t;if(void 0!==hn.current&&(0!==n||0!==r)){const t=hn.current.cell[1]===sr-1&&void 0!==e;ro(i(hn.current.cell[0]+n,0,Un.length-1),i(hn.current.cell[1]+r,0,sr-1),t,!1)}null==z||z(e,t)}),[null==g?void 0:g.cell,ar,hn,z,cr,sr,ro,Un.length]),io=o.useMemo((()=>"gdg-overlay-".concat(Er++)),[]),lo=o.useCallback((e=>{(async()=>{var t,n,r,o,i;let l=!1;if(void 0!==J&&J({...e,cancel:()=>{l=!0}}),l)return;const a=()=>{e.stopPropagation(),e.preventDefault()},s=void 0!==g,{altKey:c,shiftKey:u,metaKey:d,ctrlKey:h,key:p,bounds:f}=e,b=bn.value,y=b?d:h,w="Delete"===p||b&&"Backspace"===p,x=Gn.current,k=hn.columns,S=hn.rows;if("Escape"===p)return void(s?m(void 0):en.clear&&(yn(Or,!1),null==Ye||Ye()));if(Fn("primary+a",e)&&en.selectAll){if(s){const e=document.getElementById(io);if(null!==e){const t=window.getSelection(),n=document.createRange();n.selectNodeContents(e),null==t||t.removeAllRanges(),null==t||t.addRange(n)}}else yn({columns:Ne.empty(),rows:Ne.empty(),current:{cell:null!=(n=null==(t=hn.current)?void 0:t.cell)?n:[rn,0],range:{x:rn,y:0,width:O.length,height:P},rangeStack:[]}},!1);return void a()}function C(e){var t,n,r,o;ar();const i=[];for(let l=e.x;l<e.x+e.width;l++)for(let o=e.y;o<e.y+e.height;o++){const e=D([l-rn,o]);if(!e.allowOverlay&&e.kind!==me.Boolean)continue;let a;if(e.kind===me.Custom){const r=Dn(e),o=null==(t=null==r?void 0:r.provideEditor)?void 0:t.call(r,e);void 0!==(null==r?void 0:r.onDelete)?a=r.onDelete(e):Ae(o)&&(a=null==(n=null==o?void 0:o.deletedValue)?void 0:n.call(o,e))}else if(Le(e)&&e.allowOverlay||e.kind===me.Boolean){const t=Dn(e);a=null==(r=null==t?void 0:t.onDelete)?void 0:r.call(t,e)}void 0!==a&&!ze(a)&&Le(a)&&i.push({location:[l,o],value:a})}cr(i),null==(o=lr.current)||o.damage(i.map((e=>({cell:e.location}))))}if(Fn("primary+f",e)&&en.search&&(a(),null==(r=null==v?void 0:v.current)||r.focus({preventScroll:!0}),sn(!0)),w){const e=null==(o=null==Cn?void 0:Cn(hn))||o;if(a(),!1!==e){const t=!0===e?hn:e;if(void 0!==t.current){C(t.current.range);for(const e of t.current.rangeStack)C(e)}for(const e of t.rows)C({x:rn,y:e,width:Un.length-rn,height:1});for(const e of t.columns)C({x:e,y:0,width:1,height:P})}return}if(void 0===hn.current)return;let[M,E]=hn.current.cell,R=!1;if(en.selectColumn&&Fn("ctrl+ ",e)&&"none"!==ue)k.hasIndex(M)?Rn(k.remove(M),void 0,!0):"single"===ue?Rn(Ne.fromSingleSelection(M),void 0,!0):Rn(void 0,M,!0);else if(en.selectRow&&Fn("shift+ ",e)&&"none"!==he)S.hasIndex(E)?En(S.remove(E),void 0,!0):"single"===he?En(Ne.fromSingleSelection(E),void 0,!0):En(void 0,E,!0);else if((Fn("Enter",e)||Fn(" ",e)||Fn("shift+Enter",e))&&void 0!==f)s?(m(void 0),Fn("Enter",e)?E++:Fn("shift+Enter",e)&&E--):E===P&&on?window.setTimeout((()=>{const e=kr(M);xr(null!=e?e:M)}),0):(null==L||L([M-rn,E]),gr(f,!0),a());else if(en.downFill&&Fn("primary+_68",e)&&hn.current.range.height>1)Br(!1),a();else if(en.rightFill&&Fn("primary+_82",e)&&hn.current.range.width>1){const e=[],t=hn.current.range;for(let n=0;n<t.height;n++){const r=n+t.y,o=hr([t.x,r]);if(!ze(o)&&Fe(o))for(let n=1;n<t.width;n++){const i=[n+t.x,r];e.push({location:i,value:{...o}})}}cr(e),null==(i=lr.current)||i.damage(e.map((e=>({cell:e.location})))),a()}else if(en.pageDown&&Fn("PageDown",e))E+=Math.max(1,Gn.current.height-4),a();else if(en.pageUp&&Fn("PageUp",e))E-=Math.max(1,Gn.current.height-4),a();else if(en.first&&Fn("primary+Home",e))m(void 0),E=0,M=0;else if(en.last&&Fn("primary+End",e))m(void 0),E=Number.MAX_SAFE_INTEGER,M=Number.MAX_SAFE_INTEGER;else if(en.first&&Fn("primary+shift+Home",e))m(void 0),no([-2,-2]);else if(en.last&&Fn("primary+shift+End",e))m(void 0),no([2,2]);else if("ArrowDown"===p){if(h&&c)return;m(void 0),!u||"rect"!==ce&&"multi-rect"!==ce?(c&&!y&&(R=!0),y&&!c?E=P-1:E+=1):no([0,y&&!c?2:1])}else if("ArrowUp"===p||"Home"===p){const e="Home"===p||y;m(void 0),!u||"rect"!==ce&&"multi-rect"!==ce?(c&&!e&&(R=!0),E+=e&&!c?Number.MIN_SAFE_INTEGER:-1):no([0,e&&!c?-2:-1])}else if("ArrowRight"===p||"End"===p){const e="End"===p||y;m(void 0),!u||"rect"!==ce&&"multi-rect"!==ce?(c&&!e&&(R=!0),M+=e&&!c?Number.MAX_SAFE_INTEGER:1):no([e&&!c?2:1,0])}else if("ArrowLeft"===p)m(void 0),!u||"rect"!==ce&&"multi-rect"!==ce?(c&&!y&&(R=!0),M+=y&&!c?Number.MIN_SAFE_INTEGER:-1):no([y&&!c?-2:-1,0]);else if("Tab"===p)m(void 0),u?M--:M++;else if(!d&&!h&&void 0!==hn.current&&1===p.length&&/[ -~]/g.test(p)&&void 0!==f&&Fe(D([M-rn,Math.max(0,E-1)]))){if((!ln||E!==P)&&(x.y>E||E>x.y+x.height||x.x>M||M>x.x+x.width))return;gr(f,!0,p),a()}ro(M,E,!1,R)&&a()})()}),[J,g,hn,en.selectAll,en.search,en.selectColumn,en.selectRow,en.downFill,en.rightFill,en.pageDown,en.pageUp,en.first,en.last,en.clear,ue,he,D,rn,ro,yn,Ye,O.length,P,io,ar,cr,Dn,Cn,Un.length,Rn,En,on,kr,xr,L,gr,Br,hr,no,ce,ln]),ao=o.useCallback(((e,t)=>{const n=e.location[0]-rn;if("header"===e.kind&&(null==j||j(n,{...e,preventDefault:t})),e.kind===Se){if(n<0)return;null==U||U(n,{...e,preventDefault:t})}if("cell"===e.kind){const[r,o]=e.location;null==B||B([n,o],{...e,preventDefault:t}),function(e,t){const[n,r]=t;if(e.columns.hasIndex(n)||e.rows.hasIndex(r))return!0;if(void 0!==e.current){if(e.current.cell[0]===n&&e.current.cell[1]===r)return!0;const t=[e.current.range,...e.current.rangeStack];for(const e of t)if(n>=e.x&&n<e.x+e.width&&r>=e.y&&r<e.y+e.height)return!0}return!1}(hn,e.location)||ro(r,o,!1,!1)}}),[hn,B,U,j,rn,ro]),so=o.useCallback((async e=>{var t,n,r,o,i,l,a;if(!en.paste)return;function s(e,t,n){var r,o;if(!ze(e)&&Fe(e)&&!0!==e.readonly){const i=null==F?void 0:F(n,e);if(void 0!==i&&Le(i))return i.kind!==e.kind&&console.warn("Coercion should not change cell kind."),{location:t,value:i};const l=Dn(e);if(void 0===l)return;if(l.kind===me.Custom){le(e.kind===me.Custom);const o=null==(r=l.onPaste)?void 0:r.call(l,n,e.data);if(void 0===o)return;return{location:t,value:{...e,data:o}}}{const r=null==(o=l.onPaste)?void 0:o.call(l,n,e);if(void 0===r)return;return le(r.kind===e.kind),{location:t,value:r}}}}const c=hn.columns,u=hn.rows,d=!0===(null==(t=x.current)?void 0:t.contains(document.activeElement))||!0===(null==(n=b.current)?void 0:n.contains(document.activeElement));let h=null==(r=hn.current)?void 0:r.cell;if(void 0===h&&1===c.length&&(h=[null!=(o=c.first())?o:0,0]),void 0===h&&1===u.length&&(h=[rn,null!=(i=u.first())?i:0]),d&&void 0!==h){let t,n;const r="text/plain",o="text/html";if(void 0!==navigator.clipboard.read){const e=await navigator.clipboard.read();for(const i of e){if(i.types.includes(o)){const e=await i.getType(o),n=await e.text(),r=document.createElement("html");r.innerHTML=n;const l=r.querySelector("table");if(null!==l){t=Vn(l);break}}i.types.includes(r)&&(n=await(await i.getType(r)).text())}}else if(void 0!==navigator.clipboard.readText)n=await navigator.clipboard.readText();else{if(void 0===e||null===(null==e?void 0:e.clipboardData))return;if(e.clipboardData.types.includes(o)){const n=e.clipboardData.getData(o),r=document.createElement("html");r.innerHTML=n;const i=r.querySelector("table");null!==i&&(t=Vn(i))}void 0===t&&e.clipboardData.types.includes(r)&&(n=e.clipboardData.getData(r))}const[i,c]=h,u=[];do{if(void 0===Ee){const e=s(hr(h),h,null!=(l=null!=n?n:null==t?void 0:t.map((e=>e.join("\t"))).join("\t"))?l:"");void 0!==e&&u.push(e);break}if(void 0===t){if(void 0===n)return;t=_n(n)}if(!1===Ee||"function"===typeof Ee&&!0!==(null==Ee?void 0:Ee([h[0]-rn,h[1]],t)))return;for(const[e,n]of t.entries()){if(e+c>=P)break;for(const[t,r]of n.entries()){const n=[t+i,e+c],o=s(hr(n),n,r);void 0!==o&&u.push(o)}}}while(0);cr(u),null==(a=lr.current)||a.damage(u.map((e=>({cell:e.location}))))}}),[F,Dn,hr,hn,en.paste,cr,Ee,rn,P]);qe("paste",so,window,!1,!0);const co=o.useCallback((async(e,t)=>{var n,r;if(!en.copy)return;const o=!0===t||!0===(null==(n=x.current)?void 0:n.contains(document.activeElement))||!0===(null==(r=b.current)?void 0:r.contains(document.activeElement)),i=hn.columns,l=hn.rows,a=(t,n)=>{if(Re){jn([n.map((e=>({kind:me.Text,data:O[e].title,displayData:O[e].title,allowOverlay:!1}))),...t],n,e)}else jn(t,n,e)};if(o&&void 0!==fn)if(void 0!==hn.current){let e=fn(hn.current.range,pn.current.signal);"object"!==typeof e&&(e=await e()),a(e,s(hn.current.range.x-rn,hn.current.range.x+hn.current.range.width-rn))}else if(void 0!==l&&l.length>0){const e=[...l].map((e=>{const t=fn({x:rn,y:e,width:O.length,height:1},pn.current.signal);return"object"===typeof t?t[0]:t().then((e=>e[0]))}));if(e.some((e=>e instanceof Promise))){a(await Promise.all(e),s(O.length))}else a(e,s(O.length))}else if(i.length>0){const e=[],t=[];for(const n of i){let r=fn({x:n,y:0,width:1,height:P},pn.current.signal);"object"!==typeof r&&(r=await r()),e.push(r),t.push(n-rn)}if(1===e.length)a(e[0],t);else{a(e.reduce(((e,t)=>e.map(((e,n)=>[...e,...t[n]])))),t)}}}),[O,fn,hn,en.copy,rn,P,Re]);qe("copy",co,window,!1,!1);const uo=o.useCallback(((e,t)=>{if(void 0!==$)return 0!==rn&&(e=e.map((e=>[e[0]-rn,e[1]]))),void $(e,t);if(0===e.length||-1===t)return;const[n,r]=e[t];void 0!==k.current&&k.current[0]===n&&k.current[1]===r||(k.current=[n,r],ro(n,r,!1,!1))}),[$,rn,ro]),[ho,po]=null!=(h=null==(d=null==Ge?void 0:Ge.current)?void 0:d.cell)?h:[],fo=o.useRef(vr);fo.current=vr,o.useLayoutEffect((()=>{var e,t,n,r;Qn.current||void 0===ho||void 0===po||ho===(null==(t=null==(e=vn.current)?void 0:e.current)?void 0:t.cell[0])&&po===(null==(r=null==(n=vn.current)?void 0:n.current)?void 0:r.cell[1])||fo.current(ho,po),Qn.current=!1}),[ho,po]);const go=void 0!==hn.current&&(hn.current.cell[0]>=Un.length||hn.current.cell[1]>=sr);o.useLayoutEffect((()=>{go&&yn(Or,!1)}),[go,yn]);const mo=o.useMemo((()=>!0===on&&!0===(null==ot?void 0:ot.tint)?Ne.fromSingleSelection(sr-1):Ne.empty()),[sr,on,null==ot?void 0:ot.tint]),vo=o.useCallback((e=>{var t;return"boolean"===typeof at?at:null==(t=null==at?void 0:at(e-rn))||t}),[rn,at]),bo=o.useMemo((()=>{if(void 0===_r||null===b.current)return null;const{bounds:e,group:t}=_r,n=b.current.getBoundingClientRect();return o.createElement(Hn,{bounds:e,group:t,canvasBounds:n,onClose:()=>Vr(void 0),onFinish:e=>{Vr(void 0),null==X||X(t,e)}})}),[X,_r]),yo=Math.min(Un.length,Te+(nn?1:0));o.useImperativeHandle(t,(()=>({appendRow:(e,t)=>xr(e+rn,t),updateCells:e=>{var t;return 0!==rn&&(e=e.map((e=>({cell:[e.cell[0]+rn,e.cell[1]]})))),null==(t=lr.current)?void 0:t.damage(e)},getBounds:(e,t)=>{var n;return null==(n=lr.current)?void 0:n.getBounds(e+rn,t)},focus:()=>{var e;return null==(e=lr.current)?void 0:e.focus()},emit:async e=>{switch(e){case"delete":lo({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!1,key:"Delete",keyCode:46,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-right":lo({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"r",keyCode:82,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-down":lo({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"d",keyCode:68,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"copy":await co(void 0,!0);break;case"paste":await so()}},scrollTo:vr,remeasureColumns:e=>{for(const t of e)jr(t+rn,!0)}})),[xr,jr,co,lo,so,rn,vr]);const[wo,xo]=null!=$r?$r:[],ko=o.useCallback((e=>{const[t,n]=e;-1!==n?wo===t&&xo===n||(Mn({cell:e,range:{x:t,y:n,width:1,height:1}},!0,!1,"keyboard-nav"),vr(t,n)):"none"!==ue&&(Rn(Ne.fromSingleSelection(t),void 0,!1),ar())}),[ue,ar,vr,wo,xo,Mn,Rn]),[So,Co]=o.useState(!1),Mo=o.useRef(c((e=>{Co(e)}),5)),Eo=o.useCallback((()=>{Mo.current(!0),void 0===hn.current&&0===hn.columns.length&&0===hn.rows.length&&void 0===y&&Mn({cell:[rn,ir],range:{x:rn,y:ir,width:1,height:1}},!0,!1,"keyboard-select")}),[ir,hn,y,rn,Mn]),Ro=o.useCallback((()=>{Mo.current(!1)}),[]),[To,Io]=o.useMemo((()=>{var e;let t;const n=null!=(e=null==yt?void 0:yt.scrollbarWidthOverride)?e:function(){if(void 0!==ht)return ht;const e=document.createElement("p");e.style.width="100%",e.style.height="200px";const t=document.createElement("div");t.id="testScrollbar",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.append(e),document.body.append(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),ht=n-r}(),r=P+(on?1:0);if("number"===typeof Kt)t=Bn+r*Kt;else{let e=0;const n=Math.min(r,10);for(let t=0;t<n;t++)e+=Kt(t);e=Math.floor(e/n),t=Bn+r*e}t+=n;const o=Un.reduce(((e,t)=>t.width+e),0)+n;return["".concat(Math.min(1e5,o),"px"),"".concat(Math.min(1e5,t),"px")]}),[Un,null==yt?void 0:yt.scrollbarWidthOverride,Kt,P,on,Bn]);return o.createElement(fe.Provider,{value:Tn},o.createElement(Xn,{style:de(Tn),className:W,inWidth:null!=T?T:To,inHeight:null!=I?I:Io},o.createElement(Pn,{fillHandle:vt,drawFocusRing:bt,experimental:yt,fixedShadowX:wt,fixedShadowY:xt,getRowThemeOverride:E,headerIcons:kt,imageWindowLoader:St,initialSize:Ct,isDraggable:Mt,onDragLeave:Et,onRowMoved:Rt,overscrollX:Qt,overscrollY:Jt,preventDiagonalScrolling:Ot,rightElement:Pt,rightElementProps:Dt,showMinimap:Ht,smoothScrollX:Lt,smoothScrollY:zt,className:W,enableGroups:Nn,onCanvasFocused:Eo,onCanvasBlur:Ro,canvasRef:b,onContextMenu:ao,theme:Tn,cellXOffset:or,cellYOffset:ir,accessibilityHeight:Jn.height,onDragEnd:eo,columns:Un,drawCustomCell:se,drawHeader:Sn,disabledRows:mo,freezeColumns:yo,lockColumns:rn,firstColAccessible:0===rn,getCellContent:hr,minColumnWidth:jt,maxColumnWidth:Zt,searchInputRef:v,showSearch:cn,onSearchClose:un,highlightRegions:ur,getCellsForSelection:fn,getGroupDetails:pr,headerHeight:$t,isFocused:So,groupHeaderHeight:Nn?Gt:0,trailingRowType:on?!0===(null==ot?void 0:ot.sticky)?"sticky":"appended":"none",onColumnResize:wn,onColumnResizeEnd:xn,onColumnResizeStart:kn,onCellFocused:ko,onColumnMoved:qr,onDragStart:Jr,onHeaderMenuClick:Kr,onItemHovered:to,isFilling:!0===(null==y?void 0:y.fillHandle),onMouseMove:Yr,onKeyDown:lo,onKeyUp:ee,onMouseDown:Ar,onMouseUp:Xr,onDragOverCell:st,onDrop:ct,onSearchResultsChanged:uo,onVisibleRegionChanged:Gr,clientSize:[In[0],In[1]],rowHeight:Kt,searchResults:G,searchValue:Q,onSearchValueChange:q,rows:sr,scrollRef:x,selection:hn,translateX:Jn.tx,translateY:Jn.ty,verticalBorder:vo,gridRef:lr,getCellRenderer:Dn,scrollToEnd:Ft}),bo,void 0!==g&&o.createElement(je,{...g,validateCell:mn,id:io,getCellRenderer:Dn,className:!0===(null==yt?void 0:yt.isSubGrid)?"click-outside-ignore":void 0,provideEditor:rt,imageEditorOverride:M,onFinishEditing:oo,markdownDivCreateNode:R,isOutsideClick:Wt})))}));function Dr(e){return{...e,kind:me.Custom}}function Hr(e){return{customRenderers:o.useMemo((()=>e.map(Dr)),[e])}}},46979:(e,t,n)=>{"use strict";n.r(t),n.d(t,{FileSystemDirectoryHandle:()=>a,default:()=>s});var r=n(64649),o=n(93232);let i;const l=Symbol("adapter");i=Symbol.asyncIterator;class a extends o.Z{constructor(e){super(e),(0,r.Z)(this,l,void 0),this[l]=e}async getDirectoryHandle(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(""===e)throw new TypeError("Name can't be an empty string.");if("."===e||".."===e||e.includes("/"))throw new TypeError("Name contains invalid characters.");t.create=!!t.create;const n=await this[l].getDirectoryHandle(e,t);return new a(n)}async*entries(){const{FileSystemFileHandle:e}=await Promise.resolve().then(n.bind(n,8192));for await(const[t,n]of this[l].entries())yield[n.name,"file"===n.kind?new e(n):new a(n)]}async*getEntries(){const{FileSystemFileHandle:e}=await Promise.resolve().then(n.bind(n,8192));console.warn("deprecated, use .entries() instead");for await(let t of this[l].entries())yield"file"===t.kind?new e(t):new a(t)}async getFileHandle(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{FileSystemFileHandle:r}=await Promise.resolve().then(n.bind(n,8192));if(""===e)throw new TypeError("Name can't be an empty string.");if("."===e||".."===e||e.includes("/"))throw new TypeError("Name contains invalid characters.");t.create=!!t.create;return new r(await this[l].getFileHandle(e,t))}async removeEntry(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(""===e)throw new TypeError("Name can't be an empty string.");if("."===e||".."===e||e.includes("/"))throw new TypeError("Name contains invalid characters.");return t.recursive=!!t.recursive,this[l].removeEntry(e,t)}async resolve(e){if(await e.isSameEntry(this))return[];const t=[{handle:this,path:[]}];for(;t.length;){let{handle:n,path:r}=t.pop();for await(const o of n.values()){if(await o.isSameEntry(e))return[...r,o.name];"directory"===o.kind&&t.push({handle:o,path:[...r,o.name]})}}return null}async*keys(){for await(const[e]of this[l].entries())yield e}async*values(){for await(const[e,t]of this)yield t}[i](){return this.entries()}}Object.defineProperty(a.prototype,Symbol.toStringTag,{value:"FileSystemDirectoryHandle",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(a.prototype,{getDirectoryHandle:{enumerable:!0},entries:{enumerable:!0},getFileHandle:{enumerable:!0},removeEntry:{enumerable:!0}});const s=a},8192:(e,t,n)=>{"use strict";n.r(t),n.d(t,{FileSystemFileHandle:()=>a,default:()=>s});var r=n(64649),o=n(93232),i=n(82572);const l=Symbol("adapter");class a extends o.Z{constructor(e){super(e),(0,r.Z)(this,l,void 0),this[l]=e}async createWritable(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new i.Z(await this[l].createWritable(e))}async getFile(){return this[l].getFile()}}Object.defineProperty(a.prototype,Symbol.toStringTag,{value:"FileSystemFileHandle",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(a.prototype,{createWritable:{enumerable:!0},getFile:{enumerable:!0}});const s=a},93232:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(64649);const o=Symbol("adapter");class i{constructor(e){(0,r.Z)(this,o,void 0),(0,r.Z)(this,"name",void 0),(0,r.Z)(this,"kind",void 0),this.kind=e.kind,this.name=e.name,this[o]=e}async queryPermission(){let{mode:e="read"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=this[o];if(t.queryPermission)return t.queryPermission({mode:e});if("read"===e)return"granted";if("readwrite"===e)return t.writable?"granted":"denied";throw new TypeError("Mode ".concat(e," must be 'read' or 'readwrite'"))}async requestPermission(){let{mode:e="read"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=this[o];if(t.requestPermission)return t.requestPermission({mode:e});if("read"===e)return"granted";if("readwrite"===e)return t.writable?"granted":"denied";throw new TypeError("Mode ".concat(e," must be 'read' or 'readwrite'"))}async remove(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};await this[o].remove(e)}async isSameEntry(e){return this===e||!(!e||"object"!==typeof e||this.kind!==e.kind||!e[o])&&this[o].isSameEntry(e[o])}}Object.defineProperty(i.prototype,Symbol.toStringTag,{value:"FileSystemHandle",writable:!1,enumerable:!1,configurable:!0});const l=i},82572:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(60643);const{WritableStream:o}=r.Z;class i extends o{constructor(){super(...arguments),Object.setPrototypeOf(this,i.prototype),this._closed=!1}close(){this._closed=!0;const e=this.getWriter(),t=e.close();return e.releaseLock(),t}seek(e){return this.write({type:"seek",position:e})}truncate(e){return this.write({type:"truncate",size:e})}write(e){if(this._closed)return Promise.reject(new TypeError("Cannot write to a CLOSED writable stream"));const t=this.getWriter(),n=t.write(e);return t.releaseLock(),n}}Object.defineProperty(i.prototype,Symbol.toStringTag,{value:"FileSystemWritableFileStream",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(i.prototype,{close:{enumerable:!0},seek:{enumerable:!0},truncate:{enumerable:!0},write:{enumerable:!0}});const l=i},60643:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r={ReadableStream:globalThis.ReadableStream,WritableStream:globalThis.WritableStream,TransformStream:globalThis.TransformStream,DOMException:globalThis.DOMException,Blob:globalThis.Blob,File:globalThis.File}},95345:(e,t,n)=>{"use strict";n.d(t,{Kr:()=>o});globalThis.showDirectoryPicker;globalThis.showOpenFilePicker;const r=globalThis.showSaveFilePicker;const o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(r&&!e._preferPolyfill)return r(e);e._name&&(console.warn("deprecated _name, spec now have `suggestedName`"),e.suggestedName=e._name);const{FileSystemFileHandle:t}=await Promise.resolve().then(n.bind(n,8192)),{FileHandle:o}=await n.e(474).then(n.bind(n,60474));return new t(new o(e.suggestedName))};globalThis.DataTransferItem&&!DataTransferItem.prototype.getAsFileSystemHandle&&(DataTransferItem.prototype.getAsFileSystemHandle=async function(){const e=this.webkitGetAsEntry(),[{FileHandle:t,FolderHandle:r},{FileSystemDirectoryHandle:o},{FileSystemFileHandle:i}]=await Promise.all([n.e(3631).then(n.bind(n,13053)),Promise.resolve().then(n.bind(n,46979)),Promise.resolve().then(n.bind(n,8192))]);return e.isFile?new i(new t(e,!1)):new o(new r(e,!1))});n(46979),n(8192),n(93232),n(82572)}}]);