"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[4500],{54500:(t,e,s)=>{s.r(e),s.d(e,{default:()=>r});var l=s(90994),i=(s(66845),s(40864));function r(t){let{element:e,width:s}=t;const r=e.hasWidth?e.width:s;let a,o;e.scrolling?(a="auto",o={}):(a="no",o={overflow:"hidden"});const c=n(e.src),d=null!=c?void 0:n(e.srcdoc);return(0,i.jsx)("iframe",{"data-testid":"stIFrame",allow:l.p,style:o,src:c,srcDoc:d,width:r,height:e.height,scrolling:a,sandbox:l.T,title:"st.iframe"})}function n(t){return null==t||""===t?void 0:t}}}]);