"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[8005],{28005:(e,i,t)=>{t.r(i),t.d(i,{FileHandle:()=>_,FolderHandle:()=>y,Sink:()=>p,default:()=>m});var s=t(69758),n=t(60643);const{File:r,Blob:o,DOMException:h}=n.Z,{INVALID:l,GONE:a,MISMATCH:w,MOD_ERR:f,SYNTAX:d,SECURITY:c,DISALLOWED:u}=s.errors;class p{constructor(e,i){this.fileHandle=e,this.file=i,this.size=i.size,this.position=0}write(e){let i=this.file;if("object"===typeof e)if("write"===e.type){if(Number.isInteger(e.position)&&e.position>=0&&(this.position=e.position,this.size<e.position&&(this.file=new r([this.file,new ArrayBuffer(e.position-this.size)],this.file.name,this.file))),!("data"in e))throw new h(...d("write requires a data argument"));e=e.data}else{if("seek"===e.type){if(Number.isInteger(e.position)&&e.position>=0){if(this.size<e.position)throw new h(...l);return void(this.position=e.position)}throw new h(...d("seek requires a position argument"))}if("truncate"===e.type){if(Number.isInteger(e.size)&&e.size>=0)return i=e.size<this.size?new r([i.slice(0,e.size)],i.name,i):new r([i,new Uint8Array(e.size-this.size)],i.name),this.size=i.size,this.position>i.size&&(this.position=i.size),void(this.file=i);throw new h(...d("truncate requires a size argument"))}}e=new o([e]);let t=this.file;const s=t.slice(0,this.position),n=t.slice(this.position+e.size);let a=this.position-s.size;a<0&&(a=0),t=new r([s,new Uint8Array(a),e,n],t.name),this.size=t.size,this.position+=e.size,this.file=t}close(){if(this.fileHandle._deleted)throw new h(...a);this.fileHandle._file=this.file,this.file=this.position=this.size=null,this.fileHandle.onclose&&this.fileHandle.onclose(this.fileHandle)}}class _{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new r([],e),t=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];this._file=i,this.name=e,this.kind="file",this._deleted=!1,this.writable=t,this.readable=!0}async getFile(){if(this._deleted)throw new h(...a);return this._file}async createWritable(e){if(!this.writable)throw new h(...u);if(this._deleted)throw new h(...a);const i=e.keepExistingData?await this.getFile():new r([],this.name);return new p(this,i)}async isSameEntry(e){return this===e}async _destroy(){this._deleted=!0,this._file=null}}class y{constructor(e){let i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.name=e,this.kind="directory",this._deleted=!1,this._entries={},this.writable=i,this.readable=!0}async*entries(){if(this._deleted)throw new h(...a);yield*Object.entries(this._entries)}async isSameEntry(e){return this===e}async getDirectoryHandle(e,i){if(this._deleted)throw new h(...a);const t=this._entries[e];if(t){if(t instanceof _)throw new h(...w);return t}if(i.create)return this._entries[e]=new y(e);throw new h(...a)}async getFileHandle(e,i){const t=this._entries[e],s=t instanceof _;if(t&&s)return t;if(t&&!s)throw new h(...w);if(!t&&!i.create)throw new h(...a);return!t&&i.create?this._entries[e]=new _(e):void 0}async removeEntry(e,i){const t=this._entries[e];if(!t)throw new h(...a);await t._destroy(i.recursive),delete this._entries[e]}async _destroy(e){for(let i of Object.values(this._entries)){if(!e)throw new h(...f);await i._destroy(e)}this._entries={},this._deleted=!0}}const z=new y(""),m=()=>z}}]);