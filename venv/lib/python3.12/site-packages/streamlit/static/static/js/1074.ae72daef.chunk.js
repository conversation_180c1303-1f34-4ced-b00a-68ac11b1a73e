(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[1074],{87814:(e,t,r)=>{"use strict";r.d(t,{K:()=>i});var s=r(50641);class i{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,r){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,s.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,r),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}},91074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(66845),i=r(91706),o=r(81810),n=r.n(o),l=r(25621),a=r(87814),d=r(90481),p=r(91034),u=r(12576),h=r(98478),c=r(86659);const m=(0,r(1515).Z)("div",{target:"e6zijwc0"})((e=>{let{theme:t}=e;return{"span[aria-disabled='true']":{background:t.colors.fadedText05}}}),"");var g=r(8879),v=r(68411),f=r(26628),b=r(97965),x=r(50641),S=r(40864);class y extends s.PureComponent{constructor(){super(...arguments),this.formClearHelper=new a.K,this.state={value:this.initialValue},this.commitWidgetValue=e=>{this.props.widgetMgr.setIntArrayValue(this.props.element,this.state.value,e)},this.onFormCleared=()=>{this.setState(((e,t)=>({value:t.element.default})),(()=>this.commitWidgetValue({fromUi:!0})))},this.onChange=e=>{this.props.element.maxSelections&&"select"===e.type&&this.state.value.length>=this.props.element.maxSelections||this.setState(this.generateNewState(e),(()=>{this.commitWidgetValue({fromUi:!0})}))},this.filterOptions=(e,t)=>{if(this.overMaxSelections())return[];const r=e.filter((e=>!this.state.value.includes(Number(e.value))));return(0,b.HX)(r,t)}}overMaxSelections(){return this.props.element.maxSelections>0&&this.state.value.length>=this.props.element.maxSelections}getNoResultsMsg(){const{maxSelections:e}=this.props.element,{value:t}=this.state;if(0===e)return"No results";if(t.length===e){const t=1!==e?"options":"option";return"You can only select up to ".concat(e," ").concat(t,". Remove an option first.")}return"No results"}get initialValue(){const e=this.props.widgetMgr.getIntArrayValue(this.props.element);return void 0!==e?e:this.props.element.default}componentDidMount(){this.props.element.setValue?this.updateFromProtobuf():this.commitWidgetValue({fromUi:!1})}componentDidUpdate(){this.maybeUpdateFromProtobuf()}componentWillUnmount(){this.formClearHelper.disconnect()}maybeUpdateFromProtobuf(){const{setValue:e}=this.props.element;e&&this.updateFromProtobuf()}updateFromProtobuf(){const{value:e}=this.props.element;this.props.element.setValue=!1,this.setState({value:e},(()=>{this.commitWidgetValue({fromUi:!1})}))}get valueFromState(){return this.state.value.map((e=>{const t=this.props.element.options[e];return{value:e.toString(),label:t}}))}generateNewState(e){const t=()=>{var t;const r=null===(t=e.option)||void 0===t?void 0:t.value;return parseInt(r,10)};switch(e.type){case"remove":return{value:n()(this.state.value,t())};case"clear":return{value:[]};case"select":return{value:this.state.value.concat([t()])};default:throw new Error("State transition is unknown: ".concat(e.type))}}render(){var e;const{element:t,theme:r,width:s,widgetMgr:o}=this.props,n={width:s},{options:l}=t,a=0===l.length||this.props.disabled,b=0===l.length?"No options to select.":t.placeholder,y=l.map(((e,t)=>({label:e,value:t.toString()})));this.formClearHelper.manageFormClearListener(o,t.formId,this.onFormCleared);const w=l.length>10;return(0,S.jsxs)("div",{className:"row-widget stMultiSelect","data-testid":"stMultiSelect",style:n,children:[(0,S.jsx)(h.O,{label:t.label,disabled:a,labelVisibility:(0,x.iF)(null===(e=t.labelVisibility)||void 0===e?void 0:e.value),children:t.help&&(0,S.jsx)(c.dT,{children:(0,S.jsx)(g.Z,{content:t.help,placement:v.u.TOP_RIGHT})})}),(0,S.jsx)(m,{children:(0,S.jsx)(p.Z,{options:y,labelKey:"label",valueKey:"value","aria-label":t.label,placeholder:b,type:u.wD.select,multi:!0,onChange:this.onChange,value:this.valueFromState,disabled:a,size:"compact",noResultsMsg:this.getNoResultsMsg(),filterOptions:this.filterOptions,closeOnSelect:!1,overrides:{SelectArrow:{component:d.Z,props:{overrides:{Svg:{style:()=>({width:r.iconSizes.xl,height:r.iconSizes.xl})}}}},IconsContainer:{style:()=>({paddingRight:r.spacing.sm})},ControlContainer:{style:{borderLeftWidth:"1px",borderRightWidth:"1px",borderTopWidth:"1px",borderBottomWidth:"1px"}},Placeholder:{style:()=>({flex:"inherit"})},ValueContainer:{style:()=>({minHeight:"38.4px",paddingLeft:r.spacing.sm,paddingTop:0,paddingBottom:0,paddingRight:0})},ClearIcon:{props:{overrides:{Svg:{style:{color:r.colors.darkGray,transform:"scale(1.5)",width:r.spacing.twoXL,":hover":{fill:r.colors.bodyText}}}}}},SearchIcon:{style:{color:r.colors.darkGray}},Tag:{props:{overrides:{Root:{style:{borderTopLeftRadius:r.radii.md,borderTopRightRadius:r.radii.md,borderBottomRightRadius:r.radii.md,borderBottomLeftRadius:r.radii.md,fontSize:r.fontSizes.sm,paddingLeft:r.spacing.sm,marginLeft:0,marginRight:r.spacing.sm,height:"28px"}},Action:{style:{paddingLeft:0}},ActionIcon:{props:{overrides:{Svg:{style:{width:"10px",height:"10px"}}}}},Text:{style:{fontSize:r.fontSizes.md}}}}},MultiValue:{props:{overrides:{Root:{style:{fontSize:r.fontSizes.sm}}}}},Input:{props:{readOnly:i.tq&&!1===w?"readonly":null}},Dropdown:{component:f.Z}}})})]})}}const w=(0,l.b)(y)},10636:(e,t,r)=>{var s=r(74791);e.exports=function(e,t){return!!(null==e?0:e.length)&&s(e,t,0)>-1}},19774:e=>{e.exports=function(e,t,r){for(var s=-1,i=null==e?0:e.length;++s<i;)if(r(t,e[s]))return!0;return!1}},80514:(e,t,r)=>{var s=r(89632),i=r(10636),o=r(19774),n=r(53114),l=r(57291),a=r(7086);e.exports=function(e,t,r,d){var p=-1,u=i,h=!0,c=e.length,m=[],g=t.length;if(!c)return m;r&&(t=n(t,l(r))),d?(u=o,h=!1):t.length>=200&&(u=a,h=!1,t=new s(t));e:for(;++p<c;){var v=e[p],f=null==r?v:r(v);if(v=d||0!==v?v:0,h&&f===f){for(var b=g;b--;)if(t[b]===f)continue e;m.push(v)}else u(t,f,d)||m.push(v)}return m}},18218:e=>{e.exports=function(e,t,r,s){for(var i=e.length,o=r+(s?1:-1);s?o--:++o<i;)if(t(e[o],o,e))return o;return-1}},74791:(e,t,r)=>{var s=r(18218),i=r(18238),o=r(68603);e.exports=function(e,t,r){return t===t?o(e,t,r):s(e,i,r)}},18238:e=>{e.exports=function(e){return e!==e}},68603:e=>{e.exports=function(e,t,r){for(var s=r-1,i=e.length;++s<i;)if(e[s]===t)return s;return-1}},81810:(e,t,r)=>{var s=r(80514),i=r(26724),o=r(67051),n=i((function(e,t){return o(e)?s(e,t):[]}));e.exports=n}}]);