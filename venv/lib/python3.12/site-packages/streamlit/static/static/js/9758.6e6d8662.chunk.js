"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[9758],{69758:(e,t,n)=>{n.r(t),n.d(t,{config:()=>a,errors:()=>r,fromDataTransfer:()=>i,getDirHandlesFromInput:()=>o,getFileHandlesFromInput:()=>s});const r={INVALID:["seeking position failed.","InvalidStateError"],GONE:["A requested file or directory could not be found at the time an operation was processed.","NotFoundError"],MISMATCH:["The path supplied exists, but was not an entry of requested type.","TypeMismatchError"],MOD_ERR:["The object can not be modified in this way.","InvalidModificationError"],SYNTAX:e=>["Failed to execute 'write' on 'UnderlyingSinkBase': Invalid params passed. ".concat(e),"SyntaxError"],SECURITY:["It was determined that certain files are unsafe for access within a Web application, or that too many calls are being made on file resources.","SecurityError"],DISALLOWED:["The request is not allowed by the user agent or the platform in the current context.","NotAllowedError"]},a={writable:globalThis.WritableStream};async function i(e){console.warn("deprecated fromDataTransfer - use `dt.items[0].getAsFileSystemHandle()` instead");const[t,r,a]=await Promise.all([n.e(8005).then(n.bind(n,28005)),n.e(3053).then(n.bind(n,13053)),Promise.resolve().then(n.bind(n,46979))]),i=new t.FolderHandle("",!1);return i._entries=e.map((e=>e.isFile?new r.FileHandle(e,!1):new r.FolderHandle(e,!1))),new a.FileSystemDirectoryHandle(i)}async function o(e){const{FolderHandle:t,FileHandle:r}=await n.e(8005).then(n.bind(n,28005)),{FileSystemDirectoryHandle:a}=await Promise.resolve().then(n.bind(n,46979)),i=Array.from(e.files),o=i[0].webkitRelativePath.split("/",1)[0],s=new t(o,!1);return i.forEach((e=>{const n=e.webkitRelativePath.split("/");n.shift();const a=n.pop();n.reduce(((e,n)=>(e._entries[n]||(e._entries[n]=new t(n,!1)),e._entries[n])),s)._entries[a]=new r(e.name,e,!1)})),new a(s)}async function s(e){const{FileHandle:t}=await n.e(8005).then(n.bind(n,28005)),{FileSystemFileHandle:r}=await Promise.resolve().then(n.bind(n,8192));return Array.from(e.files).map((e=>new r(new t(e.name,e,!1))))}}}]);