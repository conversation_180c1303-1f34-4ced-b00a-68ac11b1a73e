"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.Common_pb2
import streamlit.proto.Components_pb2
import sys

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class WidgetStates(google.protobuf.message.Message):
    """State for every widget in an app."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WIDGETS_FIELD_NUMBER: builtins.int
    @property
    def widgets(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___WidgetState]: ...
    def __init__(
        self,
        *,
        widgets: collections.abc.Iterable[global___WidgetState] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["widgets", b"widgets"]) -> None: ...

global___WidgetStates = WidgetStates

class WidgetState(google.protobuf.message.Message):
    """State for a single widget."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    TRIGGER_VALUE_FIELD_NUMBER: builtins.int
    BOOL_VALUE_FIELD_NUMBER: builtins.int
    DOUBLE_VALUE_FIELD_NUMBER: builtins.int
    INT_VALUE_FIELD_NUMBER: builtins.int
    STRING_VALUE_FIELD_NUMBER: builtins.int
    DOUBLE_ARRAY_VALUE_FIELD_NUMBER: builtins.int
    INT_ARRAY_VALUE_FIELD_NUMBER: builtins.int
    STRING_ARRAY_VALUE_FIELD_NUMBER: builtins.int
    JSON_VALUE_FIELD_NUMBER: builtins.int
    ARROW_VALUE_FIELD_NUMBER: builtins.int
    BYTES_VALUE_FIELD_NUMBER: builtins.int
    FILE_UPLOADER_STATE_VALUE_FIELD_NUMBER: builtins.int
    STRING_TRIGGER_VALUE_FIELD_NUMBER: builtins.int
    id: builtins.str
    trigger_value: builtins.bool
    """trigger_value is for buttons. A button's value needs to
    auto-revert back to False after the script has been run with
    the value set to True. After running the script, the server
    will reset all trigger_values in its stored Widget state dict.
    (Use bool_value for widgets like checkboxes, whose state persists
    beyond a single script run.)
    """
    bool_value: builtins.bool
    double_value: builtins.float
    int_value: builtins.int
    string_value: builtins.str
    @property
    def double_array_value(self) -> streamlit.proto.Common_pb2.DoubleArray: ...
    @property
    def int_array_value(self) -> streamlit.proto.Common_pb2.SInt64Array: ...
    @property
    def string_array_value(self) -> streamlit.proto.Common_pb2.StringArray: ...
    json_value: builtins.str
    @property
    def arrow_value(self) -> streamlit.proto.Components_pb2.ArrowTable: ...
    bytes_value: builtins.bytes
    @property
    def file_uploader_state_value(self) -> streamlit.proto.Common_pb2.FileUploaderState: ...
    @property
    def string_trigger_value(self) -> streamlit.proto.Common_pb2.StringTriggerValue:
        """String value that resets itself to empty after the script has been run.
        This is used for the chat_input widget.
        """
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        trigger_value: builtins.bool = ...,
        bool_value: builtins.bool = ...,
        double_value: builtins.float = ...,
        int_value: builtins.int = ...,
        string_value: builtins.str = ...,
        double_array_value: streamlit.proto.Common_pb2.DoubleArray | None = ...,
        int_array_value: streamlit.proto.Common_pb2.SInt64Array | None = ...,
        string_array_value: streamlit.proto.Common_pb2.StringArray | None = ...,
        json_value: builtins.str = ...,
        arrow_value: streamlit.proto.Components_pb2.ArrowTable | None = ...,
        bytes_value: builtins.bytes = ...,
        file_uploader_state_value: streamlit.proto.Common_pb2.FileUploaderState | None = ...,
        string_trigger_value: streamlit.proto.Common_pb2.StringTriggerValue | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["arrow_value", b"arrow_value", "bool_value", b"bool_value", "bytes_value", b"bytes_value", "double_array_value", b"double_array_value", "double_value", b"double_value", "file_uploader_state_value", b"file_uploader_state_value", "int_array_value", b"int_array_value", "int_value", b"int_value", "json_value", b"json_value", "string_array_value", b"string_array_value", "string_trigger_value", b"string_trigger_value", "string_value", b"string_value", "trigger_value", b"trigger_value", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["arrow_value", b"arrow_value", "bool_value", b"bool_value", "bytes_value", b"bytes_value", "double_array_value", b"double_array_value", "double_value", b"double_value", "file_uploader_state_value", b"file_uploader_state_value", "id", b"id", "int_array_value", b"int_array_value", "int_value", b"int_value", "json_value", b"json_value", "string_array_value", b"string_array_value", "string_trigger_value", b"string_trigger_value", "string_value", b"string_value", "trigger_value", b"trigger_value", "value", b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["value", b"value"]) -> typing_extensions.Literal["trigger_value", "bool_value", "double_value", "int_value", "string_value", "double_array_value", "int_array_value", "string_array_value", "json_value", "arrow_value", "bytes_value", "file_uploader_state_value", "string_trigger_value"] | None: ...

global___WidgetState = WidgetState
