"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class Video(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Type:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _TypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Video._Type.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNUSED: Video._Type.ValueType  # 0
        """This should always exist."""
        NATIVE: Video._Type.ValueType  # 1
        YOUTUBE_IFRAME: Video._Type.ValueType  # 2

    class Type(_Type, metaclass=_TypeEnumTypeWrapper): ...
    UNUSED: Video.Type.ValueType  # 0
    """This should always exist."""
    NATIVE: Video.Type.ValueType  # 1
    YOUTUBE_IFRAME: Video.Type.ValueType  # 2

    URL_FIELD_NUMBER: builtins.int
    START_TIME_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    url: builtins.str
    """A url pointing to a video file"""
    start_time: builtins.int
    """The currentTime attribute of the HTML <video> tag's <source> subtag."""
    type: global___Video.Type.ValueType
    """Type affects how browser wraps the video in tags: plain HTML5, YouTube..."""
    def __init__(
        self,
        *,
        url: builtins.str = ...,
        start_time: builtins.int = ...,
        type: global___Video.Type.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["start_time", b"start_time", "type", b"type", "url", b"url"]) -> None: ...

global___Video = Video
