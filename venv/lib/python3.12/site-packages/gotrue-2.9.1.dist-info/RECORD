gotrue-2.9.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gotrue-2.9.1.dist-info/LICENSE,sha256=M03Wgg4urqsgZOfFkAG4EFZnKKKKQafB2_abvuF9CTY,1065
gotrue-2.9.1.dist-info/METADATA,sha256=nG6Cr-x7BRoLcHsF8hFmuUVWEJ1KMhFJ_GNwFYJyduE,6000
gotrue-2.9.1.dist-info/RECORD,,
gotrue-2.9.1.dist-info/WHEEL,sha256=sP946D7jFCHeNz5Iq4fL4Lu-PrWrFsgfLXbbkciIZwg,88
gotrue/__init__.py,sha256=NiC2cr94N4Q_k9pZ1_P5iy0P162T1hmQfhf5VESRgiQ,761
gotrue/__pycache__/__init__.cpython-312.pyc,,
gotrue/__pycache__/constants.cpython-312.pyc,,
gotrue/__pycache__/errors.cpython-312.pyc,,
gotrue/__pycache__/helpers.cpython-312.pyc,,
gotrue/__pycache__/http_clients.cpython-312.pyc,,
gotrue/__pycache__/timer.cpython-312.pyc,,
gotrue/__pycache__/types.cpython-312.pyc,,
gotrue/__pycache__/version.cpython-312.pyc,,
gotrue/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
gotrue/_async/__pycache__/__init__.cpython-312.pyc,,
gotrue/_async/__pycache__/gotrue_admin_api.cpython-312.pyc,,
gotrue/_async/__pycache__/gotrue_admin_mfa_api.cpython-312.pyc,,
gotrue/_async/__pycache__/gotrue_base_api.cpython-312.pyc,,
gotrue/_async/__pycache__/gotrue_client.cpython-312.pyc,,
gotrue/_async/__pycache__/gotrue_mfa_api.cpython-312.pyc,,
gotrue/_async/__pycache__/storage.cpython-312.pyc,,
gotrue/_async/gotrue_admin_api.py,sha256=Ngkiln1Z7ChEVJlV6QD7JH320lJ3pJl8AQC5UY0FH4c,5751
gotrue/_async/gotrue_admin_mfa_api.py,sha256=Lcf-QQSjm6e-8xBGghNfF4MJB_KFsssJ6DcJCETp76U,947
gotrue/_async/gotrue_base_api.py,sha256=lrlvOVXi4avnWpoX-LMT-CrBNEe5mBvre711cmnI1LE,4112
gotrue/_async/gotrue_client.py,sha256=4ah31mYShlBhmq3sj1oyA2O-7ILBSlTRqhv8tgJ9wgo,41823
gotrue/_async/gotrue_mfa_api.py,sha256=jbYrec9ynCTi2Xu1UfNHDHBrNJi4rOnURG9pLXurRQM,3956
gotrue/_async/storage.py,sha256=DdzM-b7Kn9aBS0MSYiCUTop9foqWspW7BWicSrJbWuM,901
gotrue/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
gotrue/_sync/__pycache__/__init__.cpython-312.pyc,,
gotrue/_sync/__pycache__/api.cpython-312.pyc,,
gotrue/_sync/__pycache__/client.cpython-312.pyc,,
gotrue/_sync/__pycache__/gotrue_admin_api.cpython-312.pyc,,
gotrue/_sync/__pycache__/gotrue_admin_mfa_api.cpython-312.pyc,,
gotrue/_sync/__pycache__/gotrue_base_api.cpython-312.pyc,,
gotrue/_sync/__pycache__/gotrue_client.cpython-312.pyc,,
gotrue/_sync/__pycache__/gotrue_mfa_api.cpython-312.pyc,,
gotrue/_sync/__pycache__/storage.cpython-312.pyc,,
gotrue/_sync/api.py,sha256=7gEZ-QBPOqSnlE63PTG9_TWp03Ubm2ZI-B75z3uLELQ,19432
gotrue/_sync/client.py,sha256=BdDcKYdY_6tO1e6kqd1JtexvyxRWYO-Y7rTb-Ip-hmQ,22506
gotrue/_sync/gotrue_admin_api.py,sha256=sOUOUJfh5XUYGsAzOA0rbrxTUeX1nC0IMO5217oe-ks,5623
gotrue/_sync/gotrue_admin_mfa_api.py,sha256=X4Xj7A3V7SZh4FfQ2-psbEbhXSocuZQtHzYiWmcdUZg,934
gotrue/_sync/gotrue_base_api.py,sha256=CkCBLrW7gXVBMa-YK8v4erbIHL8yavCjlT2FdU3kg9Y,4046
gotrue/_sync/gotrue_client.py,sha256=z71V0o4eOpO9gNR_w5mGOc-72yYLcNLj2a2Rv32sc38,40933
gotrue/_sync/gotrue_mfa_api.py,sha256=gr8EznSMAfuoCE_W9O784w8HvUVa-1h4NalpT5QEpbQ,3913
gotrue/_sync/storage.py,sha256=LxcDdE6978GZH2X5XKN25WT5vHnRFDQpsMXUximF1Lo,862
gotrue/constants.py,sha256=vs6rCG7CJ1I7jm1f-ntjvfdMOL5C6FAVqV3ND77Jdhg,580
gotrue/errors.py,sha256=28j2yu5VN2US9R4t9dwqkfxLVwB984l_9eifmQ6pJOc,5504
gotrue/helpers.py,sha256=nnc5h8dV39P_-dizDRjmgeWJxhJ9A8lzNrRxatzb4VQ,7554
gotrue/http_clients.py,sha256=C_tK5x2iKsz-QFf73QuEWV8X0ghwJCtrIJTeTPDS2fI,202
gotrue/timer.py,sha256=jfyQVkrWiih2juHcnxHgbM38hdmH0MIOocPINjyO0dA,1366
gotrue/types.py,sha256=xVcdBJ2cfAiuWzfZamGG3iz_ZQ1oWci7SZAMeQg_dQg,20070
gotrue/version.py,sha256=lGE1643qT0wb4hC8z8UM-c3b7UDPiSdV0qCa0MTgUBI,52
